import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:wiggyz_app/features/notifications/providers/notification_provider.dart';
import 'package:wiggyz_app/features/notifications/widgets/notification_badge.dart';
import 'package:wiggyz_app/screens/home_screen.dart';
import 'package:wiggyz_app/screens/games_screen_new_fixed.dart';
import 'package:wiggyz_app/screens/profile_screen.dart';

import 'package:wiggyz_app/screens/wallet_screen.dart';
import 'package:wiggyz_app/screens/rewards_screen.dart';
import 'package:wiggyz_app/providers/reward_provider.dart';
import 'package:go_router/go_router.dart';

class MainNavigation extends StatefulWidget {
  final int initialIndex;

  const MainNavigation({super.key, this.initialIndex = 0});

  @override
  State<MainNavigation> createState() => _MainNavigationState();

  // Static reference to the current navigation state
  static _MainNavigationState? of(BuildContext context) {
    final navigatorState =
        context.findAncestorStateOfType<_MainNavigationState>();
    return navigatorState;
  }

  // Method to navigate to a specific tab from anywhere in the app
  static void navigateToTab(BuildContext context, int index) {
    final navigatorState = of(context);
    if (navigatorState != null) {
      navigatorState._onItemTapped(index);
    }
  }
}

class _MainNavigationState extends State<MainNavigation> {
  late int _selectedIndex;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final uri = GoRouter.of(context).routeInformationProvider.value.uri;
    final tab = uri.queryParameters['tab'];
    if (tab == 'games') {
      _selectedIndex = 1;
    } else if (tab == 'rewards') {
      _selectedIndex = 2;
    } else if (tab == 'wallet') {
      _selectedIndex = 3;
    } else if (tab == 'profile') {
      _selectedIndex = 4;
    } else {
      _selectedIndex = widget.initialIndex;
    }
  }

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialIndex;
  }

  final List<Widget> _screens = const [
    HomeScreen(),
    GamesScreen(),
    RewardsScreen(), // Rewards screen
    WalletScreen(), // Wallet screen
    ProfileScreen(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _selectedIndex,
          onTap: _onItemTapped,
          type: BottomNavigationBarType.fixed,
          backgroundColor:
              Theme.of(context).bottomNavigationBarTheme.backgroundColor,
          selectedItemColor: const Color(0xFFFFCC00), // Bright golden color
          unselectedItemColor: Colors.grey,
          selectedLabelStyle: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          items: [
            const BottomNavigationBarItem(
              icon: Icon(Icons.home_outlined),
              activeIcon: Icon(Icons.home),
              label: 'Home',
            ),
            const BottomNavigationBarItem(
              icon: Icon(Icons.emoji_events_outlined),
              activeIcon: Icon(Icons.emoji_events),
              label: 'Games',
            ),
            BottomNavigationBarItem(
              icon: Stack(
                clipBehavior: Clip.none,
                children: [
                  const Icon(Icons.card_giftcard_outlined),
                  Positioned(
                    top: -4,
                    right: -4,
                    child: Consumer<RewardProvider>(
                      builder: (context, provider, _) {
                        final canClaim = provider.canClaimDailyReward ||
                            provider.readyToClaimAchievements.isNotEmpty;
                        return canClaim
                            ? Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: const Color(0xFFFFCC00),
                                  shape: BoxShape.circle,
                                ),
                              )
                            : const SizedBox.shrink();
                      },
                    ),
                  ),
                ],
              ),
              activeIcon: Stack(
                clipBehavior: Clip.none,
                children: [
                  const Icon(Icons.card_giftcard),
                  Positioned(
                    top: -4,
                    right: -4,
                    child: Consumer<RewardProvider>(
                      builder: (context, provider, _) {
                        final canClaim = provider.canClaimDailyReward ||
                            provider.readyToClaimAchievements.isNotEmpty;
                        return canClaim
                            ? Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: const Color(0xFFFFCC00),
                                  shape: BoxShape.circle,
                                ),
                              )
                            : const SizedBox.shrink();
                      },
                    ),
                  ),
                ],
              ),
              label: 'Rewards',
            ),
            const BottomNavigationBarItem(
              icon: Icon(Icons.account_balance_wallet_outlined),
              activeIcon: Icon(Icons.account_balance_wallet),
              label: 'Wallet',
            ),
            BottomNavigationBarItem(
              icon: Stack(
                clipBehavior: Clip.none,
                children: [
                  const Icon(Icons.person_outline_rounded),
                  Positioned(
                    top: -4,
                    right: -4,
                    child: Consumer<NotificationProvider>(
                      builder: (context, provider, _) {
                        return NotificationBadge(size: 16, mini: true);
                      },
                    ),
                  ),
                ],
              ),
              activeIcon: Stack(
                clipBehavior: Clip.none,
                children: [
                  const Icon(Icons.person_rounded),
                  Positioned(
                    top: -4,
                    right: -4,
                    child: Consumer<NotificationProvider>(
                      builder: (context, provider, _) {
                        return NotificationBadge(size: 16, mini: true);
                      },
                    ),
                  ),
                ],
              ),
              label: 'Profile',
            ),
          ],
        ),
      ),
    );
  }
}
