/// Loyalty system models for the WiggyZ app
/// These models handle loyalty tiers, points, and VIP benefits
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Enum for loyalty actions that earn points
enum LoyaltyAction {
  purchase,
  activity,
  referral,
  achievement,
  dailyLogin,
  manualAdjustment,
}

/// Extension for loyalty action utilities
extension LoyaltyActionExtension on LoyaltyAction {
  String get value {
    switch (this) {
      case LoyaltyAction.purchase:
        return 'purchase';
      case LoyaltyAction.activity:
        return 'activity';
      case LoyaltyAction.referral:
        return 'referral';
      case LoyaltyAction.achievement:
        return 'achievement';
      case LoyaltyAction.dailyLogin:
        return 'daily_login';
      case LoyaltyAction.manualAdjustment:
        return 'manual_adjustment';
    }
  }

  static LoyaltyAction fromString(String value) {
    switch (value) {
      case 'purchase':
        return LoyaltyAction.purchase;
      case 'activity':
        return LoyaltyAction.activity;
      case 'referral':
        return LoyaltyAction.referral;
      case 'achievement':
        return LoyaltyAction.achievement;
      case 'daily_login':
        return LoyaltyAction.dailyLogin;
      case 'manual_adjustment':
        return LoyaltyAction.manualAdjustment;
      default:
        return LoyaltyAction.activity;
    }
  }

  /// Get display name for loyalty action
  String get displayName {
    switch (this) {
      case LoyaltyAction.purchase:
        return 'Purchase';
      case LoyaltyAction.activity:
        return 'Activity';
      case LoyaltyAction.referral:
        return 'Referral';
      case LoyaltyAction.achievement:
        return 'Achievement';
      case LoyaltyAction.dailyLogin:
        return 'Daily Login';
      case LoyaltyAction.manualAdjustment:
        return 'Manual Adjustment';
    }
  }

  /// Get icon for loyalty action
  IconData get icon {
    switch (this) {
      case LoyaltyAction.purchase:
        return Icons.shopping_cart;
      case LoyaltyAction.activity:
        return Icons.sports_esports;
      case LoyaltyAction.referral:
        return Icons.share;
      case LoyaltyAction.achievement:
        return Icons.emoji_events;
      case LoyaltyAction.dailyLogin:
        return Icons.calendar_today;
      case LoyaltyAction.manualAdjustment:
        return Icons.admin_panel_settings;
    }
  }
}

/// Model for loyalty tier benefits
class LoyaltyBenefit {
  final String type;
  final String description;
  final dynamic value;

  const LoyaltyBenefit({
    required this.type,
    required this.description,
    this.value,
  });

  factory LoyaltyBenefit.fromJson(Map<String, dynamic> json) {
    try {
      return LoyaltyBenefit(
        type: json['type']?.toString() ?? 'unknown',
        description: json['description']?.toString() ?? 'No description',
        value: json['value'],
      );
    } catch (e) {
      // Return a default benefit if parsing fails
      return LoyaltyBenefit(
        type: 'unknown',
        description: 'Failed to parse benefit',
        value: null,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'description': description,
      'value': value,
    };
  }
}

/// Model for loyalty tiers
class LoyaltyTierModel {
  final String id;
  final String name;
  final String? description;
  final String? icon;
  final int minPoints;
  final List<LoyaltyBenefit> benefits;
  final Color? color;
  final DateTime createdAt;

  const LoyaltyTierModel({
    required this.id,
    required this.name,
    this.description,
    this.icon,
    required this.minPoints,
    required this.benefits,
    this.color,
    required this.createdAt,
  });

  factory LoyaltyTierModel.fromJson(Map<String, dynamic> json) {
    try {
      List<LoyaltyBenefit> benefitsList = [];
      if (json['benefits'] != null) {
        if (json['benefits'] is List) {
          benefitsList = (json['benefits'] as List)
              .map((benefit) => LoyaltyBenefit.fromJson(benefit as Map<String, dynamic>))
              .toList();
        } else if (json['benefits'] is Map) {
          // Handle case where benefits is a Map with key-value pairs
          final benefitsMap = json['benefits'] as Map<String, dynamic>;
          benefitsList = benefitsMap.entries.map((entry) {
            return LoyaltyBenefit(
              type: entry.key,
              description: _formatBenefitDescription(entry.key, entry.value),
              value: entry.value,
            );
          }).toList();
        }
      }

      return LoyaltyTierModel(
        id: json['id']?.toString() ?? 'unknown-${DateTime.now().millisecondsSinceEpoch}',
        name: json['name']?.toString() ?? 'Unknown Tier',
        description: json['description']?.toString(),
        icon: json['icon']?.toString(),
        minPoints: (json['min_points'] as num?)?.toInt() ?? 0,
        benefits: benefitsList,
        color: json['color']?.toString() != null ? _parseColor(json['color'].toString()) : null,
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'].toString()) ?? DateTime.now()
            : DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error parsing LoyaltyTierModel from JSON: $e');
      debugPrint('JSON data: $json');
      // Return a default tier model to prevent crashes
      return LoyaltyTierModel(
        id: json['id']?.toString() ?? 'unknown',
        name: json['name']?.toString() ?? 'Error Loading Tier',
        description: json['description']?.toString() ?? 'Failed to load tier data',
        icon: json['icon']?.toString(),
        minPoints: (json['min_points'] as num?)?.toInt() ?? 0,
        benefits: [],
        color: null,
        createdAt: DateTime.now(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'min_points': minPoints,
      'benefits': benefits.map((benefit) => benefit.toJson()).toList(),
      'color': color != null ? _colorToString(color!) : null,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Get display color for tier
  Color get displayColor {
    if (color != null) return color!;
    
    // Default colors based on tier name
    final lowerName = name.toLowerCase();
    if (lowerName.contains('bronze') || lowerName.contains('basic')) {
      return Colors.brown;
    } else if (lowerName.contains('silver')) {
      return Colors.grey;
    } else if (lowerName.contains('gold')) {
      return Colors.amber;
    } else if (lowerName.contains('platinum') || lowerName.contains('diamond')) {
      return Colors.cyan;
    } else if (lowerName.contains('vip') || lowerName.contains('premium')) {
      return Colors.purple;
    }
    return Colors.blue;
  }

  /// Get display icon
  IconData get displayIcon {
    if (icon != null) {
      switch (icon!.toLowerCase()) {
        case 'bronze_medal':
        case 'bronze':
          return Icons.military_tech; // Will be overridden by emoji
        case 'silver_medal':
        case 'silver':
          return Icons.military_tech; // Will be overridden by emoji
        case 'gold_medal':
        case 'gold':
          return Icons.military_tech; // Will be overridden by emoji
        case 'platinum_medal':
        case 'platinum':
        case 'diamond':
          return Icons.military_tech; // Will be overridden by emoji
        case 'star':
          return Icons.star;
        case 'crown':
          return Icons.workspace_premium;
        case 'shield':
          return Icons.shield;
        case 'trophy':
          return Icons.emoji_events;
        default:
          return Icons.military_tech;
      }
    }

    // Default icons based on tier name
    final lowerName = name.toLowerCase();
    if (lowerName.contains('bronze')) {
      return Icons.military_tech; // Will be overridden by emoji
    } else if (lowerName.contains('silver')) {
      return Icons.military_tech; // Will be overridden by emoji
    } else if (lowerName.contains('gold')) {
      return Icons.military_tech; // Will be overridden by emoji
    } else if (lowerName.contains('platinum') || lowerName.contains('diamond')) {
      return Icons.military_tech; // Will be overridden by emoji
    }

    return Icons.military_tech;
  }

  /// Get emoji icon for tier
  String? get emojiIcon {
    if (icon != null) {
      switch (icon!.toLowerCase()) {
        case 'bronze_medal':
        case 'bronze':
          return '🥉';
        case 'silver_medal':
        case 'silver':
          return '🥈';
        case 'gold_medal':
        case 'gold':
          return '🥇';
        case 'platinum_medal':
        case 'platinum':
        case 'diamond':
          return '💎';
      }
    }

    // Default emojis based on tier name
    final lowerName = name.toLowerCase();
    if (lowerName.contains('bronze')) {
      return '🥉';
    } else if (lowerName.contains('silver')) {
      return '🥈';
    } else if (lowerName.contains('gold')) {
      return '🥇';
    } else if (lowerName.contains('platinum') || lowerName.contains('diamond')) {
      return '💎';
    }

    return null;
  }

  /// Parse color from string
  static Color? _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Convert color to string
  static String _colorToString(Color color) {
    return '#${color.value.toRadixString(16).padLeft(8, '0').substring(2)}';
  }

  /// Format benefit description based on type and value
  static String _formatBenefitDescription(String type, dynamic value) {
    switch (type.toLowerCase()) {
      case 'special_offers':
        return value == true ? 'Access to special offers' : 'No special offers';
      case 'exclusive_items':
        return value == true ? 'Access to exclusive items' : 'No exclusive items';
      case 'discount_percent':
        final discount = (value as num?)?.toInt() ?? 0;
        return discount > 0 ? '$discount% discount on purchases' : 'No purchase discount';
      case 'priority_support':
        return value == true ? 'Priority customer support' : 'Standard support';
      case 'daily_bonus':
        final bonus = (value as num?)?.toInt() ?? 0;
        return bonus > 0 ? '+$bonus diamonds daily bonus' : 'No daily bonus';
      case 'shop_discount':
        final discount = (value as num?)?.toInt() ?? 0;
        return discount > 0 ? '$discount% shop discount' : 'No shop discount';
      case 'tier_up_reward':
        final reward = (value as num?)?.toInt() ?? 0;
        return reward > 0 ? '$reward diamonds tier-up reward' : 'No tier-up reward';
      case 'exclusive_games':
        return value == true ? 'Access to exclusive games' : 'No exclusive games';
      case 'tournament_entries':
        final entries = (value as num?)?.toInt() ?? 0;
        return entries > 0 ? '$entries free tournament entries per week' : 'No free tournament entries';
      default:
        // Fallback formatting
        final formattedType = type.replaceAll('_', ' ').split(' ').map((word) =>
          word.isNotEmpty ? word[0].toUpperCase() + word.substring(1).toLowerCase() : word
        ).join(' ');
        return '$formattedType: $value';
    }
  }
}

/// Model for user loyalty status
class UserLoyaltyModel {
  final String id;
  final String userId;
  final int points;
  final String tierId;
  final DateTime updatedAt;
  final LoyaltyTierModel? loyaltyTier;

  const UserLoyaltyModel({
    required this.id,
    required this.userId,
    required this.points,
    required this.tierId,
    required this.updatedAt,
    this.loyaltyTier,
  });

  factory UserLoyaltyModel.fromJson(Map<String, dynamic> json) {
    try {
      // Handle different response structures from different endpoints
      LoyaltyTierModel? tierModel;
      String tierId = '';

      // Check if this is a loyalty status response (has 'tier' property)
      if (json['tier'] != null) {
        final tierData = json['tier'] as Map<String, dynamic>;
        tierModel = LoyaltyTierModel.fromJson(tierData);
        tierId = tierData['id']?.toString() ?? '';
      }
      // Check if this is a user loyalty record response (has 'loyalty_tiers' property)
      else if (json['loyalty_tiers'] != null) {
        final tierData = json['loyalty_tiers'] as Map<String, dynamic>;
        tierModel = LoyaltyTierModel.fromJson(tierData);
        tierId = tierData['id']?.toString() ?? '';
      }
      // Check if tier_id is provided directly
      else if (json['tier_id'] != null) {
        tierId = json['tier_id'].toString();
      }

      return UserLoyaltyModel(
        id: json['id']?.toString() ?? 'loyalty-${DateTime.now().millisecondsSinceEpoch}',
        userId: json['user_id']?.toString() ?? 'current-user',
        points: (json['points'] as num?)?.toInt() ?? 0,
        tierId: tierId,
        updatedAt: json['updated_at'] != null
            ? DateTime.tryParse(json['updated_at'].toString()) ?? DateTime.now()
            : DateTime.now(),
        loyaltyTier: tierModel,
      );
    } catch (e) {
      debugPrint('Error parsing UserLoyaltyModel from JSON: $e');
      debugPrint('JSON data: $json');
      // Return a default user loyalty model to prevent crashes
      return UserLoyaltyModel(
        id: 'error-${DateTime.now().millisecondsSinceEpoch}',
        userId: 'unknown-user',
        points: (json['points'] as num?)?.toInt() ?? 0,
        tierId: 'unknown',
        updatedAt: DateTime.now(),
        loyaltyTier: null,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'points': points,
      'tier_id': tierId,
      'updated_at': updatedAt.toIso8601String(),
      'loyalty_tiers': loyaltyTier?.toJson(),
    };
  }

  /// Get current tier name
  String get currentTierName {
    return loyaltyTier?.name ?? 'Unknown';
  }

  /// Get progress to next tier (0.0 to 1.0)
  double getProgressToNextTier(List<LoyaltyTierModel> allTiers) {
    if (loyaltyTier == null) return 0.0;
    
    // Sort tiers by min points
    final sortedTiers = List<LoyaltyTierModel>.from(allTiers)
      ..sort((a, b) => a.minPoints.compareTo(b.minPoints));
    
    // Find current tier index
    final currentIndex = sortedTiers.indexWhere((tier) => tier.id == loyaltyTier!.id);
    if (currentIndex == -1 || currentIndex == sortedTiers.length - 1) {
      return 1.0; // Already at highest tier
    }
    
    final currentTier = sortedTiers[currentIndex];
    final nextTier = sortedTiers[currentIndex + 1];
    
    final pointsInCurrentTier = points - currentTier.minPoints;
    final pointsNeededForNextTier = nextTier.minPoints - currentTier.minPoints;
    
    if (pointsNeededForNextTier <= 0) return 1.0;
    
    return (pointsInCurrentTier / pointsNeededForNextTier).clamp(0.0, 1.0);
  }

  /// Get points needed for next tier
  int getPointsNeededForNextTier(List<LoyaltyTierModel> allTiers) {
    if (loyaltyTier == null) return 0;
    
    // Sort tiers by min points
    final sortedTiers = List<LoyaltyTierModel>.from(allTiers)
      ..sort((a, b) => a.minPoints.compareTo(b.minPoints));
    
    // Find current tier index
    final currentIndex = sortedTiers.indexWhere((tier) => tier.id == loyaltyTier!.id);
    if (currentIndex == -1 || currentIndex == sortedTiers.length - 1) {
      return 0; // Already at highest tier
    }
    
    final nextTier = sortedTiers[currentIndex + 1];
    return (nextTier.minPoints - points).clamp(0, nextTier.minPoints);
  }

  /// Get next tier
  LoyaltyTierModel? getNextTier(List<LoyaltyTierModel> allTiers) {
    if (loyaltyTier == null) return null;
    
    // Sort tiers by min points
    final sortedTiers = List<LoyaltyTierModel>.from(allTiers)
      ..sort((a, b) => a.minPoints.compareTo(b.minPoints));
    
    // Find current tier index
    final currentIndex = sortedTiers.indexWhere((tier) => tier.id == loyaltyTier!.id);
    if (currentIndex == -1 || currentIndex == sortedTiers.length - 1) {
      return null; // Already at highest tier
    }
    
    return sortedTiers[currentIndex + 1];
  }
}

/// Model for loyalty point transactions
class LoyaltyTransactionModel {
  final String id;
  final String userId;
  final int points;
  final LoyaltyAction action;
  final String? description;
  final DateTime createdAt;

  const LoyaltyTransactionModel({
    required this.id,
    required this.userId,
    required this.points,
    required this.action,
    this.description,
    required this.createdAt,
  });

  factory LoyaltyTransactionModel.fromJson(Map<String, dynamic> json) {
    return LoyaltyTransactionModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      points: json['points'] as int,
      action: LoyaltyActionExtension.fromString(json['action'] as String),
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'points': points,
      'action': action.value,
      'description': description,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Check if points were added or deducted
  bool get isPointsAdded => points > 0;
}
