/// Test script to verify null safety fixes in reward models
/// This tests the models with various null and malformed data scenarios
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:wiggyz_app/models/reward_models.dart';
import 'package:wiggyz_app/models/achievement_models.dart';
import 'package:wiggyz_app/models/loyalty_models.dart';

void main() {
  group('Null Safety Tests for Reward Models', () {
    test('RewardModel handles null values gracefully', () {
      // Test with null values that were causing crashes
      final Map<String, dynamic> nullData = {
        'id': null,
        'type_id': null,
        'title': null,
        'description': null,
        'points': null,
        'diamond_value': null,
        'requirements': null,
        'is_active': null,
        'start_date': null,
        'end_date': null,
        'created_at': null,
        'updated_at': null,
        'reward_types': null,
      };

      expect(() => RewardModel.fromJson(nullData), returnsNormally);
      
      final reward = RewardModel.fromJson(nullData);
      expect(reward.id, isNotEmpty);
      expect(reward.title, equals('Unknown Reward'));
      expect(reward.points, equals(0));
      expect(reward.diamondValue, equals(0));
    });

    test('RewardModel handles partial null values', () {
      final Map<String, dynamic> partialData = {
        'id': 'test-id',
        'type_id': 'test-type',
        'title': 'Test Reward',
        'description': null,
        'points': 100,
        'diamond_value': null,
        'requirements': {'day': 1},
        'is_active': true,
        'start_date': null,
        'end_date': null,
        'created_at': '2025-01-01T00:00:00Z',
        'updated_at': null,
        'reward_types': null,
      };

      expect(() => RewardModel.fromJson(partialData), returnsNormally);
      
      final reward = RewardModel.fromJson(partialData);
      expect(reward.id, equals('test-id'));
      expect(reward.title, equals('Test Reward'));
      expect(reward.points, equals(100));
      expect(reward.diamondValue, equals(0));
      expect(reward.description, isNull);
    });

    test('AchievementModel handles null values gracefully', () {
      final Map<String, dynamic> nullData = {
        'id': null,
        'user_id': null,
        'title': null,
        'description': null,
        'target': null,
        'current': null,
        'icon_name': null,
        'type': null,
        'reward_points': null,
        'reward_diamonds': null,
        'is_completed': null,
        'completed_at': null,
        'created_at': null,
        'updated_at': null,
      };

      expect(() => AchievementModel.fromJson(nullData), returnsNormally);
      
      final achievement = AchievementModel.fromJson(nullData);
      expect(achievement.id, isNotEmpty);
      expect(achievement.title, equals('Unknown Achievement'));
      expect(achievement.target, equals(1));
      expect(achievement.current, equals(0));
    });

    test('LoyaltyTierModel handles null values gracefully', () {
      final Map<String, dynamic> nullData = {
        'id': null,
        'name': null,
        'description': null,
        'icon': null,
        'min_points': null,
        'benefits': null,
        'color': null,
        'created_at': null,
      };

      expect(() => LoyaltyTierModel.fromJson(nullData), returnsNormally);
      
      final tier = LoyaltyTierModel.fromJson(nullData);
      expect(tier.id, isNotEmpty);
      expect(tier.name, equals('Unknown Tier'));
      expect(tier.minPoints, equals(0));
      expect(tier.benefits, isEmpty);
    });

    test('UserLoyaltyModel handles null values gracefully', () {
      final Map<String, dynamic> nullData = {
        'id': null,
        'user_id': null,
        'points': null,
        'tier_id': null,
        'updated_at': null,
        'loyalty_tiers': null,
      };

      expect(() => UserLoyaltyModel.fromJson(nullData), returnsNormally);
      
      final userLoyalty = UserLoyaltyModel.fromJson(nullData);
      expect(userLoyalty.id, isNotEmpty);
      expect(userLoyalty.userId, isNotEmpty);
      expect(userLoyalty.points, equals(0));
    });

    test('Models handle malformed JSON gracefully', () {
      // Test with completely wrong data types
      final Map<String, dynamic> malformedData = {
        'id': 123, // Should be string
        'title': ['array', 'instead', 'of', 'string'],
        'points': 'not_a_number',
        'created_at': 'invalid_date',
      };

      expect(() => RewardModel.fromJson(malformedData), returnsNormally);
      expect(() => AchievementModel.fromJson(malformedData), returnsNormally);
    });

    test('Models handle empty JSON gracefully', () {
      final Map<String, dynamic> emptyData = {};

      expect(() => RewardModel.fromJson(emptyData), returnsNormally);
      expect(() => AchievementModel.fromJson(emptyData), returnsNormally);
      expect(() => LoyaltyTierModel.fromJson(emptyData), returnsNormally);
      expect(() => UserLoyaltyModel.fromJson(emptyData), returnsNormally);
    });
  });

  group('Real-world API Response Tests', () {
    test('Handles typical Supabase response structure', () {
      // Simulate actual Supabase response structure
      final Map<String, dynamic> supabaseResponse = {
        'id': '5e4c4d3d-077b-4511-8703-fe0407b65499',
        'type_id': '3a5f9e7d-cb09-4cae-8f3b-a7f43e2c8bb6',
        'title': 'Day 1 Login',
        'description': 'Login reward for day 1',
        'points': 5,
        'diamond_value': 5,
        'requirements': {'day': 1},
        'is_active': true,
        'start_date': null,
        'end_date': null,
        'created_at': '2025-05-03T04:56:22.635971+00:00',
        'updated_at': '2025-05-03T04:56:22.635971+00:00',
        'reward_types': null,
      };

      expect(() => RewardModel.fromJson(supabaseResponse), returnsNormally);
      
      final reward = RewardModel.fromJson(supabaseResponse);
      expect(reward.id, equals('5e4c4d3d-077b-4511-8703-fe0407b65499'));
      expect(reward.title, equals('Day 1 Login'));
      expect(reward.points, equals(5));
      expect(reward.dayRequirement, equals(1));
    });
  });
}
