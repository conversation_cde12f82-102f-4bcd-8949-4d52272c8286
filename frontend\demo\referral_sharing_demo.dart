/// Demo script showing how the referral sharing functionality works
/// This demonstrates the key features and API usage
library;

import 'package:flutter/material.dart';
import 'package:wiggyz_app/services/referral_sharing_service.dart';
import 'package:wiggyz_app/models/referral_models.dart';

void main() {
  runApp(ReferralSharingDemo());
}

class ReferralSharingDemo extends StatelessWidget {
  const ReferralSharingDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'WiggyZ Referral Sharing Demo',
      theme: ThemeData(
        primarySwatch: Colors.amber,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: DemoScreen(),
    );
  }
}

class DemoScreen extends StatefulWidget {
  const DemoScreen({super.key});

  @override
  _DemoScreenState createState() => _DemoScreenState();
}

class _DemoScreenState extends State<DemoScreen> {
  final ReferralSharingService _sharingService = ReferralSharingService();
  String _status = 'Ready to test referral sharing';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Referral Sharing Demo'),
        backgroundColor: Color(0xFFFFCC00),
        foregroundColor: Colors.black,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Display
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                _status,
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 24),

            // Demo Buttons
            _buildDemoButton(
              'Test Referral Code Generation',
              _testReferralCodeGeneration,
              Icons.code,
            ),
            SizedBox(height: 12),

            _buildDemoButton(
              'Test Share Content Creation',
              _testShareContentCreation,
              Icons.create,
            ),
            SizedBox(height: 12),

            _buildDemoButton(
              'Test Referral Sharing',
              _testReferralSharing,
              Icons.share,
            ),
            SizedBox(height: 12),

            _buildDemoButton(
              'Test Clipboard Copy',
              _testClipboardCopy,
              Icons.copy,
            ),
            SizedBox(height: 12),

            _buildDemoButton(
              'Test Code Validation',
              _testCodeValidation,
              Icons.check_circle,
            ),
            SizedBox(height: 12),

            _buildDemoButton(
              'Test URL Extraction',
              _testUrlExtraction,
              Icons.link,
            ),

            Spacer(),

            // Info Section
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Color(0xFFFFCC00).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Color(0xFFFFCC00).withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🎮 WiggyZ Referral Sharing Features:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text('✅ Native sharing through all device apps'),
                  Text('✅ Automatic referral code generation'),
                  Text('✅ Engaging share messages with rewards info'),
                  Text('✅ Clipboard fallback functionality'),
                  Text('✅ Cross-platform compatibility'),
                  Text('✅ Comprehensive error handling'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDemoButton(String title, VoidCallback onPressed, IconData icon) {
    return ElevatedButton.icon(
      onPressed: _isLoading ? null : onPressed,
      icon: _isLoading ? 
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(strokeWidth: 2),
        ) : 
        Icon(icon),
      label: Text(title),
      style: ElevatedButton.styleFrom(
        backgroundColor: Color(0xFFFFCC00),
        foregroundColor: Colors.black,
        padding: EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Future<void> _testReferralCodeGeneration() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing referral code generation...';
    });

    try {
      // Simulate getting referral code
      await Future.delayed(Duration(seconds: 1));
      final mockCode = 'DEMO${DateTime.now().millisecondsSinceEpoch % 10000}';
      
      setState(() {
        _status = '✅ Generated referral code: $mockCode';
      });
    } catch (e) {
      setState(() {
        _status = '❌ Error generating referral code: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testShareContentCreation() async {
    setState(() {
      _isLoading = true;
      _status = 'Creating share content...';
    });

    try {
      // Create mock share content
      final shareContent = ReferralShareContent.create(
        referralCode: 'DEMO1234',
        customMessage: 'Hey! Join me on this awesome gaming platform!',
      );

      setState(() {
        _status = '✅ Share content created:\n\n${shareContent.shareText.substring(0, 100)}...';
      });
    } catch (e) {
      setState(() {
        _status = '❌ Error creating share content: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testReferralSharing() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing sharing functionality...';
    });

    try {
      // Simulate sharing (would open native share dialog in real app)
      await Future.delayed(Duration(seconds: 1));
      
      setState(() {
        _status = '✅ Sharing functionality works! In a real app, this would open the native share dialog with all available apps.';
      });
    } catch (e) {
      setState(() {
        _status = '❌ Error testing sharing: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testClipboardCopy() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing clipboard functionality...';
    });

    try {
      // Simulate clipboard copy
      await Future.delayed(Duration(milliseconds: 500));
      
      setState(() {
        _status = '✅ Clipboard copy works! Referral message copied successfully.';
      });
    } catch (e) {
      setState(() {
        _status = '❌ Error testing clipboard: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _testCodeValidation() {
    setState(() {
      _status = 'Testing referral code validation...';
    });

    // Test various referral codes
    final testCodes = [
      'DEMO1234', // Valid
      'ABC123',   // Valid
      '123',      // Invalid (too short)
      'test@123', // Invalid (special chars)
      '',         // Invalid (empty)
    ];

    final results = testCodes.map((code) {
      final isValid = ReferralService.isValidReferralCode(code);
      return '$code: ${isValid ? "✅ Valid" : "❌ Invalid"}';
    }).join('\n');

    setState(() {
      _status = '✅ Code validation results:\n\n$results';
    });
  }

  void _testUrlExtraction() {
    setState(() {
      _status = 'Testing URL extraction...';
    });

    // Test various URLs
    final testUrls = [
      'https://wiggyz.com/refer/DEMO1234',
      'https://wiggyz.com/app?ref=ABC123',
      'https://wiggyz.com/other/page',
      'invalid-url',
    ];

    final results = testUrls.map((url) {
      final code = ReferralService.extractReferralCodeFromUrl(url);
      return '${url.length > 30 ? "${url.substring(0, 30)}..." : url}:\n${code ?? "No code found"}';
    }).join('\n\n');

    setState(() {
      _status = '✅ URL extraction results:\n\n$results';
    });
  }
}

/// Example of how to use the referral sharing service in your app
class ReferralSharingExample {
  static Future<void> demonstrateUsage() async {
    final sharingService = ReferralSharingService();

    try {
      // 1. Get referral code
      final referralCode = await sharingService.getReferralCode();
      print('User referral code: $referralCode');

      // 2. Create share content
      final shareContent = await sharingService.getReferralShareContent(
        customMessage: 'Join me on WiggyZ for awesome gaming rewards!',
      );
      print('Share content created: ${shareContent.shareText}');

      // 3. Share referral
      final shareResult = await sharingService.shareReferral();
      
      if (shareResult.isSuccess) {
        print('✅ Referral shared successfully!');
      } else if (shareResult.isCancelled) {
        print('ℹ️ User cancelled sharing');
      } else {
        print('❌ Sharing failed: ${shareResult.errorMessage}');
      }

      // 4. Fallback: Copy to clipboard
      final copySuccess = await sharingService.copyReferralMessageToClipboard();
      if (copySuccess) {
        print('✅ Referral message copied to clipboard');
      }

    } catch (e) {
      print('❌ Error in referral sharing: $e');
    }
  }
}
