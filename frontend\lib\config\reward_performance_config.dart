/// Performance configuration for the reward system
/// Centralized settings for caching, optimization, and monitoring
library;

class RewardPerformanceConfig {
  // Cache Configuration
  static const int dailyRewardsCacheDuration = 5; // minutes
  static const int achievementsCacheDuration = 10; // minutes
  static const int loyaltyCacheDuration = 15; // minutes
  static const int userRewardsCacheDuration = 5; // minutes
  
  // Memory Management
  static const int maxUserRewards = 100;
  static const int maxRewardTransactions = 50;
  static const int maxLoyaltyHistory = 50;
  static const int maxAchievements = 50;
  static const int maxMetricsHistory = 100;
  
  // Performance Thresholds
  static const Duration slowOperationThreshold = Duration(seconds: 2);
  static const Duration apiTimeoutDuration = Duration(seconds: 30);
  static const Duration debounceDelay = Duration(milliseconds: 300);
  static const Duration batchDelay = Duration(milliseconds: 100);
  
  // Image Cache
  static const int maxImageCacheSize = 50 * 1024 * 1024; // 50MB
  static const int maxImageCacheItems = 200;
  
  // Offline Management
  static const int maxOfflineOperations = 50;
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(minutes: 5);
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Monitoring
  static const Duration memoryCheckInterval = Duration(minutes: 5);
  static const Duration performanceReportInterval = Duration(minutes: 10);
  
  // Feature Flags
  static const bool enablePerformanceMonitoring = true;
  static const bool enableMemoryOptimization = true;
  static const bool enableImageCacheOptimization = true;
  static const bool enableOfflineSupport = true;
  static const bool enableAdvancedCaching = true;
  
  // Development Settings
  static const bool enableDebugLogging = true;
  static const bool enablePerformanceLogging = true;
  static const bool enableMemoryLogging = false;
  
  /// Get cache duration for specific data type
  static int getCacheDuration(String dataType) {
    switch (dataType) {
      case 'daily_rewards':
        return dailyRewardsCacheDuration;
      case 'achievements':
        return achievementsCacheDuration;
      case 'loyalty':
        return loyaltyCacheDuration;
      case 'user_rewards':
        return userRewardsCacheDuration;
      default:
        return dailyRewardsCacheDuration;
    }
  }
  
  /// Get memory limit for specific data type
  static int getMemoryLimit(String dataType) {
    switch (dataType) {
      case 'user_rewards':
        return maxUserRewards;
      case 'transactions':
        return maxRewardTransactions;
      case 'loyalty_history':
        return maxLoyaltyHistory;
      case 'achievements':
        return maxAchievements;
      default:
        return maxUserRewards;
    }
  }
  
  /// Check if feature is enabled
  static bool isFeatureEnabled(String feature) {
    switch (feature) {
      case 'performance_monitoring':
        return enablePerformanceMonitoring;
      case 'memory_optimization':
        return enableMemoryOptimization;
      case 'image_cache_optimization':
        return enableImageCacheOptimization;
      case 'offline_support':
        return enableOfflineSupport;
      case 'advanced_caching':
        return enableAdvancedCaching;
      default:
        return false;
    }
  }
  
  /// Get configuration summary
  static Map<String, dynamic> getConfigSummary() {
    return {
      'cache': {
        'daily_rewards_duration': dailyRewardsCacheDuration,
        'achievements_duration': achievementsCacheDuration,
        'loyalty_duration': loyaltyCacheDuration,
        'user_rewards_duration': userRewardsCacheDuration,
      },
      'memory': {
        'max_user_rewards': maxUserRewards,
        'max_transactions': maxRewardTransactions,
        'max_loyalty_history': maxLoyaltyHistory,
        'max_achievements': maxAchievements,
      },
      'performance': {
        'slow_operation_threshold_ms': slowOperationThreshold.inMilliseconds,
        'api_timeout_ms': apiTimeoutDuration.inMilliseconds,
        'debounce_delay_ms': debounceDelay.inMilliseconds,
      },
      'features': {
        'performance_monitoring': enablePerformanceMonitoring,
        'memory_optimization': enableMemoryOptimization,
        'image_cache_optimization': enableImageCacheOptimization,
        'offline_support': enableOfflineSupport,
        'advanced_caching': enableAdvancedCaching,
      },
    };
  }
}
