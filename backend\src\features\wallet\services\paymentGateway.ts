/**
 * Payment Gateway service for handling payment processing
 * This is a simplified implementation that interfaces with external payment processors
 */
import { Request } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { config } from '../../../config';
import crypto from 'crypto';
import { razorpayService, RazorpayOrderOptions } from '../../../services/razorpayService';
import { logger } from '../../../utils/logger';

// Supported payment gateways
export enum PaymentGateway {
  STRIPE = 'stripe',
  PAYPAL = 'paypal',
  RAZORPAY = 'razorpay'
}

// Payment method types
export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_TRANSFER = 'bank_transfer',
  WALLET = 'wallet',
  UPI = 'upi'
}

// Transaction status types
export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// Payment initialization response interface
export interface PaymentInitializationResponse {
  transaction_id: string;
  payment_token: string;
  gateway: string;
  redirect_url?: string;
  expires_at: string;
  order_id?: string; // Additional field for Razorpay
  key_id?: string; // Razorpay key ID for frontend
}

/**
 * Initialize a payment with the specified gateway
 * @param gateway - Payment gateway identifier
 * @param amount - Amount to charge
 * @param currency - Currency code (ISO 4217)
 * @param metadata - Additional payment metadata
 * @returns Payment token and transaction details
 */
export const initializePayment = async (
  gateway: PaymentGateway,
  amount: number,
  currency: string,
  metadata: Record<string, any>
): Promise<PaymentInitializationResponse> => {
  const transactionId = uuidv4();
  const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30 minute expiry
  
  // Implementation would depend on the specific gateway
  switch (gateway) {
    case PaymentGateway.STRIPE:
      // In a real implementation, would call Stripe API here
      return {
        transaction_id: transactionId,
        payment_token: `stripe_${crypto.randomBytes(16).toString('hex')}`,
        gateway: PaymentGateway.STRIPE,
        redirect_url: `https://checkout.stripe.com/example?token=${transactionId}`,
        expires_at: expiresAt.toISOString()
      };
      
    case PaymentGateway.PAYPAL:
      // In a real implementation, would call PayPal API here
      return {
        transaction_id: transactionId,
        payment_token: `paypal_${crypto.randomBytes(16).toString('hex')}`,
        gateway: PaymentGateway.PAYPAL,
        redirect_url: `https://www.paypal.com/checkout?token=${transactionId}`,
        expires_at: expiresAt.toISOString()
      };
      
    case PaymentGateway.RAZORPAY:
      // Real Razorpay implementation
      try {
        // Convert amount from rupees to paise for Razorpay
        const amountInPaise = Math.round(amount * 100);

        console.log(`Razorpay order creation - Amount in rupees: ${amount}, Amount in paise: ${amountInPaise}`);

        const orderOptions: RazorpayOrderOptions = {
          amount: amountInPaise, // Amount in paise for Razorpay
          currency: currency,
          receipt: transactionId,
          userId: metadata.user_id,
          description: metadata.description || 'Wallet top-up',
          notes: {
            transaction_id: transactionId,
            ...metadata
          }
        };

        const order = await razorpayService.createOrder(orderOptions);

        return {
          transaction_id: transactionId,
          payment_token: order.id, // Razorpay order ID
          gateway: PaymentGateway.RAZORPAY,
          redirect_url: undefined, // Razorpay uses checkout.js, no redirect needed
          expires_at: expiresAt.toISOString(),
          order_id: order.id, // Additional field for Razorpay
          key_id: razorpayService.getPaymentConfig().keyId // Safe to expose
        };
      } catch (error: any) {
        logger.error(`Razorpay order creation failed: ${error.message} (Transaction: ${transactionId}, Amount: ${amount}, Currency: ${currency})`);
        throw new Error(`Failed to initialize Razorpay payment: ${error.message}`);
      }
      
    default:
      throw new Error(`Unsupported payment gateway: ${gateway}`);
  }
};

/**
 * Verify the webhook signature from a payment gateway
 * @param gateway - Payment gateway identifier
 * @param request - Express request object containing the webhook payload
 * @returns Whether the signature is valid
 */
export const verifyWebhookSignature = async (
  gateway: PaymentGateway,
  request: Request
): Promise<boolean> => {
  switch (gateway) {
    case PaymentGateway.STRIPE:
      // Stripe signature verification
      const stripeSignature = request.headers['stripe-signature'];
      if (!stripeSignature) return false;
      
      // In a real implementation, would verify using Stripe SDK
      // return stripe.webhooks.constructEvent(request.body, stripeSignature, stripeWebhookSecret);
      return true; // Simplified implementation
      
    case PaymentGateway.PAYPAL:
      // PayPal signature verification
      const paypalSignature = request.headers['paypal-auth-algo'] as string;
      if (!paypalSignature) return false;
      
      // In a real implementation, would verify using PayPal SDK
      return true; // Simplified implementation
      
    case PaymentGateway.RAZORPAY:
      // Real Razorpay signature verification
      const razorpaySignature = request.headers['x-razorpay-signature'] as string;
      if (!razorpaySignature) {
        logger.warn('Missing Razorpay signature in webhook request');
        return false;
      }

      // Get raw body as string for signature verification
      const body = typeof request.body === 'string' ? request.body : JSON.stringify(request.body);

      try {
        const isValid = razorpayService.verifyWebhookSignature(body, razorpaySignature);
        if (!isValid) {
          logger.warn('Invalid Razorpay webhook signature');
        }
        return isValid;
      } catch (error: any) {
        logger.error(`Razorpay webhook signature verification failed: ${error.message}`);
        return false;
      }
      
    default:
      return false;
  }
};

/**
 * Parse the transaction ID from a webhook payload
 * @param gateway - Payment gateway identifier
 * @param payload - Webhook payload
 * @returns Transaction ID and status
 */
export const parseWebhookPayload = (
  gateway: PaymentGateway,
  payload: any
): { transactionId: string; status: TransactionStatus; metadata?: any } => {
  switch (gateway) {
    case PaymentGateway.STRIPE:
      // Parse Stripe webhook
      return {
        transactionId: payload.data?.object?.metadata?.transaction_id,
        status: payload.type === 'payment_intent.succeeded' 
          ? TransactionStatus.COMPLETED 
          : TransactionStatus.FAILED,
        metadata: payload.data?.object
      };
      
    case PaymentGateway.PAYPAL:
      // Parse PayPal webhook
      return {
        transactionId: payload.resource?.invoice_id,
        status: payload.event_type === 'PAYMENT.CAPTURE.COMPLETED' 
          ? TransactionStatus.COMPLETED 
          : TransactionStatus.FAILED,
        metadata: payload.resource
      };
      
    case PaymentGateway.RAZORPAY:
      // Parse Razorpay webhook with comprehensive event handling
      const razorpayEvent = payload.event;
      const paymentEntity = payload.payload?.payment?.entity;
      const orderEntity = payload.payload?.order?.entity;

      let status: TransactionStatus;
      let transactionId: string;

      // Determine transaction ID from payment or order
      if (paymentEntity) {
        transactionId = paymentEntity.notes?.transaction_id || paymentEntity.order_id;
      } else if (orderEntity) {
        transactionId = orderEntity.notes?.transaction_id || orderEntity.receipt;
      } else {
        throw new Error('No valid transaction ID found in Razorpay webhook payload');
      }

      // Map Razorpay events to transaction status
      switch (razorpayEvent) {
        case 'payment.authorized':
          status = TransactionStatus.PROCESSING;
          break;
        case 'payment.captured':
          status = TransactionStatus.COMPLETED;
          break;
        case 'payment.failed':
          status = TransactionStatus.FAILED;
          break;
        case 'order.paid':
          status = TransactionStatus.COMPLETED;
          break;
        default:
          status = TransactionStatus.PENDING;
      }

      return {
        transactionId,
        status,
        metadata: payload.payload?.payment?.entity
      };
      
    default:
      throw new Error(`Unsupported payment gateway: ${gateway}`);
  }
};

/**
 * Process a payout to a user
 * @param gateway - Payment gateway identifier
 * @param amount - Amount to pay out
 * @param currency - Currency code (ISO 4217)
 * @param accountDetails - User's withdrawal account details
 * @returns Payout details
 */
export const processPayout = async (
  gateway: PaymentGateway,
  amount: number,
  currency: string,
  accountDetails: any
): Promise<{ payout_id: string; status: TransactionStatus }> => {
  // Implementation would depend on the specific gateway
  switch (gateway) {
    case PaymentGateway.STRIPE:
      // In a real implementation, would call Stripe payout API
      return {
        payout_id: `po_${crypto.randomBytes(16).toString('hex')}`,
        status: TransactionStatus.PROCESSING
      };

    case PaymentGateway.PAYPAL:
      // In a real implementation, would call PayPal payout API
      return {
        payout_id: `pp_payout_${crypto.randomBytes(16).toString('hex')}`,
        status: TransactionStatus.PROCESSING
      };

    case PaymentGateway.RAZORPAY:
      // Real Razorpay payout implementation
      try {
        // Convert amount from rupees to paise for Razorpay
        const amountInPaise = Math.round(amount * 100);

        logger.info(`Razorpay payout initiation - Amount in rupees: ${amount}, Amount in paise: ${amountInPaise}`);

        // Create payout using Razorpay service
        const payout = await razorpayService.createPayout({
          amount: amountInPaise,
          currency: currency,
          account_details: accountDetails,
          mode: 'IMPS', // Immediate Payment Service for faster transfers
          purpose: 'refund', // Purpose for the payout
          queue_if_low_balance: true,
          reference_id: `payout_${crypto.randomBytes(8).toString('hex')}`
        });

        logger.info(`Razorpay payout created successfully - Payout ID: ${payout.id}, Amount: ${payout.amount}`);

        return {
          payout_id: payout.id,
          status: TransactionStatus.PROCESSING
        };
      } catch (error: any) {
        logger.error(`Razorpay payout creation failed: ${error.message} - Amount: ${amount}, Currency: ${currency}`);
        throw new Error(`Failed to process Razorpay payout: ${error.message}`);
      }

    default:
      throw new Error(`Unsupported payout gateway: ${gateway}`);
  }
};
