import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../features/shop/providers/shop_provider.dart';
import '../services/cart_service.dart';

class CheckoutScreen extends StatefulWidget {
  final double totalAmount;
  final List<Map<String, dynamic>> cartItems; // Added

  const CheckoutScreen({
    super.key,
    required this.totalAmount,
    required this.cartItems, // Added
  });

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen> {

  int _selectedPaymentMethod = 1; // Default to UPI (index 1)
  bool _isProcessing = false;
  final _formKey = GlobalKey<FormState>();
  final _gameIdController = TextEditingController();
  final _userIdController = TextEditingController();
  final CartService _cartService = CartService(); // Added for clearing cart

  // Sample payment methods
  final List<Map<String, dynamic>> _paymentMethods = [
    {
      'title': 'Credit Card',
      'subtitle': '**** **** **** 4589',
      'icon': Icons.credit_card,
    },
    {
      'title': 'UPI',
      'subtitle': 'username@upi',
      'icon': Icons.account_balance,
    },
    {
      'title': 'PayPal',
      'subtitle': '<EMAIL>',
      'icon': Icons.payment,
    },
  ];

  @override
  void dispose() {
    _gameIdController.dispose();
    _userIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDarkMode ? const Color(0xFF121418) : Colors.white;
    final cardColor = isDarkMode ? const Color(0xFF1E1F26) : Colors.grey[100];
    final textColor = isDarkMode ? Colors.white : Colors.black;
    final accentColor = const Color(0xFFFFA500);

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFFFFCC00),
                Color(0xFFFF9500),
              ],
            ),
          ),
        ),
        backgroundColor: Colors.transparent,
        title: Text(
          'Checkout',
          style: GoogleFonts.poppins(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _isProcessing 
          ? _buildProcessingView()
          : Form(
              key: _formKey,
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // Game ID / User ID section
                  _buildSectionTitle('Game Information', textColor),
                  const SizedBox(height: 12),
                  _buildGameIdSection(cardColor, textColor),
                  const SizedBox(height: 24),
                  
                  // Payment method section
                  _buildSectionTitle('Payment Method', textColor),
                  const SizedBox(height: 12),
                  _buildPaymentMethodList(cardColor, textColor, accentColor),
                  const SizedBox(height: 24),
                  
                  // Order summary section
                  _buildSectionTitle('Order Summary', textColor),
                  const SizedBox(height: 12),
                  _buildOrderSummary(cardColor, textColor, accentColor),
                  const SizedBox(height: 32),
                  
                  // Place order button
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: accentColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      onPressed: _placeOrder,
                      child: Text(
                        'Place Order',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionTitle(String title, Color textColor) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: textColor,
      ),
    );
  }

  Widget _buildGameIdSection(Color? cardColor, Color textColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Description text
          Text(
            'Please enter your Game ID and User ID to deliver items to your game account',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 16),
          
          // Game ID field
          Text(
            'Game ID',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: textColor,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _gameIdController,
            decoration: InputDecoration(
              hintText: 'Enter your Game ID',
              hintStyle: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[400]),
              filled: true,
              fillColor: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF2A2C36)
                  : Colors.grey[100],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your Game ID';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // User ID field (optional)
          Row(
            children: [
              Text(
                'User ID',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: textColor,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '(Optional)',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _userIdController,
            decoration: InputDecoration(
              hintText: 'Enter your User ID (Optional)',
              hintStyle: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[400]),
              filled: true,
              fillColor: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF2A2C36)
                  : Colors.grey[100],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            ),
            // No validation for optional field
            validator: (value) {
              // User ID is optional, so no validation needed
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodList(Color? cardColor, Color textColor, Color accentColor) {
    return Column(
      children: _paymentMethods.asMap().entries.map((entry) {
        final index = entry.key;
        final method = entry.value;
        final isSelected = index == _selectedPaymentMethod;
        
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedPaymentMethod = index;
            });
          },
          child: Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: cardColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? accentColor : Colors.transparent,
                width: 2,
              ),
            ),
            child: Row(
              children: [
                Radio(
                  value: index,
                  groupValue: _selectedPaymentMethod,
                  activeColor: accentColor,
                  onChanged: (value) {
                    setState(() {
                      _selectedPaymentMethod = value as int;
                    });
                  },
                ),
                const SizedBox(width: 8),
                Icon(
                  method['icon'],
                  size: 28,
                  color: isSelected ? accentColor : Colors.grey[500],
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        method['title'],
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: textColor,
                        ),
                      ),
                      Text(
                        method['subtitle'],
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildOrderSummary(Color? cardColor, Color textColor, Color accentColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildSummaryRow(
            label: 'Subtotal',
            value: '\$${(widget.totalAmount - 5.99 - (widget.totalAmount * 0.07)).toStringAsFixed(2)}',
            textColor: textColor,
          ),
          const SizedBox(height: 8),
          _buildSummaryRow(
            label: 'Shipping',
            value: '\$5.99',
            textColor: textColor,
          ),
          const SizedBox(height: 8),
          _buildSummaryRow(
            label: 'Tax',
            value: '\$${(widget.totalAmount * 0.07).toStringAsFixed(2)}',
            textColor: textColor,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Divider(color: Colors.grey[500]),
          ),
          _buildSummaryRow(
            label: 'Total',
            value: '\$${widget.totalAmount.toStringAsFixed(2)}',
            textColor: textColor,
            isBold: true,
            valueColor: accentColor,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow({
    required String label,
    required String value,
    required Color textColor,
    bool isBold = false,
    Color? valueColor,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: isBold ? FontWeight.w600 : FontWeight.normal,
            color: textColor,
          ),
        ),
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: isBold ? 16 : 14,
            fontWeight: isBold ? FontWeight.w600 : FontWeight.normal,
            color: valueColor ?? textColor,
          ),
        ),
      ],
    );
  }

  Future<void> _placeOrder() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });
      
      final shopProvider = Provider.of<ShopProvider>(context, listen: false);
      bool allPurchasesSuccessful = true;
      String? firstErrorMessage;

      try {
        for (var item in widget.cartItems) {
          // IMPORTANT ASSUMPTION:
          // item['id'] is the actual backend itemId
          // item['itemType'] is the correct itemType string
          // item['quantity'] is the quantity
          // These need to be ensured when items are added to CartService
          final String itemId = item['id'] as String? ?? ''; 
          final String itemType = item['itemType'] as String? ?? ''; 
          final int quantity = item['quantity'] as int? ?? 0;
          final double unitPrice = (item['price'] as num?)?.toDouble() ?? 0.0;

          if (itemId.isEmpty || itemType.isEmpty || quantity == 0) {
            print('CheckoutScreen: Missing itemId, itemType, or quantity for item: ${item['title']}');
            allPurchasesSuccessful = false;
            firstErrorMessage = 'Missing item details for ${item['title']}. Please ensure item data is correct.';
            break; 
          }
          
          print('Attempting to purchase: itemId=$itemId, itemType=$itemType, quantity=$quantity');

          // Calculate total price for this item line
          final double totalPriceForItem = unitPrice * quantity;

          print('Attempting to purchase: itemId=$itemId, itemType=$itemType, quantity=$quantity, unitPrice=$unitPrice, totalPriceForItem=$totalPriceForItem');

          bool success = await shopProvider.purchaseItem(
            itemId: itemId,
            itemType: itemType,
            quantity: quantity,
            price: totalPriceForItem, // Pass the total price for this line item
          );
          if (!success) {
            allPurchasesSuccessful = false;
            firstErrorMessage = shopProvider.errorMessage ?? 'Failed to purchase ${item['title']}.';
            break; // Stop on first failure
          }
        }

        if (allPurchasesSuccessful) {
          _cartService.clear(); // Clear cart on full success
          _showOrderConfirmation();
        } else {
          setState(() {
            _isProcessing = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(firstErrorMessage ?? 'An error occurred during purchase.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        setState(() {
          _isProcessing = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An unexpected error occurred: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildProcessingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFFA500)),
          ),
          const SizedBox(height: 24),
          Text(
            'Processing your order...',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  void _showOrderConfirmation() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF1E1F26)
            : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle,
              color: Color(0xFF4CAF50),
              size: 80,
            ),
            const SizedBox(height: 16),
            Text(
              'Order Placed Successfully!',
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Your order has been placed successfully. You will receive a confirmation email shortly.',
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Order #WZ-${DateTime.now().millisecondsSinceEpoch.toString().substring(5, 13)}',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFFFFA500),
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFFA500),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: () {
                  Navigator.of(context).popUntil((route) => route.isFirst);
                },
                child: Text(
                  'Back to Shop',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
