import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wiggyz_app/screens/add_money_screen.dart';
import 'package:wiggyz_app/screens/withdraw_screen.dart';
import 'package:wiggyz_app/screens/notification_screen.dart';
import 'package:wiggyz_app/screens/transaction_history_screen.dart';
import 'package:provider/provider.dart';
import 'package:wiggyz_app/features/wallet/providers/wallet_provider.dart';
import 'package:wiggyz_app/services/referral_sharing_service.dart';
import 'package:wiggyz_app/models/referral_models.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  final ReferralSharingService _referralSharingService = ReferralSharingService();
  bool _isReferralLoading = false;

  @override
  void initState() {
    super.initState();
    // Fetch initial wallet data when the screen loads
    // Use a post-frame callback to ensure Provider is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final walletProvider = Provider.of<WalletProvider>(
        context,
        listen: false,
      );
      walletProvider.fetchWalletDetails();
      walletProvider
          .fetchWalletTransactions(page: 1, limit: 20); // Fetch more transactions for recent display
    });
  }

  // Mock data has been removed and functionality is now driven by WalletProvider.

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final walletProvider = Provider.of<WalletProvider>(context);

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.grey[100],
      appBar: AppBar(
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
            ),
          ),
        ),
        backgroundColor: Colors.transparent,
        title: Text(
          'My Wallet',
          style: GoogleFonts.poppins(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: Icon(Icons.notifications_outlined, color: Colors.black),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NotificationScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        color: const Color(0xFFFFCC00),
        onRefresh: () async {
          print('🔄 DEBUG: Refreshing wallet data and transactions');
          await walletProvider.fetchWalletDetails();
          await walletProvider.fetchWalletTransactions(
            page: 1,
            limit: 20,
          ); // Reset to first page on refresh with more transactions
          print('🔄 DEBUG: Refresh completed');
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildBalanceCard(isDarkMode, walletProvider),
                const SizedBox(height: 24),
                _buildActionButtons(isDarkMode),
                const SizedBox(height: 24),
                _buildRewardCard(isDarkMode),
                const SizedBox(height: 24),
                _buildTransactionsList(isDarkMode, walletProvider),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Header is now implemented directly in the AppBar

  Widget _buildBalanceCard(bool isDarkMode, WalletProvider walletProvider) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors:
              isDarkMode
                  ? [
                    const Color(
                      0xFF1E1F26,
                    ), // Dark background matching app theme
                    const Color(0xFF2A2C36), // Slightly lighter dark
                  ]
                  : [
                    const Color(0xFFE0F2F1), // Very light green
                    const Color(0xFFA5D6A7), // Light green
                  ],
        ),
        border: Border.all(
          color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF2E7D32),
          width: isDarkMode ? 1.5 : 1.0,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color:
                isDarkMode
                    ? const Color(0xFFD4AF37).withOpacity(0.2)
                    : const Color(0xFF81C784).withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Balance',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.verified,
                      size: 16,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Verified',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          walletProvider.isLoading && walletProvider.walletData == null
              ? const CircularProgressIndicator(color: Color(0xFFFFCC00))
              : walletProvider.errorMessage != null &&
                  walletProvider.walletData == null
              ? Text(
                'Error: ${walletProvider.errorMessage}',
                style: GoogleFonts.poppins(color: Colors.red, fontSize: 16),
              )
              : Text(
                '₹${walletProvider.walletData?['balance']?.toStringAsFixed(2) ?? '0.00'}',
                style: GoogleFonts.poppins(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color:
                      isDarkMode
                          ? Color(0xFFD4AF37)
                          : Color(
                            0xFF2E7D32,
                          ), // Gold in dark mode, Green in light mode
                ),
              ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.update,
                size: 16,
                color:
                    isDarkMode
                        ? Colors.white.withOpacity(0.9)
                        : Colors.black.withOpacity(0.7),
              ),
              const SizedBox(width: 4),
              Text(
                'Last updated: Today, 11:25 AM',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color:
                      isDarkMode
                          ? Colors.white.withOpacity(0.9)
                          : Colors.black.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(bool isDarkMode) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(
          isDarkMode,
          'Add Money',
          Icons.add_circle_outline,
          () async {
            final result = await Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const AddMoneyScreen()),
            );

            // Refresh wallet data and transactions when returning from add money screen
            if (result == true || result == null) {
              print('💰 DEBUG: Refreshing wallet after add money screen');
              final walletProvider = Provider.of<WalletProvider>(context, listen: false);
              await walletProvider.fetchWalletDetails();
              await walletProvider.fetchWalletTransactions(page: 1, limit: 20);
              print('💰 DEBUG: Wallet refresh completed after add money');
            }
          },
        ),
        _buildActionButton(
          isDarkMode,
          'Withdraw',
          Icons.monetization_on_outlined,
          () async {
            final result = await Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const WithdrawScreen()),
            );

            // Refresh wallet data and transactions when returning from withdrawal screen
            if (result == true || result == null) {
              print('💸 DEBUG: Refreshing wallet after withdrawal screen');
              final walletProvider = Provider.of<WalletProvider>(context, listen: false);
              await walletProvider.fetchWalletDetails();
              await walletProvider.fetchWalletTransactions(page: 1, limit: 20);
              print('💸 DEBUG: Wallet refresh completed after withdrawal');
            }
          },
        ),
        _buildActionButton(isDarkMode, 'History', Icons.history, () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const TransactionHistoryScreen(),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildActionButton(
    bool isDarkMode,
    String label,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(icon, color: const Color(0xFFFFCC00), size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRewardCard(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color:
              isDarkMode
                  ? const Color(0xFFFFCC00).withOpacity(0.3)
                  : const Color(0xFFFFECB3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: const Color(0xFFFFCC00).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.card_giftcard,
              color: Color(0xFFFFCC00),
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Refer & Earn Rewards',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Invite friends and earn ₹50 for each signup',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: _isReferralLoading ? null : _handleReferralShare,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFFCC00),
              foregroundColor: Colors.black,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isReferralLoading
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                    ),
                  )
                : Text(
                    'Refer',
                    style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(
    bool isDarkMode,
    WalletProvider walletProvider,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Transactions',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const TransactionHistoryScreen(),
                  ),
                );
              },
              child: Text(
                'See All',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFFFFCC00),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        walletProvider.isLoading && walletProvider.transactions.isEmpty
            ? const Center(
              child: CircularProgressIndicator(color: Color(0xFFFFCC00)),
            )
            : walletProvider.errorMessage != null &&
                walletProvider.transactions.isEmpty
            ? Center(
              child: Text(
                'Error: ${walletProvider.errorMessage}',
                style: GoogleFonts.poppins(color: Colors.red),
              ),
            )
            : walletProvider.transactions.isEmpty
            ? Center(
              child: Text(
                'No transactions yet.',
                style: GoogleFonts.poppins(
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
              ),
            )
            : _buildRecentTransactionsList(isDarkMode, walletProvider),
      ],
    );
  }

  Widget _buildRecentTransactionsList(bool isDarkMode, WalletProvider walletProvider) {

    // Filter transactions to show relevant ones for recent transactions section
    final relevantTransactions = walletProvider.transactions
        .where((transaction) {
          final Map<String, dynamic> txData = transaction as Map<String, dynamic>;
          final String status = txData['status']?.toString().toLowerCase().trim() ?? '';
          final String type = txData['type']?.toString().toLowerCase().trim() ?? '';

          print('🔍 DEBUG: Checking transaction ${txData['id']} with type "$type" and status "$status"');

          // Show completed transactions (deposits and other completed transactions)
          if (status == 'completed' || status == 'success') {
            print('🔍 DEBUG: Transaction ${txData['id']} is completed');
            return true;
          }

          // Show pending withdrawal transactions so users can see their withdrawal requests
          if (type == 'withdrawal' && status == 'pending') {
            print('🔍 DEBUG: Transaction ${txData['id']} is pending withdrawal');
            return true;
          }

          // Show processing transactions (add money in progress)
          if ((type == 'deposit' || type == 'withdrawal') && status == 'processing') {
            print('🔍 DEBUG: Transaction ${txData['id']} is processing');
            return true;
          }

          print('🔍 DEBUG: Transaction ${txData['id']} filtered out');
          return false;
        })
        .take(5) // Limit to 5 most recent relevant transactions
        .toList();

    print('🔍 DEBUG: Relevant transactions found: ${relevantTransactions.length}');

    if (relevantTransactions.isEmpty) {
      // If no relevant transactions, show all recent transactions regardless of status
      final recentTransactions = walletProvider.transactions.take(5).toList();

      if (recentTransactions.isEmpty) {
        return Center(
          child: Column(
            children: [
              Icon(
                Icons.receipt_long_outlined,
                size: 48,
                color: isDarkMode ? Colors.grey[600] : Colors.grey[400],
              ),
              const SizedBox(height: 12),
              Text(
                'No transactions yet.',
                style: GoogleFonts.poppins(
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Your recent transactions will appear here.',
                style: GoogleFonts.poppins(
                  color: isDarkMode ? Colors.grey[600] : Colors.grey[500],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        );
      } else {
        // Show recent transactions with status indicators
        return _buildTransactionItems(isDarkMode, recentTransactions, showStatus: true);
      }
    }

    // Show relevant transactions with status indicators for pending/processing transactions
    return _buildTransactionItems(isDarkMode, relevantTransactions, showStatus: true);
  }

  Widget _buildTransactionItems(bool isDarkMode, List<dynamic> transactions, {bool showStatus = false}) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: transactions.length,
      separatorBuilder: (context, index) => Divider(
        color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
        height: 1,
      ),
      itemBuilder: (context, index) {
        final transactionData = transactions[index] as Map<String, dynamic>;

        // Determine if transaction is debit using enhanced logic
        final isDebit = _isDebitTransaction(transactionData);
        final displayAmount = _getTransactionDisplayAmount(transactionData);
        final transactionColor = _getTransactionColor(isDebit);
        final iconBackgroundColor = _getTransactionBackgroundColor(isDebit);

        // Create transaction model with proper color coding
        // Use type-based title to match Transaction History screen (not description)
        final transaction = TransactionModel(
          title: _getTransactionTitle(transactionData['type']),  // Always use type-based title
          amount: displayAmount,
          date: transactionData['created_at'] != null
              ? _formatDate(transactionData['created_at'])
              : 'N/A',
          isDebit: isDebit,
          category: _getTransactionCategory(transactionData['type']),
          iconData: _getIconForTransactionType(
            transactionData['type'] ?? '',
            transactionData['category'] ?? '',
          ),
          amountColor: transactionColor,
          iconBackgroundColor: iconBackgroundColor,
        );
        return _buildTransactionItem(transaction, isDarkMode, showStatus: showStatus, rawData: transactionData);
      },
    );
  }

  Widget _buildTransactionItem(TransactionModel transaction, bool isDarkMode, {bool showStatus = false, Map<String, dynamic>? rawData}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: transaction.iconBackgroundColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              transaction.iconData,
              color: transaction.amountColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.title,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        transaction.category,
                        style: GoogleFonts.poppins(
                          fontSize: 10,
                          color:
                              isDarkMode ? Colors.grey[400] : Colors.grey[700],
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      transaction.date,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                      ),
                    ),
                    if (showStatus && rawData != null) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getStatusColor(rawData!['status']).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: _getStatusColor(rawData!['status']).withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          (rawData!['status'] ?? 'unknown').toString().toUpperCase(),
                          style: GoogleFonts.poppins(
                            fontSize: 9,
                            fontWeight: FontWeight.w600,
                            color: _getStatusColor(rawData!['status']),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          Text(
            transaction.amount,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: transaction.amountColor,
            ),
          ),
        ],
      ),
    );
  } // End of _buildTransactionItem

  // Helper function to format date string (e.g., from ISO 8601)
  String _formatDate(String dateString) {
    try {
      final dateTime = DateTime.parse(dateString);
      // Using a simple format, consider intl package for more complex formatting
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } catch (e) {
      return dateString; // Return original if parsing fails
    }
  }

  // Helper function to get status color
  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
        return Colors.green;
      case 'pending':
      case 'processing':
        return Colors.orange;
      case 'failed':
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // Helper function to get an icon based on transaction type/category
  IconData _getIconForTransactionType(String type, String category) {
    // Customize this based on actual transaction types/categories from backend
    final lowerType = type.toLowerCase();
    final lowerCategory = category.toLowerCase();

    if (lowerCategory.contains('tournament')) {
      return Icons.videogame_asset;
    }
    if (lowerType == 'deposit' ||
        lowerCategory.contains('deposit') ||
        lowerCategory.contains('added funds')) {
      return Icons.account_balance_wallet;
    }
    if (lowerCategory.contains('prize') ||
        lowerCategory.contains('winnings')) {
      return Icons.emoji_events;
    }
    if (lowerCategory.contains('match')) return Icons.sports_esports;
    if (lowerCategory.contains('purchase')) return Icons.shopping_cart;
    if (lowerType == 'withdrawal') return Icons.money_off;
    if (lowerType == 'refund') return Icons.replay;
    if (lowerType == 'reward') return Icons.card_giftcard;
    return Icons.receipt_long; // Default icon
  }

  // Helper function to determine if a transaction is a debit (money going out)
  bool _isDebitTransaction(Map<String, dynamic> transactionData) {
    final type = transactionData['type']?.toString().toLowerCase() ?? '';
    final amount = transactionData['amount'] ?? 0.0;

    // Backend transaction types that represent money going out (debits)
    const debitTypes = ['withdrawal', 'purchase', 'match_fee'];

    // If amount is negative, it's a debit regardless of type
    if (amount < 0) return true;

    // Check if transaction type is a debit type
    return debitTypes.contains(type);
  }

  // Helper function to get transaction display amount with proper sign
  String _getTransactionDisplayAmount(Map<String, dynamic> transactionData) {
    final amount = transactionData['amount'] ?? 0.0;
    final isDebit = _isDebitTransaction(transactionData);

    // Convert to absolute value for display
    final absAmount = amount.abs();

    // Format with proper sign and currency
    return isDebit
        ? '-₹${absAmount.toStringAsFixed(2)}'
        : '+₹${absAmount.toStringAsFixed(2)}';
  }

  // Helper function to get transaction color based on debit/credit
  Color _getTransactionColor(bool isDebit) {
    return isDebit ? Colors.red : Colors.green;
  }

  // Helper function to get background color for transaction icon
  Color _getTransactionBackgroundColor(bool isDebit) {
    return isDebit
        ? Colors.red.withOpacity(0.1)
        : Colors.green.withOpacity(0.1);
  }

  // Helper function to get user-friendly transaction title
  // Updated to match Transaction History screen titles
  String _getTransactionTitle(String? type) {
    switch (type?.toLowerCase()) {
      case 'deposit':
        return 'Added Funds';  // Changed from 'Wallet Top-up' to match Transaction History
      case 'withdrawal':
        return 'Withdrawal';   // Changed from 'Money Withdrawn' to match Transaction History
      case 'purchase':
      case 'tournament_fee':
      case 'tournament_creation':
      case 'match_fee':
        return 'Tournament Entry';  // Changed from 'Match Entry Fee' to match Transaction History
      case 'reward':
      case 'prize':
      case 'prize_payout':
        return 'Tournament Winnings';  // Changed from 'Prize Winnings' to match Transaction History
      case 'refund':
        return 'Refund Received';
      default:
        return 'Transaction';
    }
  }

  // Helper function to get transaction category
  // Updated to match Transaction History screen categories
  String _getTransactionCategory(String? type) {
    switch (type?.toLowerCase()) {
      case 'deposit':
        return 'Deposit';  // Changed from 'Top-up' to match Transaction History
      case 'withdrawal':
        return 'Withdrawal';
      case 'purchase':
      case 'tournament_fee':
      case 'tournament_creation':
      case 'match_fee':
        return 'Tournament';  // Changed from 'Gaming' to match Transaction History
      case 'reward':
      case 'prize':
      case 'prize_payout':
        return 'Prize';  // Changed from 'Winnings' to match Transaction History
      case 'refund':
        return 'Refund';
      default:
        return 'Other';  // Changed from 'General' to match Transaction History
    }
  }

  /// Handle referral sharing when the "Refer" button is pressed
  Future<void> _handleReferralShare() async {
    if (_isReferralLoading) return;

    setState(() {
      _isReferralLoading = true;
    });

    try {
      // Show a bottom sheet with sharing options
      await _showReferralShareBottomSheet();
    } catch (e) {
      // Show error message
      if (mounted) {
        _showErrorSnackBar('Failed to load referral information. Please try again.');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isReferralLoading = false;
        });
      }
    }
  }

  /// Show bottom sheet with referral sharing options
  Future<void> _showReferralShareBottomSheet() async {
    try {
      // Get referral content
      final shareContent = await _referralSharingService.getReferralShareContent();

      if (!mounted) return;

      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => _buildReferralShareBottomSheet(shareContent),
      );
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to load referral code. Please try again.');
      }
    }
  }

  /// Build the referral sharing bottom sheet
  Widget _buildReferralShareBottomSheet(ReferralShareContent shareContent) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1A1A1A) : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Title
          Text(
            'Share Your Referral Code',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 8),

          // Subtitle
          Text(
            'Invite friends and earn 25 points each when they sign up!',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // Referral code display
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFFFCC00).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFFFCC00).withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Your Referral Code',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        shareContent.referralCode,
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => _copyReferralCode(shareContent.referralCode),
                  icon: const Icon(Icons.copy),
                  style: IconButton.styleFrom(
                    backgroundColor: const Color(0xFFFFCC00),
                    foregroundColor: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _copyFullMessage(shareContent),
                  icon: const Icon(Icons.copy),
                  label: Text(
                    'Copy Message',
                    style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFFFFCC00),
                    side: const BorderSide(color: Color(0xFFFFCC00)),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _shareReferral(shareContent),
                  icon: const Icon(Icons.share),
                  label: Text(
                    'Share Now',
                    style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFFCC00),
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),

          // Bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  /// Copy referral code to clipboard
  Future<void> _copyReferralCode(String referralCode) async {
    try {
      await Clipboard.setData(ClipboardData(text: referralCode));
      if (mounted) {
        _showSuccessSnackBar('Referral code copied to clipboard!');
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to copy referral code');
      }
    }
  }

  /// Copy full referral message to clipboard
  Future<void> _copyFullMessage(ReferralShareContent shareContent) async {
    try {
      final success = await _referralSharingService.copyReferralMessageToClipboard();
      if (mounted) {
        if (success) {
          Navigator.pop(context); // Close bottom sheet
          _showSuccessSnackBar('Referral message copied to clipboard!');
        } else {
          _showErrorSnackBar('Failed to copy message');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to copy message');
      }
    }
  }

  /// Share referral through available apps
  Future<void> _shareReferral(ReferralShareContent shareContent) async {
    try {
      Navigator.pop(context); // Close bottom sheet first

      final result = await _referralSharingService.shareReferral();

      if (mounted) {
        switch (result.status) {
          case ReferralShareStatus.success:
            _showSuccessSnackBar('Referral shared successfully!');
            break;
          case ReferralShareStatus.cancelled:
            // User cancelled, no need to show message
            break;
          case ReferralShareStatus.unavailable:
            _showErrorSnackBar('Sharing is not available on this device');
            break;
          case ReferralShareStatus.error:
            _showErrorSnackBar(result.errorMessage ?? 'Failed to share referral');
            break;
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to share referral');
      }
    }
  }

  /// Show success snackbar
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
} // End of _WalletScreenState

class TransactionModel {
  final String title;
  final String amount;
  final String date;
  final bool isDebit;
  final String category;
  final IconData iconData;
  final Color amountColor;
  final Color iconBackgroundColor;

  TransactionModel({
    required this.title,
    required this.amount,
    required this.date,
    required this.isDebit,
    required this.category,
    required this.iconData,
    required this.amountColor,
    required this.iconBackgroundColor,
  });
}
