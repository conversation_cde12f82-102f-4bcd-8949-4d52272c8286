import 'package:http/http.dart' as http;

/// Simple test script to verify the claim API endpoint
void main() async {
  print('🧪 Testing Daily Reward Claim API...');
  
  // Test configuration
  const apiBaseUrl = 'http://127.0.0.1:5000/api/v1';
  const claimEndpoint = '$apiBaseUrl/rewards/claim-daily';
  
  print('🌐 API Base URL: $apiBaseUrl');
  print('🎯 Claim Endpoint: $claimEndpoint');
  
  // Test 1: Check if the endpoint exists (without auth)
  print('\n📡 Test 1: Testing endpoint connectivity...');
  try {
    final response = await http.post(
      Uri.parse(claimEndpoint),
      headers: {
        'Content-Type': 'application/json',
      },
    );
    
    print('✅ Endpoint reachable!');
    print('📊 Status Code: ${response.statusCode}');
    print('📄 Response Body: ${response.body}');
    print('📋 Response Headers: ${response.headers}');
    
    if (response.statusCode == 401) {
      print('🔐 Expected 401 - Authentication required (endpoint working)');
    } else if (response.statusCode == 404) {
      print('❌ 404 - Endpoint not found (route issue)');
    } else {
      print('ℹ️ Unexpected status code: ${response.statusCode}');
    }
    
  } catch (e) {
    print('❌ Network error: $e');
    print('🔍 This suggests the backend is not running or not accessible');
  }
  
  // Test 2: Check daily-status endpoint (should work)
  print('\n📡 Test 2: Testing daily-status endpoint...');
  try {
    final response = await http.get(
      Uri.parse('$apiBaseUrl/rewards/daily-status'),
      headers: {
        'Content-Type': 'application/json',
      },
    );
    
    print('✅ Daily-status endpoint reachable!');
    print('📊 Status Code: ${response.statusCode}');
    print('📄 Response Body Length: ${response.body.length}');
    
    if (response.statusCode == 401) {
      print('🔐 Expected 401 - Authentication required (endpoint working)');
    } else if (response.statusCode == 200) {
      print('✅ 200 - Endpoint working (unexpected without auth)');
    } else {
      print('ℹ️ Status code: ${response.statusCode}');
    }
    
  } catch (e) {
    print('❌ Network error: $e');
  }
  
  // Test 3: Check base API health
  print('\n📡 Test 3: Testing base API health...');
  try {
    final response = await http.get(
      Uri.parse('$apiBaseUrl/health'),
      headers: {
        'Content-Type': 'application/json',
      },
    );
    
    print('✅ Health endpoint reachable!');
    print('📊 Status Code: ${response.statusCode}');
    print('📄 Response Body: ${response.body}');
    
  } catch (e) {
    print('❌ Health endpoint error: $e');
  }
  
  print('\n🏁 API connectivity test completed!');
}
