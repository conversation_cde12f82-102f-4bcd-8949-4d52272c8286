import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:wiggyz_app/services/razorpay_service.dart';

/// Test script to verify payment system fixes
/// Run this to test the Razorpay service improvements
void main() {
  runApp(PaymentTestApp());
}

class PaymentTestApp extends StatelessWidget {
  const PaymentTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Payment System Test',
      theme: ThemeData(
        primarySwatch: Colors.orange,
      ),
      home: PaymentTestScreen(),
    );
  }
}

class PaymentTestScreen extends StatefulWidget {
  const PaymentTestScreen({super.key});

  @override
  _PaymentTestScreenState createState() => _PaymentTestScreenState();
}

class _PaymentTestScreenState extends State<PaymentTestScreen> {
  late RazorpayService _razorpayService;
  String _status = 'Ready to test';
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeRazorpay();
  }

  void _initializeRazorpay() {
    try {
      _razorpayService = RazorpayService();
      _razorpayService.initialize();
      
      setState(() {
        _isInitialized = true;
        if (kIsWeb) {
          _status = 'Web platform detected - Razorpay not supported';
        } else {
          _status = 'Razorpay initialized successfully';
        }
      });
    } catch (e) {
      setState(() {
        _status = 'Error initializing Razorpay: $e';
      });
    }
  }

  void _testPayment() {
    if (kIsWeb) {
      setState(() {
        _status = 'Web platform - Payment not supported (this is expected)';
      });
      return;
    }

    setState(() {
      _status = 'Testing payment...';
    });

    // Test payment with dummy data
    _razorpayService.startPayment(
      orderId: 'test_order_${DateTime.now().millisecondsSinceEpoch}',
      keyId: 'rzp_test_iVqMylHzm5WcBc', // Test key
      amount: 100.0, // ₹100
      currency: 'INR',
      name: 'Test User',
      description: 'Test Payment',
      userEmail: '<EMAIL>',
      userPhone: '9999999999',
      userName: 'Test User',
      onSuccess: (response) {
        setState(() {
          _status = 'Payment Success: ${response.paymentId}';
        });
      },
      onError: (response) {
        setState(() {
          _status = 'Payment Error: ${response.message}';
        });
      },
    );
  }

  @override
  void dispose() {
    _razorpayService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Payment System Test'),
        backgroundColor: Color(0xFFFFCC00),
        foregroundColor: Colors.black,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Platform Information',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('Platform: ${kIsWeb ? "Web" : "Mobile"}'),
                    Text('Razorpay Support: ${kIsWeb ? "Not Supported" : "Supported"}'),
                    Text('Initialization: ${_isInitialized ? "Success" : "Failed"}'),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Status',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      _status,
                      style: TextStyle(
                        color: _status.contains('Error') ? Colors.red : 
                               _status.contains('Success') ? Colors.green : 
                               Colors.blue,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _testPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(0xFFFFCC00),
                  foregroundColor: Colors.black,
                ),
                child: Text(
                  kIsWeb ? 'Test Web Error Handling' : 'Test Payment',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Expected Behavior:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              kIsWeb 
                ? '• Web platform should show "not supported" message\n'
                  '• No crashes or plugin exceptions\n'
                  '• User-friendly error handling'
                : '• Mobile platform should initialize Razorpay\n'
                  '• Payment gateway should open\n'
                  '• Proper error handling for failures',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
