/**
 * Logger utility for consistent logging throughout the application
 * Uses different log levels based on environment configuration
 */

import { config } from '../config';

// Define log levels with numeric priority
const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
};

// Determine current log level from config
const currentLogLevel = config.logging?.level || 'info';
const currentLevelValue = LOG_LEVELS[currentLogLevel as keyof typeof LOG_LEVELS] || LOG_LEVELS.info;

/**
 * Format a log message with timestamp and metadata
 */
const formatLogMessage = (level: string, message: string, metadata?: Record<string, any>): string => {
  const timestamp = new Date().toISOString();
  let formattedMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;

  if (metadata && Object.keys(metadata).length > 0) {
    const metadataStr = JSON.stringify(metadata, null, 2);
    formattedMessage += ` ${metadataStr}`;
  }

  return formattedMessage;
};

/**
 * Check if a log level should be printed based on configured level
 */
const shouldLog = (level: keyof typeof LOG_LEVELS): boolean => {
  return LOG_LEVELS[level] <= currentLevelValue;
};

/**
 * Logger with different severity levels
 */
export const logger = {
  error: (message: string, metadata?: Record<string, any>): void => {
    if (shouldLog('error')) {
      console.error(formatLogMessage('error', message, metadata));
    }
  },

  warn: (message: string, metadata?: Record<string, any>): void => {
    if (shouldLog('warn')) {
      console.warn(formatLogMessage('warn', message, metadata));
    }
  },

  info: (message: string, metadata?: Record<string, any>): void => {
    if (shouldLog('info')) {
      console.info(formatLogMessage('info', message, metadata));
    }
  },

  debug: (message: string, metadata?: Record<string, any>): void => {
    if (shouldLog('debug')) {
      // Only log debug in non-production
      if (process.env.NODE_ENV !== 'production' && shouldLog('debug')) {
        console.log(formatLogMessage('debug', message, metadata));
      }
    }
  },

  // Method to log security events specifically
  security: (message: string, metadata?: Record<string, any>): void => {
    // Always log security events regardless of log level
    console.warn(formatLogMessage('SECURITY', message, metadata));

    // In a production system, you might want to log these to a separate file
    // or security monitoring system
  }
};
