import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/reward_provider.dart';
import '../theme_provider.dart';
import '../models/loyalty_models.dart';
import '../widgets/shared/golden_app_bar.dart';

class LoyaltyRewardsInfoScreen extends StatefulWidget {
  const LoyaltyRewardsInfoScreen({super.key});

  @override
  State<LoyaltyRewardsInfoScreen> createState() => _LoyaltyRewardsInfoScreenState();
}

class _LoyaltyRewardsInfoScreenState extends State<LoyaltyRewardsInfoScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch loyalty data if not already loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final rewardProvider = context.read<RewardProvider>();
      if (rewardProvider.loyaltyTiers.isEmpty) {
        rewardProvider.fetchLoyaltyStatus();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      appBar: GoldenAppBar(
        title: 'Loyalty Rewards',
        onBackPressed: () => Navigator.pop(context),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<RewardProvider>().fetchLoyaltyStatus();
            },
          ),
        ],
      ),
      body: Consumer<RewardProvider>(
        builder: (context, rewardProvider, child) {
          if (rewardProvider.isLoyaltyLoading) {
            return _buildLoadingState(isDarkMode);
          }

          if (rewardProvider.loyaltyError != null) {
            return _buildErrorState(isDarkMode, rewardProvider.loyaltyError!);
          }

          return _buildRewardsContent(isDarkMode, rewardProvider);
        },
      ),
    );
  }

  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFFCC00)),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading loyalty rewards...',
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(bool isDarkMode, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: isDarkMode ? Colors.red[300] : Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading Rewards',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context.read<RewardProvider>().fetchLoyaltyStatus();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFFCC00),
                foregroundColor: Colors.black,
              ),
              child: Text(
                'Retry',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRewardsContent(bool isDarkMode, RewardProvider rewardProvider) {
    final userPoints = rewardProvider.loyaltyStatus?.points ?? 0;
    final sortedTiers = List<LoyaltyTierModel>.from(rewardProvider.loyaltyTiers)
      ..sort((a, b) => a.minPoints.compareTo(b.minPoints));

    return RefreshIndicator(
      onRefresh: () async {
        await context.read<RewardProvider>().fetchLoyaltyStatus();
      },
      color: const Color(0xFFFFCC00),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeaderCard(isDarkMode, userPoints),
            const SizedBox(height: 24),
            _buildTiersSection(isDarkMode, sortedTiers, userPoints),
            const SizedBox(height: 24),
            _buildBenefitsOverview(isDarkMode),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(bool isDarkMode, int userPoints) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFFFFCC00),
            const Color(0xFFFFCC00).withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFFCC00).withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.emoji_events,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Your Loyalty Journey',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'Discover all the amazing rewards waiting for you',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.stars,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Current Points: $userPoints',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTiersSection(bool isDarkMode, List<LoyaltyTierModel> tiers, int userPoints) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Loyalty Tiers',
          style: GoogleFonts.poppins(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Progress through tiers to unlock exclusive rewards and benefits',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
        const SizedBox(height: 16),
        ...tiers.asMap().entries.map((entry) {
          final index = entry.key;
          final tier = entry.value;
          final isAchieved = userPoints >= tier.minPoints;
          final isNext = !isAchieved && (index == 0 || userPoints >= tiers[index - 1].minPoints);
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildTierCard(isDarkMode, tier, isAchieved, isNext, userPoints),
          );
        }),
      ],
    );
  }

  Widget _buildTierCard(bool isDarkMode, LoyaltyTierModel tier, bool isAchieved, bool isNext, int userPoints) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isAchieved
              ? const Color(0xFFFFCC00)
              : isNext
                  ? const Color(0xFFFFCC00).withOpacity(0.5)
                  : (isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
          width: isAchieved ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isAchieved
                ? const Color(0xFFFFCC00).withOpacity(0.2)
                : Colors.black.withOpacity(0.1),
            blurRadius: isAchieved ? 12 : 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isAchieved
                      ? const Color(0xFFFFCC00).withOpacity(0.2)
                      : (isDarkMode ? Colors.grey[700] : Colors.grey[200]),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: tier.emojiIcon != null
                    ? Text(
                        tier.emojiIcon!,
                        style: const TextStyle(fontSize: 24),
                      )
                    : Icon(
                        tier.displayIcon,
                        color: isAchieved
                            ? const Color(0xFFFFCC00)
                            : (isDarkMode ? Colors.grey[400] : Colors.grey[600]),
                        size: 24,
                      ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          tier.name,
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: isAchieved
                                ? const Color(0xFFFFCC00)
                                : (isDarkMode ? Colors.white : Colors.black),
                          ),
                        ),
                        const SizedBox(width: 8),
                        if (isAchieved)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFCC00),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'ACHIEVED',
                              style: GoogleFonts.poppins(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                          )
                        else if (isNext)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFCC00).withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: const Color(0xFFFFCC00), width: 1),
                            ),
                            child: Text(
                              'NEXT',
                              style: GoogleFonts.poppins(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFFFFCC00),
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${tier.minPoints} points required',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                    if (!isAchieved && isNext)
                      Text(
                        '${tier.minPoints - userPoints} points to go',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFFFFCC00),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          if (tier.description != null) ...[
            const SizedBox(height: 12),
            Text(
              tier.description!,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
              ),
            ),
          ],
          const SizedBox(height: 16),
          Text(
            'Benefits:',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          ...tier.benefits.map((benefit) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: isAchieved
                        ? const Color(0xFFFFCC00).withOpacity(0.2)
                        : (isDarkMode ? Colors.grey[700] : Colors.grey[200]),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    _getBenefitIcon(benefit.type),
                    color: isAchieved
                        ? const Color(0xFFFFCC00)
                        : (isDarkMode ? Colors.grey[400] : Colors.grey[600]),
                    size: 14,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    benefit.description,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: isAchieved
                          ? (isDarkMode ? Colors.white : Colors.black)
                          : (isDarkMode ? Colors.grey[400] : Colors.grey[600]),
                      fontWeight: isAchieved ? FontWeight.w500 : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildBenefitsOverview(bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: const Color(0xFFFFCC00),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'How to Earn Points',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildEarnPointsItem(isDarkMode, Icons.login, 'Daily Login', 'Login daily to earn points'),
          _buildEarnPointsItem(isDarkMode, Icons.sports_esports, 'Play Matches', 'Participate in matches and tournaments'),
          _buildEarnPointsItem(isDarkMode, Icons.shopping_cart, 'Shop Purchases', 'Buy items from the shop'),
          _buildEarnPointsItem(isDarkMode, Icons.share, 'Refer Friends', 'Invite friends to join WiggyZ'),
        ],
      ),
    );
  }

  Widget _buildEarnPointsItem(bool isDarkMode, IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFFFCC00).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: const Color(0xFFFFCC00),
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                Text(
                  description,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getBenefitIcon(String benefitType) {
    switch (benefitType.toLowerCase()) {
      case 'special_offers':
        return Icons.local_offer;
      case 'exclusive_items':
        return Icons.star;
      case 'discount_percent':
      case 'shop_discount':
        return Icons.discount;
      case 'priority_support':
        return Icons.support_agent;
      case 'daily_bonus':
        return Icons.diamond;
      case 'tier_up_reward':
        return Icons.emoji_events;
      case 'exclusive_games':
        return Icons.sports_esports;
      case 'tournament_entries':
        return Icons.emoji_events;
      default:
        return Icons.check_circle;
    }
  }
}
