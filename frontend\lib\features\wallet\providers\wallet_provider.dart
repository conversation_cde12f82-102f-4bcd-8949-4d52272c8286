import 'package:flutter/foundation.dart';
import 'package:wiggyz_app/features/wallet/services/wallet_service.dart';
// TODO: Import wallet models if they exist or define them here/separately

class WalletProvider with ChangeNotifier {
  final WalletService _walletService = WalletService();

  Map<String, dynamic>? _walletData;
  final List<dynamic> _transactions = [];
  bool _isLoading = false;
  String? _errorMessage;

  Map<String, dynamic>? get walletData => _walletData;
  List<dynamic> get transactions => _transactions;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  Future<void> fetchWalletDetails() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _walletData = await _walletService.getWalletDetails();
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> fetchWalletTransactions({int page = 1, int limit = 10}) async {
    print('🔍 DEBUG: Starting fetchWalletTransactions(page: $page, limit: $limit)');
    _isLoading = true;
    _errorMessage = null;

    // Clear previous transactions when fetching first page
    if (page == 1) {
      print('🔍 DEBUG: Clearing previous transactions (page 1)');
      _transactions.clear();
    }

    notifyListeners();

    try {
      print('🔍 DEBUG: Calling walletService.getWalletTransactions...');
      final newTransactions = await _walletService.getWalletTransactions(page: page, limit: limit);

      print('🔍 DEBUG: API Response received:');
      print('  - Type: ${newTransactions.runtimeType}');
      print('  - Length: ${newTransactions.length}');

      // Log first few transactions for debugging
      for (int i = 0; i < newTransactions.length && i < 3; i++) {
        print('  - Transaction $i: ${newTransactions[i]}');
      }

      if (page == 1) {
        // Replace all transactions for first page
        print('🔍 DEBUG: Replacing all transactions (page 1)');
        _transactions.clear();
        _transactions.addAll(newTransactions);
      } else {
        // Append for pagination
        print('🔍 DEBUG: Appending transactions (page $page)');
        _transactions.addAll(newTransactions);
      }

      print('📊 DEBUG: Final state - ${newTransactions.length} new transactions, ${_transactions.length} total');
      print('🔍 DEBUG: First transaction in provider: ${_transactions.isNotEmpty ? _transactions[0] : 'NONE'}');
    } catch (e) {
      _errorMessage = e.toString();
      print('❌ DEBUG: Error fetching transactions: $e');
      print('❌ DEBUG: Error type: ${e.runtimeType}');
    } finally {
      _isLoading = false;
      print('🔍 DEBUG: fetchWalletTransactions completed, notifying listeners');
      notifyListeners();
    }
  }

  Future<bool> topUp(Map<String, dynamic> topUpData) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    bool success = false;
    try {
      // The backend might return the updated wallet state or a success message
      final response = await _walletService.topUpWallet(topUpData);
      // Assuming success, refresh wallet details to get the latest balance
      await fetchWalletDetails(); 
      success = true;
    } catch (e) {
      _errorMessage = e.toString();
      success = false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
    return success;
  }

  Future<bool> withdraw(Map<String, dynamic> withdrawalData) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    bool success = false;
    try {
      // The backend might return the updated wallet state or a success message
      final response = await _walletService.withdrawFromWallet(withdrawalData);
      // Assuming success, refresh wallet details to get the latest balance
      await fetchWalletDetails(); 
      success = true;
    } catch (e) {
      _errorMessage = e.toString();
      success = false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
    return success;
  }
}
