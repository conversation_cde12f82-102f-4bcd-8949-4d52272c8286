/// Razorpay Integration Debug Script
/// This script helps debug Razorpay payment gateway issues
library;

import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  print('🔍 Debugging Razorpay Integration Issues\n');

  // Test 1: Check Razorpay Key Format
  print('📋 Test 1: Razorpay Configuration Check');
  
  // These should be your actual Razorpay test credentials
  const testKeyId = 'rzp_test_iVqMylHzm5WcBc'; // Your actual key from .env
  const testKeySecret = 'zp6U1jpZ6Uqg0oPj8jLcaGkL'; // Your actual secret from .env
  
  print('Key ID: $testKeyId');
  print('Key ID Length: ${testKeyId.length}');
  print('Key ID Format: ${testKeyId.startsWith('rzp_test_') ? '✅ Valid Test Key' : '❌ Invalid Format'}');
  
  print('Key Secret Length: ${testKeySecret.length}');
  print('Key Secret Format: ${testKeySecret.length >= 20 ? '✅ Valid Length' : '❌ Too Short'}');
  
  // Test 2: Backend Order Creation
  print('\n📡 Test 2: Backend Order Creation Test');
  await testBackendOrderCreation();
  
  // Test 3: Razorpay Configuration Analysis
  print('\n⚙️ Test 3: Razorpay Configuration Analysis');
  analyzeRazorpayConfig();
  
  // Test 4: Common Issues Check
  print('\n🔧 Test 4: Common Issues Checklist');
  checkCommonIssues();
  
  print('\n📝 Debugging Summary and Next Steps');
  printDebuggingSummary();
}

Future<void> testBackendOrderCreation() async {
  try {
    // Test if we can create a Razorpay order via backend
    final response = await http.post(
      Uri.parse('http://127.0.0.1:5000/api/v1/wallet/topup'),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer test-token', // This will fail auth but test validation
      },
      body: jsonEncode({
        'amount': 100,
        'currency': 'INR',
        'payment_method': 'upi',
      }),
    );
    
    print('Backend Response Status: ${response.statusCode}');
    print('Backend Response: ${response.body}');
    
    if (response.statusCode == 401) {
      print('✅ Backend validation passed (auth failed as expected)');
    } else if (response.statusCode == 400) {
      print('⚠️  Backend validation issue detected');
    } else {
      print('❓ Unexpected response');
    }
    
  } catch (e) {
    print('❌ Backend test failed: $e');
  }
}

void analyzeRazorpayConfig() {
  print('🔍 Analyzing Razorpay Configuration:');
  
  // Check if the configuration looks correct
  final config = {
    'method': {
      'card': true,
      'netbanking': true,
      'wallet': true,
      'upi': true,
      'emi': false,
    },
    'config': {
      'display': {
        'blocks': {
          'banks': {
            'name': 'Pay using Net Banking',
            'instruments': [
              {
                'method': 'netbanking',
                'banks': ['HDFC', 'ICICI', 'SBI', 'AXIS', 'KOTAK']
              }
            ]
          },
          'other': {
            'name': 'Other Payment Methods',
            'instruments': [
              {
                'method': 'card',
                'issuers': ['VISA', 'MASTERCARD', 'AMEX']
              },
              {
                'method': 'upi'
              }
            ]
          }
        },
        'sequence': ['block.banks', 'block.other'],
        'preferences': {
          'show_default_blocks': true
        }
      }
    }
  };
  
  print('✅ Card method enabled: ${config['method']!['card']}');
  print('✅ UPI method enabled: ${config['method']!['upi']}');
  print('✅ Net banking enabled: ${config['method']!['netbanking']}');
  print('✅ Wallet method enabled: ${config['method']!['wallet']}');
  print('✅ Display configuration: Present');
}

void checkCommonIssues() {
  print('🔍 Common Razorpay Issues Checklist:');
  
  print('\n1. ✅ Key Format Issues:');
  print('   - Key ID should start with rzp_test_ or rzp_live_');
  print('   - Key Secret should be 20+ characters');
  print('   - No extra spaces or special characters');
  
  print('\n2. ⚠️  Payment Method Configuration:');
  print('   - Cards should be explicitly enabled');
  print('   - Display blocks should be configured');
  print('   - Test mode should show all payment options');
  
  print('\n3. 🔧 Flutter Integration Issues:');
  print('   - Razorpay plugin version compatibility');
  print('   - Android/iOS specific configurations');
  print('   - Network permissions');
  
  print('\n4. 🌐 Network and Environment:');
  print('   - Backend server running on correct port');
  print('   - CORS configuration allowing Flutter app');
  print('   - Test vs Live mode consistency');
}

void printDebuggingSummary() {
  print('\n📋 Debugging Summary:');
  print('1. ✅ Backend validation error fixed');
  print('2. ✅ Razorpay configuration enhanced');
  print('3. ⚠️  Card input fields issue needs investigation');
  
  print('\n🎯 Immediate Action Items:');
  print('1. 🔄 Hot restart your Flutter app to pick up changes');
  print('2. 🧪 Test payment flow with ₹10 amount');
  print('3. 📱 Check if Razorpay modal opens correctly');
  print('4. 💳 Verify card input fields are visible');
  
  print('\n🔧 If Card Fields Still Missing:');
  print('1. Check Razorpay plugin version in pubspec.yaml');
  print('2. Verify Android/iOS permissions');
  print('3. Test on different device/emulator');
  print('4. Check Razorpay dashboard settings');
  
  print('\n💳 Test Card Details:');
  print('Card Number: 4111 1111 1111 1111');
  print('CVV: 123');
  print('Expiry: 12/25');
  print('Name: Test User');
  
  print('\n📞 If Issues Persist:');
  print('1. Check Flutter console for Razorpay errors');
  print('2. Monitor backend logs during payment');
  print('3. Test with minimal Razorpay configuration');
  print('4. Contact Razorpay support if needed');
}
