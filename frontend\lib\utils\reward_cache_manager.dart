/// Performance optimization utilities for the reward system
/// Handles caching, efficient state updates, and loading optimizations
library;

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/reward_models.dart';
import '../models/daily_reward_models.dart';
import '../models/achievement_models.dart';
import '../models/loyalty_models.dart';

/// Cache manager for reward system data
class RewardCacheManager {
  static final RewardCacheManager _instance = RewardCacheManager._internal();
  factory RewardCacheManager() => _instance;
  RewardCacheManager._internal();

  static const String _dailyRewardStatusKey = 'daily_reward_status';
  static const String _userRewardsKey = 'user_rewards';
  static const String _achievementsKey = 'achievements';
  static const String _loyaltyStatusKey = 'loyalty_status';
  static const String _loyaltyTiersKey = 'loyalty_tiers';
  static const String _lastUpdateKey = 'last_update';
  
  // Cache duration in minutes - different durations for different data types
  static const int _dailyRewardsCacheDuration = 5;
  static const int _achievementsCacheDuration = 10;
  static const int _loyaltyCacheDuration = 15;
  static const int _userRewardsCacheDuration = 5;

  SharedPreferences? _prefs;

  /// Initialize the cache manager
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Check if cached data is still valid with specific duration
  bool _isCacheValid(String key, {int? customDurationMinutes}) {
    if (_prefs == null) return false;

    final lastUpdateStr = _prefs!.getString('${key}_$_lastUpdateKey');
    if (lastUpdateStr == null) return false;

    final lastUpdate = DateTime.tryParse(lastUpdateStr);
    if (lastUpdate == null) return false;

    final now = DateTime.now();
    final difference = now.difference(lastUpdate).inMinutes;

    // Use custom duration or default based on data type
    int cacheDuration = customDurationMinutes ?? _getDefaultCacheDuration(key);

    return difference < cacheDuration;
  }

  /// Get default cache duration based on data type
  int _getDefaultCacheDuration(String key) {
    switch (key) {
      case _dailyRewardStatusKey:
        return _dailyRewardsCacheDuration;
      case _achievementsKey:
        return _achievementsCacheDuration;
      case _loyaltyStatusKey:
      case _loyaltyTiersKey:
        return _loyaltyCacheDuration;
      case _userRewardsKey:
        return _userRewardsCacheDuration;
      default:
        return _dailyRewardsCacheDuration;
    }
  }

  /// Save data to cache with timestamp
  Future<void> _saveToCache(String key, String data) async {
    if (_prefs == null) return;
    
    await _prefs!.setString(key, data);
    await _prefs!.setString('${key}_$_lastUpdateKey', DateTime.now().toIso8601String());
  }

  /// Get data from cache with custom duration
  String? _getFromCache(String key, {int? customDurationMinutes}) {
    if (_prefs == null || !_isCacheValid(key, customDurationMinutes: customDurationMinutes)) return null;
    return _prefs!.getString(key);
  }

  /// Clear specific cache entry
  Future<void> _clearCache(String key) async {
    if (_prefs == null) return;
    
    await _prefs!.remove(key);
    await _prefs!.remove('${key}_$_lastUpdateKey');
  }

  /// Clear all reward caches
  Future<void> clearAllCaches() async {
    if (_prefs == null) return;
    
    await Future.wait([
      _clearCache(_dailyRewardStatusKey),
      _clearCache(_userRewardsKey),
      _clearCache(_achievementsKey),
      _clearCache(_loyaltyStatusKey),
      _clearCache(_loyaltyTiersKey),
    ]);
  }

  // ==================== DAILY REWARDS CACHE ====================

  /// Cache daily reward status
  Future<void> cacheDailyRewardStatus(DailyRewardStatusModel status) async {
    try {
      final json = jsonEncode(status.toJson());
      await _saveToCache(_dailyRewardStatusKey, json);
    } catch (e) {
      debugPrint('Error caching daily reward status: $e');
    }
  }

  /// Get cached daily reward status
  DailyRewardStatusModel? getCachedDailyRewardStatus() {
    try {
      final cached = _getFromCache(_dailyRewardStatusKey);
      if (cached == null) return null;
      
      final json = jsonDecode(cached) as Map<String, dynamic>;
      return DailyRewardStatusModel.fromJson(json);
    } catch (e) {
      debugPrint('Error getting cached daily reward status: $e');
      return null;
    }
  }

  /// Clear daily reward status cache
  Future<void> clearDailyRewardStatusCache() async {
    await _clearCache(_dailyRewardStatusKey);
  }

  // ==================== USER REWARDS CACHE ====================

  /// Cache user rewards
  Future<void> cacheUserRewards(List<UserRewardModel> rewards) async {
    try {
      final json = jsonEncode(rewards.map((r) => r.toJson()).toList());
      await _saveToCache(_userRewardsKey, json);
    } catch (e) {
      debugPrint('Error caching user rewards: $e');
    }
  }

  /// Get cached user rewards
  List<UserRewardModel>? getCachedUserRewards() {
    try {
      final cached = _getFromCache(_userRewardsKey);
      if (cached == null) return null;
      
      final json = jsonDecode(cached) as List;
      return json.map((item) => UserRewardModel.fromJson(item)).toList();
    } catch (e) {
      debugPrint('Error getting cached user rewards: $e');
      return null;
    }
  }

  // ==================== ACHIEVEMENTS CACHE ====================

  /// Cache achievements
  Future<void> cacheAchievements(List<AchievementModel> achievements) async {
    try {
      final json = jsonEncode(achievements.map((a) => a.toJson()).toList());
      await _saveToCache(_achievementsKey, json);
    } catch (e) {
      debugPrint('Error caching achievements: $e');
    }
  }

  /// Get cached achievements
  List<AchievementModel>? getCachedAchievements() {
    try {
      final cached = _getFromCache(_achievementsKey);
      if (cached == null) return null;
      
      final json = jsonDecode(cached) as List;
      return json.map((item) => AchievementModel.fromJson(item)).toList();
    } catch (e) {
      debugPrint('Error getting cached achievements: $e');
      return null;
    }
  }

  // ==================== LOYALTY CACHE ====================

  /// Cache loyalty status
  Future<void> cacheLoyaltyStatus(UserLoyaltyModel status) async {
    try {
      final json = jsonEncode(status.toJson());
      await _saveToCache(_loyaltyStatusKey, json);
    } catch (e) {
      debugPrint('Error caching loyalty status: $e');
    }
  }

  /// Get cached loyalty status
  UserLoyaltyModel? getCachedLoyaltyStatus() {
    try {
      final cached = _getFromCache(_loyaltyStatusKey);
      if (cached == null) return null;
      
      final json = jsonDecode(cached) as Map<String, dynamic>;
      return UserLoyaltyModel.fromJson(json);
    } catch (e) {
      debugPrint('Error getting cached loyalty status: $e');
      return null;
    }
  }

  /// Cache loyalty tiers
  Future<void> cacheLoyaltyTiers(List<LoyaltyTierModel> tiers) async {
    try {
      final json = jsonEncode(tiers.map((t) => t.toJson()).toList());
      await _saveToCache(_loyaltyTiersKey, json);
    } catch (e) {
      debugPrint('Error caching loyalty tiers: $e');
    }
  }

  /// Get cached loyalty tiers
  List<LoyaltyTierModel>? getCachedLoyaltyTiers() {
    try {
      final cached = _getFromCache(_loyaltyTiersKey);
      if (cached == null) return null;
      
      final json = jsonDecode(cached) as List;
      return json.map((item) => LoyaltyTierModel.fromJson(item)).toList();
    } catch (e) {
      debugPrint('Error getting cached loyalty tiers: $e');
      return null;
    }
  }
}

/// Performance optimization utilities for reward UI
class RewardPerformanceOptimizer {
  /// Debounce function for API calls
  static final Map<String, Timer?> _debounceTimers = {};
  
  static void debounce(String key, Duration delay, VoidCallback callback) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, callback);
  }

  /// Throttle function for frequent updates
  static final Map<String, DateTime> _lastThrottleCall = {};
  
  static bool shouldThrottle(String key, Duration throttleDuration) {
    final now = DateTime.now();
    final lastCall = _lastThrottleCall[key];
    
    if (lastCall == null || now.difference(lastCall) >= throttleDuration) {
      _lastThrottleCall[key] = now;
      return false;
    }
    
    return true;
  }

  /// Batch update function for multiple state changes
  static void batchUpdate(List<VoidCallback> updates) {
    for (final update in updates) {
      update();
    }
  }

  /// Efficient list update that only notifies if data actually changed
  static bool updateListIfChanged<T>(List<T> currentList, List<T> newList) {
    if (currentList.length != newList.length) {
      currentList.clear();
      currentList.addAll(newList);
      return true;
    }
    
    bool hasChanges = false;
    for (int i = 0; i < currentList.length; i++) {
      if (currentList[i] != newList[i]) {
        hasChanges = true;
        break;
      }
    }
    
    if (hasChanges) {
      currentList.clear();
      currentList.addAll(newList);
      return true;
    }
    
    return false;
  }

  /// Memory-efficient pagination for large lists
  static List<T> paginateList<T>(List<T> fullList, int page, int pageSize) {
    final startIndex = page * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, fullList.length);
    
    if (startIndex >= fullList.length) return [];
    
    return fullList.sublist(startIndex, endIndex);
  }

  /// Lazy loading helper for achievement progress
  static List<AchievementModel> filterAchievementsByStatus(
    List<AchievementModel> achievements,
    AchievementFilterType filterType,
  ) {
    switch (filterType) {
      case AchievementFilterType.all:
        return achievements;
      case AchievementFilterType.completed:
        return achievements.where((a) => a.isCompleted).toList();
      case AchievementFilterType.inProgress:
        return achievements.where((a) => !a.isCompleted).toList();
      case AchievementFilterType.readyToClaim:
        return achievements.where((a) => a.isReadyToClaim).toList();
    }
  }

  /// Efficient search function for rewards
  static List<T> searchItems<T>(
    List<T> items,
    String query,
    String Function(T) getSearchText,
  ) {
    if (query.isEmpty) return items;
    
    final lowerQuery = query.toLowerCase();
    return items.where((item) {
      final searchText = getSearchText(item).toLowerCase();
      return searchText.contains(lowerQuery);
    }).toList();
  }

  /// Memory cleanup for disposed widgets
  static void cleanup() {
    for (final timer in _debounceTimers.values) {
      timer?.cancel();
    }
    _debounceTimers.clear();
    _lastThrottleCall.clear();
  }
}

/// Achievement filter types for performance optimization
enum AchievementFilterType {
  all,
  completed,
  inProgress,
  readyToClaim,
}

/// Loading state manager for better UX
class RewardLoadingStateManager {
  static final Map<String, bool> _loadingStates = {};
  
  /// Set loading state for a specific operation
  static void setLoading(String operation, bool isLoading) {
    _loadingStates[operation] = isLoading;
  }
  
  /// Check if operation is loading
  static bool isLoading(String operation) {
    return _loadingStates[operation] ?? false;
  }
  
  /// Check if any reward operation is loading
  static bool isAnyLoading() {
    return _loadingStates.values.any((loading) => loading);
  }
  
  /// Clear all loading states
  static void clearAll() {
    _loadingStates.clear();
  }
  
  /// Common operation keys
  static const String dailyRewards = 'daily_rewards';
  static const String achievements = 'achievements';
  static const String loyalty = 'loyalty';
  static const String claimReward = 'claim_reward';
  static const String updateProgress = 'update_progress';
}


