import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:wiggyz_app/features/notifications/widgets/notification_badge.dart';
import 'package:wiggyz_app/screens/notification_screen.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget title;
  final bool showBackButton;
  final bool showNotificationIcon;
  final List<Widget>? actions;
  final PreferredSizeWidget? bottom;
  final double elevation;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final IconThemeData? iconTheme;
  final TextTheme? textTheme;
  final double? titleSpacing;
  final bool centerTitle;
  
  // Constructor with String title for convenience
  CustomAppBar.text({
    super.key,
    required String title,
    TextStyle? style,
    this.showBackButton = true,
    this.showNotificationIcon = true,
    this.actions,
    this.bottom,
    this.elevation = 0,
    this.backgroundColor,
    this.foregroundColor,
    this.iconTheme,
    this.textTheme,
    this.titleSpacing,
    this.centerTitle = false,
  }) : title = Text(
          title,
          style: style,
        );
        
  // Default constructor that takes a widget for the title

  const CustomAppBar({
    super.key,
    required this.title,
    this.showBackButton = true,
    this.showNotificationIcon = true,
    this.actions,
    this.bottom,
    this.elevation = 0,
    this.backgroundColor,
    this.foregroundColor,
    this.iconTheme,
    this.textTheme,
    this.titleSpacing,
    this.centerTitle = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // Default actions
    final defaultActions = <Widget>[];
    
    // Add notification icon if enabled
    if (showNotificationIcon) {
      defaultActions.add(
        Stack(
          clipBehavior: Clip.none,
          children: [
            IconButton(
              icon: const Icon(Icons.notifications_none_rounded),
              onPressed: () {
                // Use GoRouter for navigation if route exists, otherwise fallback to Navigator
                try {
                  context.push('/notifications');
                } catch (e) {
                  // Fallback to Navigator if route doesn't exist
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const NotificationScreen(),
                    ),
                  );
                }
              },
              tooltip: 'Notifications',
            ),
            const Positioned(
              top: 8,
              right: 8,
              child: NotificationBadge(),
            ),
          ],
        ),
      );
    }
    
      // Combine default actions with custom actions
    final List<Widget> allActions = [
      ...defaultActions,
      ...(actions ?? <Widget>[]),
    ];

    return AppBar(
      title: title,
      automaticallyImplyLeading: showBackButton,
      leading: showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back_ios_new_rounded, size: 20),
              onPressed: () {
                // Use GoRouter's canPop and pop for better navigation handling
                if (context.canPop()) {
                  context.pop();
                } else {
                  // Fallback to Navigator if GoRouter can't pop
                  Navigator.maybePop(context);
                }
              },
              tooltip: 'Back',
            )
          : null,
      actions: allActions,
      bottom: bottom,
      elevation: elevation,
      backgroundColor: backgroundColor ?? colorScheme.surface,
      foregroundColor: foregroundColor ?? colorScheme.onSurface,
      iconTheme: iconTheme ?? IconThemeData(color: foregroundColor ?? colorScheme.onSurface),
      titleSpacing: titleSpacing,
      centerTitle: centerTitle,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom?.preferredSize.height ?? 0.0),
      );
}
