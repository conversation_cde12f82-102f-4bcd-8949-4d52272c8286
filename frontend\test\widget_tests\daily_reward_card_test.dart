/// Widget tests for daily reward card component
/// Tests UI behavior, animations, and user interactions
library;

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:wiggyz_app/widgets/daily_reward_card.dart';
import 'package:wiggyz_app/models/daily_reward_models.dart';
import 'package:wiggyz_app/models/reward_models.dart';

void main() {
  group('DailyRewardCard Widget Tests', () {
    late RewardModel testReward;
    late DailyRewardDay testDay;

    setUp(() {
      testReward = RewardModel(
        id: 'reward_1',
        typeId: 'daily_login',
        title: 'Day 1 Reward',
        points: 100,
        diamondValue: 50,
        requirements: {'day': 1},
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      testDay = DailyRewardDay(
        day: 1,
        reward: testReward,
        isClaimed: false,
        isCurrentDay: true,
        isAvailable: true,
        isFutureDay: false,
      );
    });

    testWidgets('should display day number correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DailyRewardCard(
              day: testDay,
              isDarkMode: false,
            ),
          ),
        ),
      );

      expect(find.text('1'), findsOneWidget);
    });

    testWidgets('should display reward amount correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DailyRewardCard(
              day: testDay,
              isDarkMode: false,
            ),
          ),
        ),
      );

      expect(find.text('50'), findsOneWidget); // Diamond value
    });

    testWidgets('should show claim status correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DailyRewardCard(
              day: testDay,
              isDarkMode: false,
            ),
          ),
        ),
      );

      expect(find.text('Claim'), findsOneWidget);
    });

    testWidgets('should show claimed status for claimed rewards', (WidgetTester tester) async {
      final claimedDay = DailyRewardDay(
        day: 1,
        reward: testReward,
        isClaimed: true,
        isCurrentDay: false,
        isAvailable: true,
        isFutureDay: false,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DailyRewardCard(
              day: claimedDay,
              isDarkMode: false,
            ),
          ),
        ),
      );

      expect(find.text('Claimed'), findsOneWidget);
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });

    testWidgets('should show locked status for future days', (WidgetTester tester) async {
      final futureDay = DailyRewardDay(
        day: 5,
        reward: testReward,
        isClaimed: false,
        isCurrentDay: false,
        isAvailable: false,
        isFutureDay: true,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DailyRewardCard(
              day: futureDay,
              isDarkMode: false,
            ),
          ),
        ),
      );

      expect(find.text('Locked'), findsOneWidget);
      expect(find.byIcon(Icons.lock), findsOneWidget);
    });

    testWidgets('should handle tap events when claimable', (WidgetTester tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DailyRewardCard(
              day: testDay,
              isDarkMode: false,
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(DailyRewardCard));
      await tester.pump();

      expect(tapped, true);
    });

    testWidgets('should not handle tap events when not claimable', (WidgetTester tester) async {
      bool tapped = false;
      
      final unclaimableDay = DailyRewardDay(
        day: 1,
        reward: testReward,
        isClaimed: true,
        isCurrentDay: false,
        isAvailable: true,
        isFutureDay: false,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DailyRewardCard(
              day: unclaimableDay,
              isDarkMode: false,
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(DailyRewardCard));
      await tester.pump();

      expect(tapped, false);
    });

    testWidgets('should apply dark mode styling correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DailyRewardCard(
              day: testDay,
              isDarkMode: true,
            ),
          ),
        ),
      );

      // Test would verify dark mode colors are applied
      // This would require checking container decorations
    });

    testWidgets('should show pulse animation for claimable rewards', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DailyRewardCard(
              day: testDay,
              isDarkMode: false,
            ),
          ),
        ),
      );

      // Verify animation controller is running for claimable rewards
      await tester.pump(const Duration(milliseconds: 500));
      // Animation testing would require more complex setup
    });
  });

  group('StreakIndicator Widget Tests', () {
    testWidgets('should display current streak correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StreakIndicator(
              currentStreak: 5,
              longestStreak: 10,
              isDarkMode: false,
            ),
          ),
        ),
      );

      expect(find.text('5 Days'), findsOneWidget);
      expect(find.text('Best: 10 days'), findsOneWidget);
      expect(find.byIcon(Icons.local_fire_department), findsOneWidget);
    });

    testWidgets('should display compact version correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StreakIndicator(
              currentStreak: 3,
              longestStreak: 7,
              isDarkMode: false,
              isCompact: true,
            ),
          ),
        ),
      );

      expect(find.text('3'), findsOneWidget);
      expect(find.byIcon(Icons.local_fire_department), findsOneWidget);
    });
  });

  group('RewardClaimButton Widget Tests', () {
    testWidgets('should display button text correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardClaimButton(
              text: 'Claim Reward',
              isEnabled: true,
            ),
          ),
        ),
      );

      expect(find.text('Claim Reward'), findsOneWidget);
    });

    testWidgets('should show loading state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardClaimButton(
              text: 'Claim Reward',
              isLoading: true,
              isEnabled: true,
            ),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Claim Reward'), findsNothing);
    });

    testWidgets('should handle button press correctly', (WidgetTester tester) async {
      bool pressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardClaimButton(
              text: 'Claim Reward',
              isEnabled: true,
              onPressed: () => pressed = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      expect(pressed, true);
    });

    testWidgets('should be disabled when not enabled', (WidgetTester tester) async {
      bool pressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardClaimButton(
              text: 'Claim Reward',
              isEnabled: false,
              onPressed: () => pressed = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      expect(pressed, false);
    });
  });
}
