/// Offline reward management for handling reward operations without network connectivity
/// Queues operations and syncs when connection is restored
library;

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Offline operation types
enum OfflineOperationType {
  claimDailyReward,
  claimReward,
  updateAchievementProgress,
}

/// Offline operation model
class OfflineOperation {
  final String id;
  final OfflineOperationType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final int retryCount;

  const OfflineOperation({
    required this.id,
    required this.type,
    required this.data,
    required this.timestamp,
    this.retryCount = 0,
  });

  factory OfflineOperation.fromJson(Map<String, dynamic> json) {
    return OfflineOperation(
      id: json['id'] as String,
      type: OfflineOperationType.values[json['type'] as int],
      data: json['data'] as Map<String, dynamic>,
      timestamp: DateTime.parse(json['timestamp'] as String),
      retryCount: json['retryCount'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.index,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'retryCount': retryCount,
    };
  }

  OfflineOperation copyWith({
    String? id,
    OfflineOperationType? type,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    int? retryCount,
  }) {
    return OfflineOperation(
      id: id ?? this.id,
      type: type ?? this.type,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      retryCount: retryCount ?? this.retryCount,
    );
  }
}

/// Offline reward manager for handling operations without network
class OfflineRewardManager {
  static final OfflineRewardManager _instance = OfflineRewardManager._internal();
  factory OfflineRewardManager() => _instance;
  OfflineRewardManager._internal();

  static const String _queueKey = 'offline_reward_queue';
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(minutes: 5);

  SharedPreferences? _prefs;
  List<OfflineOperation> _operationQueue = [];
  bool _isProcessing = false;

  /// Initialize the offline manager
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
    await _loadQueue();
    _startConnectivityListener();
  }

  /// Load queued operations from storage
  Future<void> _loadQueue() async {
    if (_prefs == null) return;

    final queueJson = _prefs!.getString(_queueKey);
    if (queueJson != null) {
      try {
        final List<dynamic> queueList = jsonDecode(queueJson);
        _operationQueue = queueList
            .map((item) => OfflineOperation.fromJson(item as Map<String, dynamic>))
            .toList();
      } catch (e) {
        debugPrint('Error loading offline queue: $e');
        _operationQueue = [];
      }
    }
  }

  /// Save queue to storage
  Future<void> _saveQueue() async {
    if (_prefs == null) return;

    try {
      final queueJson = jsonEncode(_operationQueue.map((op) => op.toJson()).toList());
      await _prefs!.setString(_queueKey, queueJson);
    } catch (e) {
      debugPrint('Error saving offline queue: $e');
    }
  }

  /// Start listening for connectivity changes
  void _startConnectivityListener() {
    Connectivity().onConnectivityChanged.listen((List<ConnectivityResult> results) {
      if (results.isNotEmpty && !results.contains(ConnectivityResult.none)) {
        _processQueue();
      }
    });
  }

  /// Check if device is online
  Future<bool> isOnline() async {
    final connectivityResults = await Connectivity().checkConnectivity();
    return connectivityResults.isNotEmpty && !connectivityResults.contains(ConnectivityResult.none);
  }

  /// Add operation to offline queue
  Future<void> queueOperation(OfflineOperation operation) async {
    _operationQueue.add(operation);
    await _saveQueue();
    
    debugPrint('Queued offline operation: ${operation.type} - ${operation.id}');
    
    // Try to process immediately if online
    if (await isOnline()) {
      _processQueue();
    }
  }

  /// Queue daily reward claim
  Future<void> queueDailyRewardClaim({String? idempotencyKey}) async {
    final operation = OfflineOperation(
      id: idempotencyKey ?? 'daily_${DateTime.now().millisecondsSinceEpoch}',
      type: OfflineOperationType.claimDailyReward,
      data: {
        'idempotencyKey': idempotencyKey,
      },
      timestamp: DateTime.now(),
    );

    await queueOperation(operation);
  }

  /// Queue reward claim
  Future<void> queueRewardClaim(String rewardId, {String? idempotencyKey}) async {
    final operation = OfflineOperation(
      id: idempotencyKey ?? 'reward_${rewardId}_${DateTime.now().millisecondsSinceEpoch}',
      type: OfflineOperationType.claimReward,
      data: {
        'rewardId': rewardId,
        'idempotencyKey': idempotencyKey,
      },
      timestamp: DateTime.now(),
    );

    await queueOperation(operation);
  }

  /// Queue achievement progress update
  Future<void> queueAchievementProgress(
    String achievementId,
    int progressIncrement, {
    String? description,
  }) async {
    final operation = OfflineOperation(
      id: 'achievement_${achievementId}_${DateTime.now().millisecondsSinceEpoch}',
      type: OfflineOperationType.updateAchievementProgress,
      data: {
        'achievementId': achievementId,
        'progressIncrement': progressIncrement,
        'description': description,
      },
      timestamp: DateTime.now(),
    );

    await queueOperation(operation);
  }

  /// Process queued operations
  Future<void> _processQueue() async {
    if (_isProcessing || _operationQueue.isEmpty) return;
    if (!await isOnline()) return;

    _isProcessing = true;
    debugPrint('Processing ${_operationQueue.length} offline operations');

    final List<OfflineOperation> failedOperations = [];

    for (final operation in List.from(_operationQueue)) {
      try {
        final success = await _executeOperation(operation);
        
        if (success) {
          _operationQueue.remove(operation);
          debugPrint('Successfully processed offline operation: ${operation.id}');
        } else {
          // Retry logic
          if (operation.retryCount < _maxRetries) {
            final updatedOperation = operation.copyWith(
              retryCount: operation.retryCount + 1,
            );
            failedOperations.add(updatedOperation);
            _operationQueue.remove(operation);
          } else {
            // Max retries reached, remove from queue
            _operationQueue.remove(operation);
            debugPrint('Max retries reached for operation: ${operation.id}');
          }
        }
      } catch (e) {
        debugPrint('Error processing offline operation ${operation.id}: $e');
        
        if (operation.retryCount < _maxRetries) {
          final updatedOperation = operation.copyWith(
            retryCount: operation.retryCount + 1,
          );
          failedOperations.add(updatedOperation);
          _operationQueue.remove(operation);
        } else {
          _operationQueue.remove(operation);
        }
      }
    }

    // Add failed operations back to queue for retry
    _operationQueue.addAll(failedOperations);

    await _saveQueue();
    _isProcessing = false;

    // Schedule retry for failed operations
    if (failedOperations.isNotEmpty) {
      Future.delayed(_retryDelay, () => _processQueue());
    }
  }

  /// Execute a single operation
  Future<bool> _executeOperation(OfflineOperation operation) async {
    // This would need to be injected with the actual reward service
    // For now, we'll return true to simulate success
    // In a real implementation, you would inject the RewardService here
    
    switch (operation.type) {
      case OfflineOperationType.claimDailyReward:
        // await rewardService.claimDailyReward(idempotencyKey: operation.data['idempotencyKey']);
        return true;
        
      case OfflineOperationType.claimReward:
        // await rewardService.claimReward(operation.data['rewardId'], idempotencyKey: operation.data['idempotencyKey']);
        return true;
        
      case OfflineOperationType.updateAchievementProgress:
        // await rewardService.updateAchievementProgress(
        //   operation.data['achievementId'],
        //   operation.data['progressIncrement'],
        //   description: operation.data['description'],
        // );
        return true;
    }
  }

  /// Get pending operations count
  int get pendingOperationsCount => _operationQueue.length;

  /// Get pending operations
  List<OfflineOperation> get pendingOperations => List.unmodifiable(_operationQueue);

  /// Clear all queued operations
  Future<void> clearQueue() async {
    _operationQueue.clear();
    await _saveQueue();
  }

  /// Get operations by type
  List<OfflineOperation> getOperationsByType(OfflineOperationType type) {
    return _operationQueue.where((op) => op.type == type).toList();
  }

  /// Check if specific operation is queued
  bool isOperationQueued(String operationId) {
    return _operationQueue.any((op) => op.id == operationId);
  }

  /// Remove specific operation from queue
  Future<void> removeOperation(String operationId) async {
    _operationQueue.removeWhere((op) => op.id == operationId);
    await _saveQueue();
  }

  /// Force process queue (for manual sync)
  Future<void> forceSync() async {
    if (await isOnline()) {
      await _processQueue();
    }
  }

  /// Get queue status summary
  Map<String, dynamic> getQueueStatus() {
    final statusMap = <OfflineOperationType, int>{};
    
    for (final operation in _operationQueue) {
      statusMap[operation.type] = (statusMap[operation.type] ?? 0) + 1;
    }

    return {
      'totalOperations': _operationQueue.length,
      'isProcessing': _isProcessing,
      'operationsByType': statusMap.map((key, value) => MapEntry(key.toString(), value)),
      'oldestOperation': _operationQueue.isNotEmpty 
          ? _operationQueue.map((op) => op.timestamp).reduce((a, b) => a.isBefore(b) ? a : b)
          : null,
    };
  }
}
