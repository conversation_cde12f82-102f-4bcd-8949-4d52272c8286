import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import * as tournamentController from './controllers/tournamentController';
import { authenticate, authorize } from '../../middleware/auth';
import { validate } from '../../middleware/validate';
import {
  createTournamentSchema,
  tournamentRegistrationSchema,
  updateScoreSchema,
  claimRewardSchema,
  tournamentLeaderboardSchema,
  tournamentIdSchema,
  submitTournamentResultSchema,
  verifyTournamentResultSchema,
  assignTournamentWinnerSchema,
  tournamentIdParamSchema,
  resultIdParamSchema,
  startTournamentMatchSchema
} from './validations';
import multer from 'multer';
import path from 'path';

// Configure multer for tournament screenshot uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/tournament-screenshots/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'tournament-screenshot-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files (JPEG, JPG, PNG, WebP) are allowed'));
    }
  }
});

// Helper function to ensure middleware type compatibility
const wrapWithValidate = (schema: any, source = 'body'): RequestHandler => {
  return validate(schema, source as any);
};

const router = express.Router();

// Public routes
router.get('/', tournamentController.getTournaments);
router.get('/matches', tournamentController.getAllMatches); // Add route for all matches

// Protected routes requiring authentication (specific routes before parameterized ones)
// Tournament creation restricted to admin, manager, and authorized spectator roles only
router.post('/', authenticate, authorize(['admin', 'super admin', 'manager']), wrapWithValidate(createTournamentSchema), tournamentController.createTournament);
router.post('/register', authenticate, wrapWithValidate(tournamentRegistrationSchema), tournamentController.registerForTournament);
router.post('/update-score', authenticate, wrapWithValidate(updateScoreSchema), tournamentController.updateParticipantScore);
router.post('/claim-reward', authenticate, wrapWithValidate(claimRewardSchema), tournamentController.claimTournamentReward);

// User tournament routes
router.get('/user/joined-tournaments', authenticate, tournamentController.getUserJoinedTournaments as any);
router.get('/user/active-tournaments', authenticate, tournamentController.getUserActiveTournaments as any);

// Tournament result submission routes
router.post('/:tournamentId/submit-result', authenticate, wrapWithValidate(tournamentIdParamSchema, 'params'), wrapWithValidate(submitTournamentResultSchema), tournamentController.submitTournamentResult as any);
router.get('/:tournamentId/verification-status', authenticate, wrapWithValidate(tournamentIdParamSchema, 'params'), tournamentController.getTournamentVerificationStatus as any);
router.get('/:tournamentId/submission-status', authenticate, wrapWithValidate(tournamentIdParamSchema, 'params'), tournamentController.checkSubmissionStatus as any);

// Tournament screenshot upload route
router.post('/:id/upload-screenshot', authenticate, upload.single('screenshot'), wrapWithValidate(tournamentIdSchema, 'params'), tournamentController.uploadScreenshot as any);

// Tournament match start routes
router.post('/:tournamentId/start-match', authenticate, authorize(['admin', 'super admin', 'manager', 'spectator']), wrapWithValidate(tournamentIdParamSchema, 'params'), wrapWithValidate(startTournamentMatchSchema), tournamentController.startTournamentMatch as any);

// Tournament match end routes
router.post('/:tournamentId/end-match', authenticate, authorize(['admin', 'super admin', 'manager', 'spectator']), wrapWithValidate(tournamentIdParamSchema, 'params'), tournamentController.endTournamentMatch as any);

// Admin tournament verification routes
router.get('/admin/pending-results', authenticate, authorize(['admin', 'super admin', 'manager', 'moderator']), tournamentController.getPendingTournamentResults as any);
router.put('/admin/results/:resultId/verify', authenticate, authorize(['admin', 'super admin', 'manager', 'moderator']), wrapWithValidate(resultIdParamSchema, 'params'), wrapWithValidate(verifyTournamentResultSchema), tournamentController.verifyTournamentResult as any);
router.post('/:tournamentId/assign-winners', authenticate, authorize(['admin', 'super admin', 'manager', 'moderator']), wrapWithValidate(tournamentIdParamSchema, 'params'), wrapWithValidate(assignTournamentWinnerSchema), tournamentController.assignTournamentWinner as any);

// Parameterized routes (these must come AFTER specific routes to avoid conflicts)
router.get('/:id', wrapWithValidate(tournamentIdSchema, 'params'), tournamentController.getTournamentById);
router.get('/:id/leaderboard', wrapWithValidate(tournamentIdSchema, 'params'), tournamentController.getTournamentLeaderboard);
// Add route for fetching matches by tournament ID
router.get('/:id/matches', wrapWithValidate(tournamentIdSchema, 'params'), tournamentController.getMatchesByTournamentId);

// Admin routes
// Note: In a production environment, additional admin authentication middleware would be applied
router.post('/tournaments/:id/finalize', authenticate, wrapWithValidate(tournamentIdSchema, 'params'), tournamentController.finalizeTournament);

export default router;
