import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:path/path.dart' as path;
import 'package:image_picker/image_picker.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'dart:typed_data';
import 'dart:convert';
import '../core/api/api_config.dart';
import '../services/auth_service.dart';

class ImageUploadService {
  final SupabaseClient _supabase = Supabase.instance.client;
  final AuthService _authService = AuthService();

  /// Uploads a screenshot for match results with backend integration and fallback
  Future<String> uploadScreenshot(XFile imageFile, String matchId) async {
    print('🔄 Starting image upload for match: $matchId');

    try {
      // Validate the image file first
      await validateImageFile(imageFile);

      // Try backend upload first (more reliable)
      try {
        print('📤 Attempting backend upload...');
        final backendUrl = await _uploadScreenshotViaBackend(
          imageFile,
          matchId,
        );
        if (backendUrl.isNotEmpty) {
          print('✅ Backend upload successful: $backendUrl');
          return backendUrl;
        }
      } catch (e) {
        print('⚠️ Backend upload failed: $e');
        print('🔄 Falling back to direct Supabase upload...');
      }

      // Fallback to direct Supabase upload
      final supabaseUrl = await _uploadScreenshotToSupabase(imageFile, matchId);
      print('✅ Supabase fallback upload successful: $supabaseUrl');
      return supabaseUrl;
    } catch (e) {
      print('❌ All upload methods failed: $e');
      throw Exception('Image upload failed: $e');
    }
  }

  /// Uploads a screenshot for tournament results with backend integration and fallback
  Future<String> uploadTournamentScreenshot(XFile imageFile, String tournamentId) async {
    print('🔄 Starting tournament image upload for tournament: $tournamentId');

    try {
      // Validate the image file first
      await validateImageFile(imageFile);

      // Try backend upload first (more reliable)
      try {
        print('📤 Attempting tournament backend upload...');
        final backendUrl = await _uploadTournamentScreenshotViaBackend(
          imageFile,
          tournamentId,
        );
        if (backendUrl.isNotEmpty) {
          print('✅ Tournament backend upload successful: $backendUrl');
          return backendUrl;
        }
      } catch (e) {
        print('⚠️ Tournament backend upload failed: $e');
        print('🔄 Falling back to direct Supabase upload...');
      }

      // Fallback to direct Supabase upload with tournament-specific path
      final supabaseUrl = await _uploadTournamentScreenshotToSupabase(imageFile, tournamentId);
      print('✅ Tournament Supabase fallback upload successful: $supabaseUrl');
      return supabaseUrl;
    } catch (e) {
      print('❌ All tournament upload methods failed: $e');
      throw Exception('Tournament image upload failed: $e');
    }
  }

  /// Upload screenshot via backend API (primary method)
  Future<String> _uploadScreenshotViaBackend(
    XFile imageFile,
    String matchId,
  ) async {
    try {
      final token = await _authService.getToken();
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('${ApiConfig.baseUrl}/matches/$matchId/upload-screenshot'),
      );

      // Add authentication header if available
      if (token != null) {
        request.headers['Authorization'] = 'Bearer $token';
      }

      // Handle web vs mobile file upload
      if (kIsWeb) {
        // Web-specific upload with preserved MIME type
        final imageBytes = await imageFile.readAsBytes();
        final mimeType =
            imageFile.mimeType ?? _detectMimeTypeFromBytes(imageBytes);

        print('🌐 Web upload - MIME type: $mimeType');

        final multipartFile = http.MultipartFile.fromBytes(
          'screenshot',
          imageBytes,
          filename: 'match_$matchId.${_getExtensionFromMimeType(mimeType)}',
          contentType: MediaType.parse(mimeType),
        );
        request.files.add(multipartFile);
      } else {
        // Mobile upload using file path
        final multipartFile = await http.MultipartFile.fromPath(
          'screenshot',
          imageFile.path,
          contentType: MediaType.parse(imageFile.mimeType ?? 'image/jpeg'),
        );
        request.files.add(multipartFile);
      }

      print('📤 Sending backend upload request...');
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print('📊 Backend response: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);
        final screenshotUrl =
            responseData['data']['screenshot_url'] ??
            responseData['data']['screenshotUrl'] ??
            responseData['screenshotUrl'];

        if (screenshotUrl != null) {
          return screenshotUrl;
        }
        throw Exception('Backend response missing screenshot URL');
      } else {
        throw Exception(
          'Backend upload failed: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('❌ Backend upload error: $e');
      rethrow;
    }
  }

  /// Upload tournament screenshot via backend API (primary method)
  Future<String> _uploadTournamentScreenshotViaBackend(
    XFile imageFile,
    String tournamentId,
  ) async {
    try {
      final token = await _authService.getToken();
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('${ApiConfig.baseUrl}/tournaments/$tournamentId/upload-screenshot'),
      );

      // Add authentication header if available
      if (token != null) {
        request.headers['Authorization'] = 'Bearer $token';
      }

      // Handle web vs mobile file upload
      if (kIsWeb) {
        // Web-specific upload with preserved MIME type
        final imageBytes = await imageFile.readAsBytes();
        final mimeType =
            imageFile.mimeType ?? _detectMimeTypeFromBytes(imageBytes);

        print('🌐 Tournament web upload - MIME type: $mimeType');

        final multipartFile = http.MultipartFile.fromBytes(
          'screenshot',
          imageBytes,
          filename: 'tournament_$tournamentId.${_getExtensionFromMimeType(mimeType)}',
          contentType: MediaType.parse(mimeType),
        );
        request.files.add(multipartFile);
      } else {
        // Mobile upload using file path
        final multipartFile = await http.MultipartFile.fromPath(
          'screenshot',
          imageFile.path,
          filename: 'tournament_$tournamentId.${path.extension(imageFile.name)}',
        );
        request.files.add(multipartFile);
      }

      print('📤 Sending tournament backend upload request...');
      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      print('📥 Tournament backend response: ${response.statusCode}');
      print('📄 Tournament response body: $responseBody');

      if (response.statusCode == 200) {
        final responseData = json.decode(responseBody);
        final screenshotUrl = responseData['data']?['screenshot_url'];
        if (screenshotUrl != null) {
          return screenshotUrl;
        }
        throw Exception('Tournament backend response missing screenshot URL');
      } else {
        throw Exception(
          'Tournament backend upload failed: ${response.statusCode} - $responseBody',
        );
      }
    } catch (e) {
      print('❌ Tournament backend upload error: $e');
      rethrow;
    }
  }

  /// Upload screenshot directly to Supabase (fallback method)
  Future<String> _uploadScreenshotToSupabase(
    XFile imageFile,
    String matchId,
  ) async {
    try {
      // Generate unique filename with proper pattern for RLS policies
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(imageFile.name).toLowerCase();
      final fileName = 'match_${matchId}_$timestamp$extension';

      // Get image bytes
      final imageBytes = await imageFile.readAsBytes();
      final mimeType =
          imageFile.mimeType ?? _detectMimeTypeFromBytes(imageBytes);

      print('☁️ Uploading to Supabase: $fileName (${imageBytes.length} bytes)');

      // Use the Supabase client for the upload, which correctly handles auth
      await _supabase.storage
          .from('match-screenshots')
          .uploadBinary(
            fileName,
            imageBytes,
            fileOptions: FileOptions(contentType: mimeType, upsert: false),
          );

      final String publicUrl = _supabase.storage
          .from('match-screenshots')
          .getPublicUrl(fileName);

      print('✅ Supabase upload successful: $publicUrl');
      return publicUrl;
    } catch (e) {
      print('❌ Supabase upload error: $e');
      if (e is StorageException) {
        print('   StorageException details: ${e.message}');
      }
      throw Exception('Supabase upload failed: $e');
    }
  }

  /// Upload tournament screenshot directly to Supabase (fallback method)
  Future<String> _uploadTournamentScreenshotToSupabase(
    XFile imageFile,
    String tournamentId,
  ) async {
    try {
      // Generate unique filename with proper pattern for tournament results
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(imageFile.name).toLowerCase();
      final fileName = 'tournament-results/$tournamentId/tournament_${tournamentId}_$timestamp$extension';

      // Get image bytes
      final imageBytes = await imageFile.readAsBytes();
      final mimeType =
          imageFile.mimeType ?? _detectMimeTypeFromBytes(imageBytes);

      print('☁️ Uploading tournament screenshot to Supabase: $fileName (${imageBytes.length} bytes)');

      // Use the Supabase client for the upload, which correctly handles auth
      await _supabase.storage
          .from('match-screenshots')
          .uploadBinary(
            fileName,
            imageBytes,
            fileOptions: FileOptions(contentType: mimeType, upsert: false),
          );

      final String publicUrl = _supabase.storage
          .from('match-screenshots')
          .getPublicUrl(fileName);

      print('✅ Tournament Supabase upload successful: $publicUrl');
      return publicUrl;
    } catch (e) {
      print('❌ Tournament Supabase upload error: $e');
      if (e is StorageException) {
        print('   StorageException details: ${e.message}');
      }
      throw Exception('Tournament Supabase upload failed: $e');
    }
  }

  /// Deletes a screenshot from storage
  Future<void> deleteScreenshot(String url) async {
    try {
      // Extract filename from URL
      final uri = Uri.parse(url);
      final fileName = path.basename(uri.path);

      await _supabase.storage.from('match-screenshots').remove([fileName]);
    } catch (e) {
      // Log error but don't throw - deletion failures shouldn't break the flow
      print('Failed to delete screenshot: $e');
    }
  }

  /// Uploads multiple screenshots at once
  Future<List<String>> uploadMultipleScreenshots(
    List<XFile> imageFiles,
    String matchId,
  ) async {
    final List<String> uploadedUrls = [];

    for (int i = 0; i < imageFiles.length; i++) {
      try {
        final url = await uploadScreenshot(imageFiles[i], '${matchId}_$i');
        uploadedUrls.add(url);
      } catch (e) {
        // Clean up previously uploaded files
        for (final uploadedUrl in uploadedUrls) {
          await deleteScreenshot(uploadedUrl);
        }
        throw Exception('Failed to upload screenshot ${i + 1}: $e');
      }
    }

    return uploadedUrls;
  }

  /// Validates image file before upload
  Future<bool> validateImageFile(XFile imageFile) async {
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    final fileSize = await imageFile.length();
    if (fileSize > maxSize) {
      throw Exception('Image size must be less than 10MB');
    }

    // Check file extension
    final allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
    final extension = path.extension(imageFile.name).toLowerCase();
    if (!allowedExtensions.contains(extension)) {
      throw Exception('Only JPG, PNG, and WebP images are allowed');
    }

    return true;
  }

  /// Compresses image if needed (basic implementation)
  Future<XFile> compressImageIfNeeded(XFile imageFile) async {
    // For now, return the original file
    // In a production app, you'd use a package like flutter_image_compress
    return imageFile;
  }

  /// Detect MIME type from image bytes
  String _detectMimeTypeFromBytes(Uint8List bytes) {
    if (bytes.length < 4) return 'image/jpeg';

    // PNG signature: 89 50 4E 47
    if (bytes[0] == 0x89 &&
        bytes[1] == 0x50 &&
        bytes[2] == 0x4E &&
        bytes[3] == 0x47) {
      return 'image/png';
    }

    // JPEG signature: FF D8
    if (bytes[0] == 0xFF && bytes[1] == 0xD8) {
      return 'image/jpeg';
    }

    // WebP signature: 52 49 46 46 (RIFF) + WebP at offset 8
    if (bytes.length >= 12 &&
        bytes[0] == 0x52 &&
        bytes[1] == 0x49 &&
        bytes[2] == 0x46 &&
        bytes[3] == 0x46 &&
        bytes[8] == 0x57 &&
        bytes[9] == 0x45 &&
        bytes[10] == 0x42 &&
        bytes[11] == 0x50) {
      return 'image/webp';
    }

    // Default to JPEG
    return 'image/jpeg';
  }

  /// Get file extension from MIME type
  String _getExtensionFromMimeType(String mimeType) {
    switch (mimeType.toLowerCase()) {
      case 'image/png':
        return 'png';
      case 'image/webp':
        return 'webp';
      case 'image/jpeg':
      case 'image/jpg':
      default:
        return 'jpg';
    }
  }

  /// Upload multiple screenshots for batch processing
  Future<String> uploadScreenshotBytes(
    Uint8List imageBytes,
    String matchId, {
    String? mimeType,
  }) async {
    print('🔄 Starting bytes upload for match: $matchId');

    try {
      // Detect MIME type if not provided
      final detectedMimeType = mimeType ?? _detectMimeTypeFromBytes(imageBytes);
      final extension = _getExtensionFromMimeType(detectedMimeType);

      // Try backend upload first
      try {
        print('📤 Attempting backend bytes upload...');
        final backendUrl = await _uploadBytesViaBackend(
          imageBytes,
          matchId,
          detectedMimeType,
        );
        if (backendUrl.isNotEmpty) {
          print('✅ Backend bytes upload successful: $backendUrl');
          return backendUrl;
        }
      } catch (e) {
        print('⚠️ Backend bytes upload failed: $e');
        print('🔄 Falling back to direct Supabase upload...');
      }

      // Fallback to direct Supabase upload
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'match_${matchId}_$timestamp.$extension';

      print(
        '☁️ Uploading bytes to Supabase: $fileName (${imageBytes.length} bytes)',
      );

      // Use the Supabase client for the upload
      await _supabase.storage
          .from('match-screenshots')
          .uploadBinary(
            fileName,
            imageBytes,
            fileOptions: FileOptions(
              contentType: detectedMimeType,
              upsert: false,
            ),
          );

      // Get public URL
      final publicUrl = _supabase.storage
          .from('match-screenshots')
          .getPublicUrl(fileName);

      print('✅ Supabase bytes upload successful: $publicUrl');
      return publicUrl;
    } catch (e) {
      print('❌ Bytes upload failed: $e');
      throw Exception('Bytes upload failed: $e');
    }
  }

  /// Upload bytes via backend API
  Future<String> _uploadBytesViaBackend(
    Uint8List imageBytes,
    String matchId,
    String mimeType,
  ) async {
    try {
      final token = await _authService.getToken();
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('${ApiConfig.baseUrl}/matches/$matchId/upload-screenshot'),
      );

      if (token != null) {
        request.headers['Authorization'] = 'Bearer $token';
      }

      final extension = _getExtensionFromMimeType(mimeType);
      final multipartFile = http.MultipartFile.fromBytes(
        'screenshot',
        imageBytes,
        filename: 'match_$matchId.$extension',
        contentType: MediaType.parse(mimeType),
      );
      request.files.add(multipartFile);

      print('📤 Sending backend bytes upload request...');
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print('📊 Backend bytes response: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);
        final screenshotUrl =
            responseData['data']['screenshot_url'] ??
            responseData['data']['screenshotUrl'] ??
            responseData['screenshotUrl'];

        if (screenshotUrl != null) {
          return screenshotUrl;
        }
        throw Exception('Backend response missing screenshot URL');
      } else {
        throw Exception(
          'Backend bytes upload failed: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('❌ Backend bytes upload error: $e');
      rethrow;
    }
  }
}

/// Exception class for image upload errors
class ImageUploadException implements Exception {
  final String message;
  final String? code;

  ImageUploadException(this.message, {this.code});

  @override
  String toString() => 'ImageUploadException: $message';
}
