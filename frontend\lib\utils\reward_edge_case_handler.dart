/// Comprehensive edge case handling for the reward system
/// Handles expired rewards, invalid states, race conditions, and data inconsistencies
library;

import 'package:flutter/foundation.dart';
import '../models/reward_models.dart';
import '../models/daily_reward_models.dart';
import '../models/achievement_models.dart';
import '../models/loyalty_models.dart';

/// Edge case handler for reward system
class RewardEdgeCaseHandler {
  /// Handle expired daily rewards
  static DailyRewardStatusModel? handleExpiredDailyRewards(DailyRewardStatusModel? status) {
    if (status == null) return null;

    // Check if streak is broken due to missed days
    final now = DateTime.now();
    final lastLogin = status.streak.lastLoginDate;
    
    if (lastLogin != null) {
      final daysSinceLastLogin = now.difference(lastLogin).inDays;
      
      // If more than 1 day has passed, streak might be broken
      if (daysSinceLastLogin > 1) {
        debugPrint('Potential streak break detected: $daysSinceLastLogin days since last login');
        
        // Create updated streak with broken status
        final updatedStreak = LoginStreakModel(
          id: status.streak.id,
          userId: status.streak.userId,
          currentStreak: 0, // Reset streak
          longestStreak: status.streak.longestStreak,
          lastLoginDate: lastLogin,
          createdAt: status.streak.createdAt,
          updatedAt: now,
        );
        
        return DailyRewardStatusModel(
          streak: updatedStreak,
          rewards: status.rewards,
          hasClaimedToday: false, // Reset claim status
        );
      }
    }

    return status;
  }

  /// Handle expired user rewards
  static List<UserRewardModel> filterExpiredRewards(List<UserRewardModel> rewards) {
    final now = DateTime.now();
    final validRewards = <UserRewardModel>[];
    final expiredRewards = <UserRewardModel>[];

    for (final reward in rewards) {
      if (reward.isExpired) {
        expiredRewards.add(reward);
        debugPrint('Expired reward detected: ${reward.id}');
      } else {
        validRewards.add(reward);
      }
    }

    if (expiredRewards.isNotEmpty) {
      debugPrint('Filtered out ${expiredRewards.length} expired rewards');
    }

    return validRewards;
  }

  /// Handle achievement progress inconsistencies
  static List<AchievementModel> validateAchievementProgress(List<AchievementModel> achievements) {
    final validatedAchievements = <AchievementModel>[];

    for (final achievement in achievements) {
      AchievementModel validatedAchievement = achievement;

      // Fix progress that exceeds target
      if (achievement.current > achievement.target) {
        debugPrint('Achievement progress exceeds target: ${achievement.id}');
        validatedAchievement = achievement.copyWith(
          current: achievement.target,
          isCompleted: true,
          completedAt: achievement.completedAt ?? DateTime.now(),
        );
      }

      // Fix negative progress
      if (achievement.current < 0) {
        debugPrint('Negative achievement progress detected: ${achievement.id}');
        validatedAchievement = validatedAchievement.copyWith(current: 0);
      }

      // Fix completion status inconsistency
      if (achievement.current >= achievement.target && !achievement.isCompleted) {
        debugPrint('Achievement should be completed: ${achievement.id}');
        validatedAchievement = validatedAchievement.copyWith(
          isCompleted: true,
          completedAt: achievement.completedAt ?? DateTime.now(),
        );
      }

      // Fix false completion status
      if (achievement.current < achievement.target && achievement.isCompleted) {
        debugPrint('Achievement incorrectly marked as completed: ${achievement.id}');
        validatedAchievement = validatedAchievement.copyWith(
          isCompleted: false,
          completedAt: null,
        );
      }

      validatedAchievements.add(validatedAchievement);
    }

    return validatedAchievements;
  }

  /// Handle loyalty tier inconsistencies
  static UserLoyaltyModel? validateLoyaltyStatus(
    UserLoyaltyModel? loyaltyStatus,
    List<LoyaltyTierModel> tiers,
  ) {
    if (loyaltyStatus == null || tiers.isEmpty) return loyaltyStatus;

    // Sort tiers by min points
    final sortedTiers = List<LoyaltyTierModel>.from(tiers)
      ..sort((a, b) => a.minPoints.compareTo(b.minPoints));

    // Find the correct tier for current points
    LoyaltyTierModel? correctTier;
    for (int i = sortedTiers.length - 1; i >= 0; i--) {
      if (loyaltyStatus.points >= sortedTiers[i].minPoints) {
        correctTier = sortedTiers[i];
        break;
      }
    }

    // If no tier found, use the lowest tier
    correctTier ??= sortedTiers.first;

    // Check if current tier is incorrect
    if (loyaltyStatus.tierId != correctTier.id) {
      debugPrint('Loyalty tier mismatch detected. Points: ${loyaltyStatus.points}, Current: ${loyaltyStatus.tierId}, Correct: ${correctTier.id}');
      
      return UserLoyaltyModel(
        id: loyaltyStatus.id,
        userId: loyaltyStatus.userId,
        points: loyaltyStatus.points,
        tierId: correctTier.id,
        updatedAt: DateTime.now(),
        loyaltyTier: correctTier,
      );
    }

    return loyaltyStatus;
  }

  /// Handle race conditions in reward claiming
  static bool canClaimReward({
    required RewardModel reward,
    required List<UserRewardModel> userRewards,
    required DateTime? lastClaimTime,
  }) {
    // Check if reward is already claimed
    final alreadyClaimed = userRewards.any((ur) => ur.rewardId == reward.id);
    if (alreadyClaimed) {
      debugPrint('Reward already claimed: ${reward.id}');
      return false;
    }

    // Check if reward is available
    if (!reward.isAvailable) {
      debugPrint('Reward not available: ${reward.id}');
      return false;
    }

    // Check for daily reward rate limiting
    if (reward.typeId == 'daily_login' && lastClaimTime != null) {
      final now = DateTime.now();
      final timeSinceLastClaim = now.difference(lastClaimTime);
      
      // Prevent claiming multiple daily rewards within 23 hours
      if (timeSinceLastClaim.inHours < 23) {
        debugPrint('Daily reward rate limit: ${timeSinceLastClaim.inHours} hours since last claim');
        return false;
      }
    }

    return true;
  }

  /// Handle data synchronization conflicts
  static T resolveDataConflict<T>({
    required T localData,
    required T serverData,
    required DateTime localTimestamp,
    required DateTime serverTimestamp,
    required bool Function(T, T) isEqual,
  }) {
    // If data is identical, return either
    if (isEqual(localData, serverData)) {
      return serverData; // Prefer server data for consistency
    }

    // Use timestamp to resolve conflict (newer wins)
    if (serverTimestamp.isAfter(localTimestamp)) {
      debugPrint('Using server data (newer timestamp)');
      return serverData;
    } else {
      debugPrint('Using local data (newer timestamp)');
      return localData;
    }
  }

  /// Handle invalid reward configurations
  static List<RewardModel> validateRewardConfigurations(List<RewardModel> rewards) {
    final validRewards = <RewardModel>[];

    for (final reward in rewards) {
      bool isValid = true;
      final issues = <String>[];

      // Check for negative values
      if (reward.points < 0) {
        issues.add('Negative points: ${reward.points}');
        isValid = false;
      }

      if (reward.diamondValue < 0) {
        issues.add('Negative diamond value: ${reward.diamondValue}');
        isValid = false;
      }

      // Check for invalid date ranges
      if (reward.startDate != null && reward.endDate != null) {
        if (reward.startDate!.isAfter(reward.endDate!)) {
          issues.add('Start date after end date');
          isValid = false;
        }
      }

      // Check for invalid daily reward requirements
      if (reward.typeId == 'daily_login' && reward.dayRequirement != null) {
        if (reward.dayRequirement! < 1 || reward.dayRequirement! > 365) {
          issues.add('Invalid day requirement: ${reward.dayRequirement}');
          isValid = false;
        }
      }

      if (isValid) {
        validRewards.add(reward);
      } else {
        debugPrint('Invalid reward configuration: ${reward.id} - ${issues.join(', ')}');
      }
    }

    return validRewards;
  }

  /// Handle memory pressure by cleaning up old data
  static Map<String, dynamic> cleanupOldData({
    required List<UserRewardModel> userRewards,
    required List<RewardTransactionModel> transactions,
    required List<LoyaltyTransactionModel> loyaltyHistory,
    int maxRewards = 100,
    int maxTransactions = 50,
    int maxLoyaltyHistory = 50,
  }) {
    final cleanupStats = <String, int>{};

    // Clean up old user rewards (keep most recent)
    if (userRewards.length > maxRewards) {
      userRewards.sort((a, b) => b.claimedAt.compareTo(a.claimedAt));
      final removed = userRewards.length - maxRewards;
      userRewards.removeRange(maxRewards, userRewards.length);
      cleanupStats['userRewards'] = removed;
    }

    // Clean up old transactions
    if (transactions.length > maxTransactions) {
      transactions.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      final removed = transactions.length - maxTransactions;
      transactions.removeRange(maxTransactions, transactions.length);
      cleanupStats['transactions'] = removed;
    }

    // Clean up old loyalty history
    if (loyaltyHistory.length > maxLoyaltyHistory) {
      loyaltyHistory.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      final removed = loyaltyHistory.length - maxLoyaltyHistory;
      loyaltyHistory.removeRange(maxLoyaltyHistory, loyaltyHistory.length);
      cleanupStats['loyaltyHistory'] = removed;
    }

    if (cleanupStats.isNotEmpty) {
      debugPrint('Cleaned up old data: $cleanupStats');
    }

    return cleanupStats;
  }

  /// Handle timezone-related issues
  static DateTime normalizeTimestamp(DateTime timestamp) {
    // Convert to UTC to avoid timezone issues
    return timestamp.toUtc();
  }

  /// Handle duplicate reward entries
  static List<T> removeDuplicates<T>(
    List<T> items,
    String Function(T) getId,
  ) {
    final seen = <String>{};
    final unique = <T>[];

    for (final item in items) {
      final id = getId(item);
      if (!seen.contains(id)) {
        seen.add(id);
        unique.add(item);
      } else {
        debugPrint('Duplicate item removed: $id');
      }
    }

    return unique;
  }

  /// Handle corrupted data recovery
  static T? recoverCorruptedData<T>(
    String dataKey,
    T? Function() parseData,
    T Function() getDefaultData,
  ) {
    try {
      final data = parseData();
      if (data != null) {
        return data;
      }
    } catch (e) {
      debugPrint('Corrupted data detected for $dataKey: $e');
    }

    debugPrint('Using default data for $dataKey');
    return getDefaultData();
  }
}
