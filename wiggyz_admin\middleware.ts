import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';

export async function middleware(request: NextRequest) {
  // Skip middleware for static files and API routes
  if (
    request.nextUrl.pathname.startsWith('/_next/') ||
    request.nextUrl.pathname.startsWith('/api/') ||
    request.nextUrl.pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // First, handle root path redirect if not on login page
  const url = request.nextUrl.clone();
  const isRootPath = url.pathname === '/';
  const isLoginPage = url.pathname === '/login';

  if (isRootPath) {
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }

  // Skip auth check for login page
  if (isLoginPage) {
    return NextResponse.next();
  }

  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  // Validate environment variables
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    console.error('Missing Supabase environment variables');
    return NextResponse.redirect(new URL('/login?error=config', request.url));
  }

  // Create a Supabase client using the request cookies
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          response.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: CookieOptions) {
          response.cookies.set({
            name,
            value: '',
            ...options,
          });
        },
      },
    }
  );

  try {
    // Get the user's session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    // If session error or no session, redirect to login
    if (sessionError) {
      console.error('Session error:', sessionError);
      return NextResponse.redirect(new URL('/login?error=session', request.url));
    }

    if (!session) {
      console.log('No session found, redirecting to login');
      return NextResponse.redirect(new URL('/login', request.url));
    }

    // Get user role from the users table
    const { data: userData, error } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    // Check if user has admin or super admin role
    const allowedRoles = ['admin', 'super admin'];
    if (error) {
      console.error('Database error fetching user role:', error);
      return NextResponse.redirect(new URL('/login?error=database', request.url));
    }

    if (!userData || !allowedRoles.includes((userData.role || '').toLowerCase())) {
      console.log('User does not have admin role:', userData?.role);
      // Redirect to login page with an access denied message
      return NextResponse.redirect(new URL('/login?access=denied', request.url));
    }

    console.log('User authorized:', session.user.email, 'Role:', userData.role);
    // Allow access if user has the required role
    return response;
  } catch (error) {
    console.error('Middleware auth error:', error);
    return NextResponse.redirect(new URL('/login?error=unexpected', request.url));
  }
}

// Specify which paths should be protected by this middleware
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
