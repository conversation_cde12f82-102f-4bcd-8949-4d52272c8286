"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { 
  AlertTriangle, 
  Shield, 
  TrendingUp, 
  Users, 
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Activity,
  Flag,
  Zap
} from "lucide-react";

interface FraudAlert {
  id: string;
  userId: string;
  userName: string;
  matchId: string;
  matchTitle: string;
  riskScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  riskFactors: string[];
  detectedAt: string;
  status: 'pending' | 'reviewed' | 'resolved';
  confidenceScore: number;
  screenshotUrl?: string;
  deviceFingerprint?: string;
  ipAddress?: string;
}

interface UserBehaviorAnalysis {
  userId: string;
  userName: string;
  totalMatches: number;
  avgScore: number;
  scoreVariance: number;
  suspiciousPatterns: string[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  lastActivity: string;
  deviceCount: number;
  ipCount: number;
}

interface VerificationMetrics {
  totalVerifications: number;
  autoApproved: number;
  manualReview: number;
  autoRejected: number;
  averageConfidence: number;
  fraudDetectionRate: number;
  falsePositiveRate: number;
}

const FraudDetectionDashboard: React.FC = () => {
  const [fraudAlerts, setFraudAlerts] = useState<FraudAlert[]>([]);
  const [userAnalytics, setUserAnalytics] = useState<UserBehaviorAnalysis[]>([]);
  const [metrics, setMetrics] = useState<VerificationMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedAlert, setSelectedAlert] = useState<FraudAlert | null>(null);

  useEffect(() => {
    fetchFraudData();
    // Set up real-time updates
    const interval = setInterval(fetchFraudData, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchFraudData = async () => {
    try {
      setLoading(true);
      
      // Fetch fraud alerts
      const alertsResponse = await fetch('/api/admin/fraud-alerts');
      const alertsData = await alertsResponse.json();
      setFraudAlerts(alertsData.data || []);

      // Fetch user behavior analytics
      const analyticsResponse = await fetch('/api/admin/user-behavior-analytics');
      const analyticsData = await analyticsResponse.json();
      setUserAnalytics(analyticsData.data || []);

      // Fetch verification metrics
      const metricsResponse = await fetch('/api/admin/verification-metrics');
      const metricsData = await metricsResponse.json();
      setMetrics(metricsData.data || null);

    } catch (error) {
      console.error('Error fetching fraud data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAlertAction = async (alertId: string, action: 'approve' | 'reject' | 'investigate') => {
    try {
      await fetch(`/api/admin/fraud-alerts/${alertId}/action`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      });
      
      // Refresh data
      fetchFraudData();
    } catch (error) {
      console.error('Error handling alert action:', error);
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRiskLevelIcon = (level: string) => {
    switch (level) {
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'high': return <Flag className="h-4 w-4 text-orange-600" />;
      case 'medium': return <Eye className="h-4 w-4 text-yellow-600" />;
      case 'low': return <Shield className="h-4 w-4 text-green-600" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  if (loading && !metrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading fraud detection data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Fraud Detection Dashboard</h1>
          <p className="text-gray-600">Monitor and manage security threats in real-time</p>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1 text-sm text-green-600">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Live Monitoring</span>
          </div>
        </div>
      </div>

      {/* Metrics Overview */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Verifications</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalVerifications}</div>
              <p className="text-xs text-muted-foreground">Last 24 hours</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Auto Approved</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{metrics.autoApproved}</div>
              <p className="text-xs text-muted-foreground">
                {((metrics.autoApproved / metrics.totalVerifications) * 100).toFixed(1)}% of total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Fraud Detection Rate</CardTitle>
              <Shield className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{metrics.fraudDetectionRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Accuracy rate</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Confidence</CardTitle>
              <TrendingUp className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{metrics.averageConfidence.toFixed(1)}%</div>
              <Progress value={metrics.averageConfidence} className="mt-2" />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="alerts" className="space-y-4">
        <TabsList>
          <TabsTrigger value="alerts" className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4" />
            <span>Active Alerts ({fraudAlerts.filter(a => a.status === 'pending').length})</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>User Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="patterns" className="flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>Behavior Patterns</span>
          </TabsTrigger>
        </TabsList>

        {/* Active Alerts Tab */}
        <TabsContent value="alerts" className="space-y-4">
          {fraudAlerts.filter(alert => alert.status === 'pending').length === 0 ? (
            <Card>
              <CardContent className="flex items-center justify-center h-32">
                <div className="text-center">
                  <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                  <p className="text-gray-600">No active fraud alerts</p>
                  <p className="text-sm text-gray-500">All systems running smoothly</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {fraudAlerts.filter(alert => alert.status === 'pending').map((alert) => (
                <Card key={alert.id} className="border-l-4 border-l-red-500">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getRiskLevelIcon(alert.riskLevel)}
                        <div>
                          <CardTitle className="text-lg">{alert.userName}</CardTitle>
                          <CardDescription>
                            Match: {alert.matchTitle} • {new Date(alert.detectedAt).toLocaleString()}
                          </CardDescription>
                        </div>
                      </div>
                      <Badge className={getRiskLevelColor(alert.riskLevel)}>
                        {alert.riskLevel.toUpperCase()} RISK ({alert.riskScore})
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Risk Factors */}
                      <div>
                        <h4 className="font-medium text-sm text-gray-700 mb-2">Risk Factors:</h4>
                        <div className="flex flex-wrap gap-2">
                          {alert.riskFactors.map((factor, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {factor}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Confidence Score */}
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Confidence Score</span>
                          <span>{alert.confidenceScore}%</span>
                        </div>
                        <Progress value={alert.confidenceScore} />
                      </div>

                      {/* Technical Details */}
                      <div className="bg-muted/50 dark:bg-muted/30 p-3 rounded-lg text-sm">
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <span className="font-medium text-foreground">Device:</span> <span className="text-muted-foreground">{alert.deviceFingerprint?.slice(0, 16)}...</span>
                          </div>
                          <div>
                            <span className="font-medium text-foreground">IP:</span> <span className="text-muted-foreground">{alert.ipAddress}</span>
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center space-x-2 pt-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleAlertAction(alert.id, 'approve')}
                          className="text-green-600 border-green-300 hover:bg-green-50"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleAlertAction(alert.id, 'reject')}
                          className="text-red-600 border-red-300 hover:bg-red-50"
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleAlertAction(alert.id, 'investigate')}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Investigate
                        </Button>
                        {alert.screenshotUrl && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => window.open(alert.screenshotUrl, '_blank')}
                          >
                            View Screenshot
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* User Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4">
            {userAnalytics.map((user) => (
              <Card key={user.userId}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{user.userName}</CardTitle>
                      <CardDescription>
                        {user.totalMatches} matches • Last active: {new Date(user.lastActivity).toLocaleDateString()}
                      </CardDescription>
                    </div>
                    <Badge className={getRiskLevelColor(user.riskLevel)}>
                      {user.riskLevel.toUpperCase()}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-gray-700">Avg Score</div>
                      <div className="text-lg font-bold">{user.avgScore.toFixed(1)}</div>
                    </div>
                    <div>
                      <div className="font-medium text-gray-700">Score Variance</div>
                      <div className="text-lg font-bold">{user.scoreVariance.toFixed(1)}</div>
                    </div>
                    <div>
                      <div className="font-medium text-gray-700">Devices</div>
                      <div className="text-lg font-bold">{user.deviceCount}</div>
                    </div>
                    <div>
                      <div className="font-medium text-gray-700">IP Addresses</div>
                      <div className="text-lg font-bold">{user.ipCount}</div>
                    </div>
                  </div>
                  
                  {user.suspiciousPatterns.length > 0 && (
                    <div className="mt-4">
                      <div className="font-medium text-sm text-gray-700 mb-2">Suspicious Patterns:</div>
                      <div className="flex flex-wrap gap-2">
                        {user.suspiciousPatterns.map((pattern, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {pattern}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Behavior Patterns Tab */}
        <TabsContent value="patterns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Fraud Detection Patterns</CardTitle>
              <CardDescription>
                Common patterns and indicators detected by the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Alert>
                  <Zap className="h-4 w-4" />
                  <AlertTitle>Rapid Submission Detection</AlertTitle>
                  <AlertDescription>
                    Users submitting multiple results within short time frames are automatically flagged
                  </AlertDescription>
                </Alert>
                
                <Alert>
                  <Users className="h-4 w-4" />
                  <AlertTitle>Multi-Device Access</AlertTitle>
                  <AlertDescription>
                    Accounts accessing from numerous devices trigger security reviews
                  </AlertDescription>
                </Alert>
                
                <Alert>
                  <TrendingUp className="h-4 w-4" />
                  <AlertTitle>Performance Anomalies</AlertTitle>
                  <AlertDescription>
                    Sudden significant improvements in performance scores are analyzed for authenticity
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FraudDetectionDashboard;
