/// Loyalty system screen with tier display and progress tracking
/// Shows current tier, benefits, progress to next tier, and transaction history
library;

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/reward_provider.dart';
import '../theme_provider.dart';
import '../models/loyalty_models.dart';
import '../widgets/shared/golden_app_bar.dart';
// import '../widgets/loyalty_tier_card.dart';

class LoyaltyScreen extends StatefulWidget {
  const LoyaltyScreen({super.key});

  @override
  State<LoyaltyScreen> createState() => _LoyaltyScreenState();
}

class _LoyaltyScreenState extends State<LoyaltyScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _animationController.forward();
    
    // Fetch loyalty data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final rewardProvider = context.read<RewardProvider>();
      rewardProvider.fetchLoyaltyStatus();
      rewardProvider.fetchLoyaltyHistory();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      appBar: GoldenAppBar(
        title: 'Loyalty Program',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              final rewardProvider = context.read<RewardProvider>();
              rewardProvider.fetchLoyaltyStatus();
              rewardProvider.fetchLoyaltyHistory();
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFFFFCC00),
          unselectedLabelColor: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          indicatorColor: const Color(0xFFFFCC00),
          labelStyle: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          tabs: const [
            Tab(text: 'My Status'),
            Tab(text: 'All Tiers'),
          ],
        ),
      ),
      body: Consumer<RewardProvider>(
        builder: (context, rewardProvider, child) {
          if (rewardProvider.isLoyaltyLoading) {
            return _buildLoadingState(isDarkMode);
          }

          if (rewardProvider.loyaltyError != null) {
            return _buildErrorState(isDarkMode, rewardProvider.loyaltyError!);
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildMyStatusTab(isDarkMode, rewardProvider),
              _buildAllTiersTab(isDarkMode, rewardProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: const Color(0xFFFFCC00),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading loyalty status...',
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(bool isDarkMode, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load loyalty data',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                final rewardProvider = context.read<RewardProvider>();
                rewardProvider.fetchLoyaltyStatus();
                rewardProvider.fetchLoyaltyHistory();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFFCC00),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'Try Again',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMyStatusTab(bool isDarkMode, RewardProvider rewardProvider) {
    if (rewardProvider.loyaltyStatus == null) {
      return Center(
        child: Text(
          'No loyalty data available',
          style: GoogleFonts.poppins(
            fontSize: 16,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
      );
    }

    final loyalty = rewardProvider.loyaltyStatus!;
    final tier = loyalty.loyaltyTier ?? _calculateTierFromPoints(loyalty.points, rewardProvider.loyaltyTiers);

    return RefreshIndicator(
      onRefresh: () async {
        final provider = context.read<RewardProvider>();
        await provider.fetchLoyaltyStatus();
        await provider.fetchLoyaltyHistory();
      },
      color: const Color(0xFFFFCC00),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCurrentTierCard(isDarkMode, loyalty, tier),
            const SizedBox(height: 24),
            _buildProgressCard(isDarkMode, loyalty, rewardProvider.loyaltyTiers),
            const SizedBox(height: 24),
            _buildBenefitsCard(isDarkMode, tier),
            const SizedBox(height: 24),
            _buildRecentTransactions(isDarkMode, rewardProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildAllTiersTab(bool isDarkMode, RewardProvider rewardProvider) {
    if (rewardProvider.loyaltyTiers.isEmpty) {
      return Center(
        child: Text(
          'No loyalty tiers available',
          style: GoogleFonts.poppins(
            fontSize: 16,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
      );
    }

    final sortedTiers = List<LoyaltyTierModel>.from(rewardProvider.loyaltyTiers)
      ..sort((a, b) => a.minPoints.compareTo(b.minPoints));

    return RefreshIndicator(
      onRefresh: () => context.read<RewardProvider>().fetchLoyaltyStatus(),
      color: const Color(0xFFFFCC00),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: sortedTiers.length,
        itemBuilder: (context, index) {
          final tier = sortedTiers[index];
          final isCurrentTier = rewardProvider.loyaltyStatus?.tierId == tier.id;
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _buildTierCard(isDarkMode, tier, isCurrentTier, rewardProvider.loyaltyStatus?.points ?? 0),
          );
        },
      ),
    );
  }

  Widget _buildCurrentTierCard(bool isDarkMode, UserLoyaltyModel loyalty, LoyaltyTierModel? tier) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            tier?.displayColor ?? const Color(0xFFFFCC00),
            (tier?.displayColor ?? const Color(0xFFFFCC00)).withOpacity(0.7),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: (tier?.displayColor ?? const Color(0xFFFFCC00)).withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: tier?.emojiIcon != null
                    ? Text(
                        tier!.emojiIcon!,
                        style: const TextStyle(fontSize: 32),
                      )
                    : Icon(
                        tier?.displayIcon ?? Icons.military_tech,
                        color: Colors.white,
                        size: 32,
                      ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tier?.name ?? 'Unknown Tier',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    if (tier == null)
                      Text(
                        'Debug: No tier data available',
                        style: GoogleFonts.poppins(
                          fontSize: 10,
                          color: Colors.red.withOpacity(0.8),
                        ),
                      ),
                    Text(
                      '${loyalty.points} loyalty points',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // View Rewards Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.pushNamed(context, '/loyalty/rewards-info');
              },
              icon: const Icon(
                Icons.card_giftcard,
                color: Colors.black,
                size: 18,
              ),
              label: Text(
                'View All Rewards',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFFCC00),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
            ),
          ),
          if (tier?.description != null) ...[
            const SizedBox(height: 16),
            Text(
              tier!.description!,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressCard(bool isDarkMode, UserLoyaltyModel loyalty, List<LoyaltyTierModel> tiers) {
    final progress = loyalty.getProgressToNextTier(tiers);
    final nextTier = loyalty.getNextTier(tiers);
    final pointsNeeded = loyalty.getPointsNeededForNextTier(tiers);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            nextTier != null ? 'Progress to ${nextTier.name}' : 'Maximum Tier Reached',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          if (nextTier != null) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${loyalty.points} points',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
                Text(
                  '${nextTier.minPoints} points',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: nextTier.displayColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: isDarkMode ? Colors.grey[700] : Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(nextTier.displayColor),
              minHeight: 8,
            ),
            const SizedBox(height: 12),
            Text(
              '$pointsNeeded more points needed',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: nextTier.displayColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFFFCC00).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.emoji_events,
                    color: const Color(0xFFFFCC00),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Congratulations! You\'ve reached the highest tier.',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFFFFCC00),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBenefitsCard(bool isDarkMode, LoyaltyTierModel? tier) {
    if (tier == null || tier.benefits.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Benefits',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          ...tier.benefits.map((benefit) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: tier.displayColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getBenefitIcon(benefit.type),
                    color: tier.displayColor,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    benefit.description,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions(bool isDarkMode, RewardProvider rewardProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Activity',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to full history
                },
                child: Text(
                  'View All',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFFFFCC00),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (rewardProvider.loyaltyHistory.isEmpty)
            Center(
              child: Text(
                'No recent activity',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            )
          else
            ...rewardProvider.loyaltyHistory.take(3).map((transaction) => 
              _buildTransactionItem(isDarkMode, transaction)
            ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(bool isDarkMode, LoyaltyTransactionModel transaction) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: transaction.isPointsAdded 
                  ? Colors.green.withOpacity(0.2)
                  : Colors.red.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              transaction.action.icon,
              color: transaction.isPointsAdded ? Colors.green : Colors.red,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.action.displayName,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                if (transaction.description != null)
                  Text(
                    transaction.description!,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),
          Text(
            '${transaction.isPointsAdded ? '+' : ''}${transaction.points}',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: transaction.isPointsAdded ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTierCard(bool isDarkMode, LoyaltyTierModel tier, bool isCurrentTier, int userPoints) {
    final isUnlocked = userPoints >= tier.minPoints;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isCurrentTier
              ? tier.displayColor
              : isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          width: isCurrentTier ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isCurrentTier
                ? tier.displayColor.withOpacity(0.2)
                : Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: tier.displayColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: tier.emojiIcon != null
                    ? Text(
                        tier.emojiIcon!,
                        style: const TextStyle(fontSize: 24),
                      )
                    : Icon(
                        tier.displayIcon,
                        color: tier.displayColor,
                        size: 24,
                      ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          tier.name,
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                        if (isCurrentTier) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: tier.displayColor,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'Current',
                              style: GoogleFonts.poppins(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    Text(
                      '${tier.minPoints} points required',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: tier.displayColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              if (!isUnlocked)
                Icon(
                  Icons.lock,
                  color: isDarkMode ? Colors.grey[600] : Colors.grey[400],
                  size: 20,
                ),
            ],
          ),
          if (tier.description != null) ...[
            const SizedBox(height: 12),
            Text(
              tier.description!,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ],
          if (tier.benefits.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Benefits',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 8),
            ...tier.benefits.take(3).map((benefit) => Padding(
              padding: const EdgeInsets.only(bottom: 6),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: (isUnlocked ? tier.displayColor : Colors.grey).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      _getBenefitIcon(benefit.type),
                      color: isUnlocked ? tier.displayColor : Colors.grey,
                      size: 12,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      benefit.description,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: isUnlocked
                            ? (isDarkMode ? Colors.grey[300] : Colors.grey[700])
                            : Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }

  IconData _getBenefitIcon(String benefitType) {
    switch (benefitType.toLowerCase()) {
      case 'special_offers':
        return Icons.local_offer;
      case 'exclusive_items':
        return Icons.star;
      case 'discount_percent':
      case 'shop_discount':
        return Icons.discount;
      case 'priority_support':
        return Icons.support_agent;
      case 'daily_bonus':
        return Icons.diamond;
      case 'tier_up_reward':
        return Icons.emoji_events;
      case 'exclusive_games':
        return Icons.sports_esports;
      case 'tournament_entries':
        return Icons.tournament;
      default:
        return Icons.check_circle;
    }
  }

  /// Calculate the user's tier based on their points and available tiers
  LoyaltyTierModel? _calculateTierFromPoints(int userPoints, List<LoyaltyTierModel> allTiers) {
    if (allTiers.isEmpty) return null;

    // Sort tiers by min points in descending order to find the highest tier the user qualifies for
    final sortedTiers = List<LoyaltyTierModel>.from(allTiers)
      ..sort((a, b) => b.minPoints.compareTo(a.minPoints));

    // Find the highest tier the user qualifies for
    for (final tier in sortedTiers) {
      if (userPoints >= tier.minPoints) {
        debugPrint('🎯 Calculated tier for $userPoints points: ${tier.name}');
        return tier;
      }
    }

    // If no tier found, return the lowest tier
    final lowestTier = allTiers.reduce((a, b) => a.minPoints < b.minPoints ? a : b);
    debugPrint('🎯 Using lowest tier as fallback: ${lowestTier.name}');
    return lowestTier;
  }
}
