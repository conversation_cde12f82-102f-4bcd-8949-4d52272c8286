/**
 * Wallet controllers for managing wallet-related API endpoints
 */
import { Request, Response } from 'express';
import { supabase } from '../../../config/supabase';
import { successResponse, errorResponse } from '../../../utils/responseFormatter';
import { walletService } from '../services/walletService';
import { razorpayService } from '../../../services/razorpayService';
import { logger } from '../../../utils/logger';
import <PERSON><PERSON> from 'joi';
import { withdrawalSchema } from '../validations';

/**
 * Get wallet balance and details
 * @route GET /api/v1/wallet
 */
export const getWalletDetails = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json(errorResponse('Authentication required'));
    }

    const walletDetails = await walletService.getWalletDetails(req.user.userId);
    return res.status(200).json(successResponse(walletDetails, 'Wallet details retrieved successfully'));
  } catch (err: any) {
    console.error('Error fetching wallet details:', err);
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Get transaction history
 * @route GET /api/v1/wallet/transactions
 */
export const getTransactionHistory = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json(errorResponse('Authentication required'));
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    
    const result = await walletService.getTransactionHistory(req.user.userId, page, limit);
    return res.status(200).json(successResponse(result.transactions, 'Transaction history retrieved successfully', {
      pagination: result.pagination
    }));
  } catch (err: any) {
    console.error('Error fetching transaction history:', err);
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Initiate wallet top-up
 * @route POST /api/v1/wallet/topup
 */
export const initiateTopUp = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json(errorResponse('Authentication required'));
    }

    // Validate request
    const schema = Joi.object({
      amount: Joi.number().positive().required(),
      currency: Joi.string().length(3).default('INR'), // Changed default to INR
      payment_method: Joi.string().required(),
      amountInPaise: Joi.number().positive().optional() // Allow amountInPaise from middleware
    });

    const { error, value } = schema.validate(req.body);
    if (error) {
      return res.status(400).json(errorResponse(error.details[0].message));
    }

    const { amount, currency, payment_method } = value;

    // Create transaction record and get payment gateway token
    const result = await walletService.initiateTopUp(req.user.userId, amount, currency, payment_method);
    
    return res.status(200).json(successResponse(result, 'Top-up initiated successfully'));
  } catch (err: any) {
    console.error('Error initiating top-up:', err);
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Process payment gateway webhook
 * @route POST /api/v1/wallet/webhook/:gateway
 */
export const processPaymentWebhook = async (req: Request, res: Response) => {
  try {
    const gateway = req.params.gateway;
    
    // Verify webhook signature based on gateway
    const isValid = await walletService.verifyWebhookSignature(gateway, req);
    if (!isValid) {
      return res.status(401).json(errorResponse('Invalid webhook signature'));
    }
    
    // Process webhook payload based on gateway
    const result = await walletService.processWebhookPayload(gateway, req.body);
    
    return res.status(200).json(successResponse(null, 'Webhook processed successfully'));
  } catch (err: any) {
    console.error('Error processing webhook:', err);
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Initiate withdrawal
 * @route POST /api/v1/wallet/withdraw
 */
export const initiateWithdrawal = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json(errorResponse('Authentication required'));
    }

    // Validate request using the withdrawal schema with allowUnknown to ignore extra fields
    const { error, value } = withdrawalSchema.validate(req.body, {
      allowUnknown: true,  // Allow extra fields like final_amount from frontend
      stripUnknown: true   // Remove unknown fields from validated data
    });

    if (error) {
      return res.status(400).json(errorResponse(error.details[0].message));
    }

    const { amount, currency, withdrawal_method, account_details } = value;

    // Process withdrawal request
    const result = await walletService.initiateWithdrawal(
      req.user.userId, 
      amount, 
      currency, 
      withdrawal_method,
      account_details
    );
    
    return res.status(200).json(successResponse(result, 'Withdrawal initiated successfully'));
  } catch (err: any) {
    console.error('Error initiating withdrawal:', err);
    
    // Handle specific errors like insufficient balance
    if (err.message === 'Insufficient balance') {
      return res.status(400).json(errorResponse('Insufficient balance for this withdrawal'));
    }
    
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Add balance to user's wallet (for testing/admin purposes)
 * @route POST /api/v1/wallet/add-balance
 */
export const addBalance = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json(errorResponse('Authentication required'));
    }

    // Validation schema
    const schema = Joi.object({
      amount: Joi.number().positive().required().messages({
        'number.base': 'Amount must be a number',
        'number.positive': 'Amount must be positive',
        'any.required': 'Amount is required'
      }),
      description: Joi.string().max(255).optional().default('Manual balance addition')
    });

    const { error, value } = schema.validate(req.body);
    if (error) {
      return res.status(400).json(errorResponse(error.details[0].message));
    }

    const { amount, description } = value;
    const userId = req.user.userId;

    console.log(`Adding ${amount} to wallet for user ${userId}: ${description}`);

    // Add balance to wallet
    const result = await walletService.addBalance(userId, amount, description);
    
    return res.status(200).json(successResponse(result, 'Balance added successfully'));
  } catch (err: any) {
    console.error('Error adding balance:', err);
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Verify Razorpay payment and update wallet balance
 * @route POST /api/v1/wallet/verify-payment
 */
export const verifyPayment = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json(errorResponse('Authentication required'));
    }

    // Validation schema for payment verification
    const verifyPaymentSchema = Joi.object({
      razorpay_order_id: Joi.string().required(),
      razorpay_payment_id: Joi.string().required(),
      razorpay_signature: Joi.string().required(),
      transaction_id: Joi.string().uuid().required()
    });

    const { error, value } = verifyPaymentSchema.validate(req.body);
    if (error) {
      return res.status(400).json(errorResponse(`Validation error: ${error.details[0].message}`));
    }

    const { razorpay_order_id, razorpay_payment_id, razorpay_signature, transaction_id } = value;
    const userId = req.user.userId;

    logger.info(`Payment verification requested - User ID: ${userId}, Order ID: ${razorpay_order_id}, Payment ID: ${razorpay_payment_id}, Transaction ID: ${transaction_id}`);

    // Verify payment signature
    const isValidSignature = razorpayService.verifyPaymentSignature({
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature
    });

    if (!isValidSignature) {
      logger.warn(`Invalid payment signature - User ID: ${userId}, Order ID: ${razorpay_order_id}, Payment ID: ${razorpay_payment_id}`);
      return res.status(400).json(errorResponse('Invalid payment signature'));
    }

    // Fetch payment details from Razorpay
    const paymentDetails = await razorpayService.fetchPayment(razorpay_payment_id);

    if (paymentDetails.status !== 'captured') {
      logger.warn(`Payment not captured - User ID: ${userId}, Payment ID: ${razorpay_payment_id}, Status: ${paymentDetails.status}`);
      return res.status(400).json(errorResponse('Payment not completed'));
    }

    // Process the payment and update wallet
    const result = await walletService.processSuccessfulPayment(
      userId,
      transaction_id,
      {
        razorpay_order_id,
        razorpay_payment_id,
        amount: typeof paymentDetails.amount === 'string' ? parseInt(paymentDetails.amount) : paymentDetails.amount,
        currency: paymentDetails.currency,
        method: paymentDetails.method,
        status: paymentDetails.status
      }
    );

    logger.info(`Payment verified and processed successfully - User ID: ${userId}, Transaction ID: ${transaction_id}, Payment ID: ${razorpay_payment_id}, Amount: ${paymentDetails.amount}`);

    return res.status(200).json(successResponse(result, 'Payment verified and wallet updated successfully'));
  } catch (err: any) {
    logger.error(`Error verifying payment: ${err.message} - User ID: ${req.user?.userId}, Body: ${JSON.stringify(req.body)}`);
    return res.status(500).json(errorResponse('Payment verification failed'));
  }
};
