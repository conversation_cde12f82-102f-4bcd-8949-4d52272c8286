/// Test script to verify payment API endpoints are working correctly
/// Run this to test the payment flow without the full Flutter app
library;

import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

void main() async {
  print('🧪 Testing WiggyZ Payment API Endpoints\n');

  const baseUrl = 'http://127.0.0.1:5000/api/v1';
  
  // Test 1: Health check
  print('📡 Test 1: Health Check');
  try {
    final response = await http.get(
      Uri.parse('http://127.0.0.1:5000/health'),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );
    
    print('✅ Health check successful');
    print('   Status: ${response.statusCode}');
    print('   Response: ${response.body}');
    print('   Headers: ${response.headers}');
  } catch (e) {
    print('❌ Health check failed: $e');
    return;
  }

  // Test 2: Wallet endpoint without auth (should return 401)
  print('\n📡 Test 2: Wallet Endpoint (No Auth)');
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/wallet'),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );
    
    print('✅ Wallet endpoint reachable');
    print('   Status: ${response.statusCode}');
    print('   Response: ${response.body}');
    print('   Headers: ${response.headers}');
    
    if (response.statusCode == 401) {
      print('   ✅ Expected 401 - Authentication required');
    }
  } catch (e) {
    print('❌ Wallet endpoint test failed: $e');
  }

  // Test 3: Top-up endpoint without auth (should return 401)
  print('\n📡 Test 3: Top-up Endpoint (No Auth)');
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/wallet/topup'),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: jsonEncode({
        'amount': 100,
        'currency': 'INR',
        'payment_method': 'upi',
      }),
    );
    
    print('✅ Top-up endpoint reachable');
    print('   Status: ${response.statusCode}');
    print('   Response: ${response.body}');
    print('   Headers: ${response.headers}');
    
    if (response.statusCode == 401) {
      print('   ✅ Expected 401 - Authentication required');
    }
  } catch (e) {
    print('❌ Top-up endpoint test failed: $e');
    
    // Check if it's the encoding error
    if (e.toString().contains('UTF-F-8')) {
      print('   🚨 ENCODING ERROR DETECTED: UTF-F-8 issue found!');
      print('   💡 This suggests the charset fix hasn\'t been applied correctly');
    }
  }

  // Test 4: Test with mock auth token
  print('\n📡 Test 4: Top-up Endpoint (Mock Auth)');
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/wallet/topup'),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer mock-token-for-testing',
      },
      body: jsonEncode({
        'amount': 100,
        'currency': 'INR',
        'payment_method': 'upi',
      }),
    );
    
    print('✅ Top-up endpoint with auth reachable');
    print('   Status: ${response.statusCode}');
    print('   Response: ${response.body}');
    print('   Headers: ${response.headers}');
    
    if (response.statusCode == 401) {
      print('   ✅ Expected 401 - Invalid token');
    } else if (response.statusCode == 400) {
      print('   ✅ Expected 400 - Validation error (good sign!)');
    }
  } catch (e) {
    print('❌ Top-up endpoint with auth test failed: $e');
    
    // Check if it's the encoding error
    if (e.toString().contains('UTF-F-8')) {
      print('   🚨 ENCODING ERROR STILL PRESENT: UTF-F-8 issue found!');
      print('   💡 The charset fix may not have been applied correctly');
    }
  }

  print('\n📝 Test Summary:');
  print('1. If all tests show ✅ and expected status codes, the API is working');
  print('2. If you see 🚨 ENCODING ERROR, the charset fix needs to be reapplied');
  print('3. Status 401 is expected for authentication-required endpoints');
  print('4. Status 400 with mock auth is a good sign (validation working)');
  
  print('\n🎯 Next Steps:');
  print('1. If tests pass, try the Flutter app payment flow');
  print('2. Use test card: 4111 1111 1111 1111');
  print('3. Monitor backend logs during payment testing');
}
