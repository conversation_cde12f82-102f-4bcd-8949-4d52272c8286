import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:wiggyz_app/core/api/api_config.dart';
import 'package:wiggyz_app/services/auth_service.dart'; // Import AuthService

class WalletService {
  final AuthService _authService = AuthService(); // Instantiate AuthService

  Future<String?> _getAuthToken() async {
    return await _authService.getToken(); // Use AuthService to get the token
  }

  Future<Map<String, String>> _getHeaders() async {
    final token = await _getAuthToken();
    final headers = {
      'Content-Type': 'application/json; charset=UTF-8',
    };
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  Future<Map<String, dynamic>> getWalletDetails() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.baseUrl + ApiConfig.getWalletDetailsEndpoint),
        headers: await _getHeaders(),
      ).timeout(ApiConfig.connectionTimeout);

      if (response.statusCode == 200) {
        final decodedResponse = jsonDecode(response.body);
        // Expecting response like: { status: 'success', data: { balance: ..., currency: ... }, message: '...' }
        if (decodedResponse is Map<String, dynamic> && 
            decodedResponse.containsKey('data') && 
            decodedResponse['data'] is Map<String, dynamic>) {
          return decodedResponse['data'] as Map<String, dynamic>;
        } else {
          print('Unexpected wallet details response format: ${response.body}');
          throw Exception('Unexpected response format for wallet details');
        }
      } else if (response.statusCode == 401) {
        // TODO: Handle token refresh or re-authentication
        throw Exception('Unauthorized: Token may be invalid or expired.');
      } else {
        throw Exception('Failed to load wallet details: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      // TODO: Implement more sophisticated error logging/handling
      print('Error in getWalletDetails: $e');
      throw Exception('Failed to connect to the server or other error: $e');
    }
  }

  Future<List<dynamic>> getWalletTransactions({int page = 1, int limit = 10}) async {
    try {
      final url = '${ApiConfig.baseUrl}${ApiConfig.getWalletTransactionsEndpoint}?page=$page&limit=$limit';
      print('🔍 DEBUG: Making API request to: $url');

      final headers = await _getHeaders();
      print('🔍 DEBUG: Request headers: $headers');

      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      ).timeout(ApiConfig.connectionTimeout);

      print('🔍 DEBUG: API Response:');
      print('  - Status Code: ${response.statusCode}');
      print('  - Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final decodedResponse = jsonDecode(response.body);
        print('🔍 DEBUG: Decoded response type: ${decodedResponse.runtimeType}');
        print('🔍 DEBUG: Decoded response keys: ${decodedResponse is Map ? decodedResponse.keys.toList() : 'Not a Map'}');

        // Expecting response like: { status: 'success', data: [...], message: '...', meta: {...} }
        if (decodedResponse is Map<String, dynamic> && decodedResponse.containsKey('data') && decodedResponse['data'] is List) {
            final transactions = decodedResponse['data'] as List<dynamic>;
            print('🔍 DEBUG: Extracted ${transactions.length} transactions from API response');

            // Log first transaction for structure analysis
            if (transactions.isNotEmpty) {
              print('🔍 DEBUG: First transaction structure: ${transactions[0]}');
            }

            return transactions;
        } else {
            // Log the actual response for easier debugging if the format is still not as expected
            print('❌ DEBUG: Unexpected transaction response format: ${response.body}');
            throw Exception('Unexpected response format for transactions');
        }
      } else if (response.statusCode == 401) {
        print('❌ DEBUG: Unauthorized response (401)');
        // TODO: Handle token refresh or re-authentication
        throw Exception('Unauthorized: Token may be invalid or expired.');
      } else {
        print('❌ DEBUG: API Error - Status: ${response.statusCode}, Body: ${response.body}');
        throw Exception('Failed to load wallet transactions: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      // TODO: Implement more sophisticated error logging/handling
      print('❌ DEBUG: Exception in getWalletTransactions: $e');
      print('❌ DEBUG: Exception type: ${e.runtimeType}');
      throw Exception('Failed to connect to the server or other error: $e');
    }
  }

  Future<Map<String, dynamic>> topUpWallet(Map<String, dynamic> topUpData) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConfig.baseUrl + ApiConfig.topUpWalletEndpoint),
        headers: await _getHeaders(),
        body: jsonEncode(topUpData),
      ).timeout(ApiConfig.connectionTimeout);

      if (response.statusCode == 200 || response.statusCode == 201) { // 201 for successful creation
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else if (response.statusCode == 401) {
        // TODO: Handle token refresh or re-authentication
        throw Exception('Unauthorized: Token may be invalid or expired.');
      } else if (response.statusCode == 400) {
        // Handle validation errors or other bad requests
        final errorBody = jsonDecode(response.body);
        throw Exception('Failed to top up wallet: ${errorBody['message'] ?? response.body}');
      } else {
        throw Exception('Failed to top up wallet: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      // TODO: Implement more sophisticated error logging/handling
      print('Error in topUpWallet: $e');
      throw Exception('Failed to connect to the server or other error: $e');
    }
  }

  Future<Map<String, dynamic>> withdrawFromWallet(Map<String, dynamic> withdrawalData) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConfig.baseUrl + ApiConfig.withdrawWalletEndpoint),
        headers: await _getHeaders(),
        body: jsonEncode(withdrawalData),
      ).timeout(ApiConfig.connectionTimeout);

      if (response.statusCode == 200) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else if (response.statusCode == 401) {
        // TODO: Handle token refresh or re-authentication
        throw Exception('Unauthorized: Token may be invalid or expired.');
      } else if (response.statusCode == 400) {
        // Handle validation errors or other bad requests (e.g., insufficient funds)
        final errorBody = jsonDecode(response.body);
        throw Exception('Failed to withdraw from wallet: ${errorBody['message'] ?? response.body}');
      } else {
        throw Exception('Failed to withdraw from wallet: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      // TODO: Implement more sophisticated error logging/handling
      print('Error in withdrawFromWallet: $e');
      throw Exception('Failed to connect to the server or other error: $e');
    }
  }

  Future<Map<String, dynamic>> verifyPayment(Map<String, dynamic> paymentData) async {
    try {
      print('Verifying payment with endpoint: ${ApiConfig.baseUrl + ApiConfig.verifyPaymentEndpoint}');
      print('Payment data: $paymentData');

      final response = await http.post(
        Uri.parse(ApiConfig.baseUrl + ApiConfig.verifyPaymentEndpoint),
        headers: await _getHeaders(),
        body: jsonEncode(paymentData),
      ).timeout(ApiConfig.connectionTimeout);

      print('Payment verification response - Status: ${response.statusCode}, Body: ${response.body}');

      if (response.statusCode == 200) {
        try {
          final decodedResponse = jsonDecode(response.body);
          if (decodedResponse is Map<String, dynamic> &&
              decodedResponse.containsKey('data') &&
              decodedResponse['data'] is Map<String, dynamic>) {
            return decodedResponse['data'] as Map<String, dynamic>;
          } else {
            print('Unexpected payment verification response format: ${response.body}');
            throw Exception('Unexpected response format for payment verification');
          }
        } catch (e) {
          print('Failed to parse JSON response: $e');
          print('Response body: ${response.body}');
          throw Exception('Failed to parse server response: $e');
        }
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Token may be invalid or expired.');
      } else if (response.statusCode == 404) {
        throw Exception('Payment verification endpoint not found. Please check the API configuration.');
      } else {
        try {
          final errorBody = jsonDecode(response.body);
          final errorMessage = errorBody['message'] ?? 'Payment verification failed';
          throw Exception(errorMessage);
        } catch (e) {
          // If we can't parse the error response as JSON, it might be HTML
          print('Failed to parse error response as JSON: $e');
          print('Error response body: ${response.body}');
          throw Exception('Payment verification failed with status ${response.statusCode}. Server returned non-JSON response.');
        }
      }
    } catch (e) {
      print('Error in verifyPayment: $e');
      print('Payment data that failed: $paymentData');

      // Provide more specific error message for common issues
      if (e.toString().contains('Failed to host lookup') || e.toString().contains('No address associated with hostname')) {
        throw Exception('Failed to connect to payment server. Please check your internet connection.');
      } else if (e.toString().contains('Connection timed out')) {
        throw Exception('Payment verification timed out. Please try again.');
      } else {
        throw Exception('Failed to verify payment: $e');
      }
    }
  }
}
