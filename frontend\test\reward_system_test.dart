/// Comprehensive test suite for the reward system
/// Tests models, services, providers, and edge cases
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:wiggyz_app/models/reward_models.dart';
import 'package:wiggyz_app/models/daily_reward_models.dart';
import 'package:wiggyz_app/models/achievement_models.dart';
import 'package:wiggyz_app/models/loyalty_models.dart';
import 'package:wiggyz_app/services/reward_service.dart';
import 'package:wiggyz_app/providers/reward_provider.dart';
import 'package:wiggyz_app/utils/reward_error_handler.dart';
import 'package:wiggyz_app/utils/reward_edge_case_handler.dart';
import 'package:wiggyz_app/utils/reward_cache_manager.dart';
import 'package:wiggyz_app/utils/offline_reward_manager.dart';
import 'package:wiggyz_app/services/auth_service.dart';

// Generate mocks
@GenerateMocks([RewardService, AuthService])
import 'reward_system_test.mocks.dart';

void main() {
  group('Reward Models Tests', () {
    group('RewardModel', () {
      test('should create reward model from JSON correctly', () {
        final json = {
          'id': 'reward_1',
          'type_id': 'daily_login',
          'title': 'Daily Login Reward',
          'description': 'Login reward for day 1',
          'points': 100,
          'diamond_value': 50,
          'requirements': {'day': 1},
          'is_active': true,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z',
        };

        final reward = RewardModel.fromJson(json);

        expect(reward.id, 'reward_1');
        expect(reward.title, 'Daily Login Reward');
        expect(reward.points, 100);
        expect(reward.diamondValue, 50);
        expect(reward.dayRequirement, 1);
        expect(reward.isActive, true);
        expect(reward.isAvailable, true);
      });

      test('should handle null requirements correctly', () {
        final json = {
          'id': 'reward_1',
          'type_id': 'daily_login',
          'title': 'Daily Login Reward',
          'points': 100,
          'diamond_value': 50,
          'is_active': true,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z',
        };

        final reward = RewardModel.fromJson(json);

        expect(reward.dayRequirement, null);
        expect(reward.requirements, null);
      });

      test('should check availability correctly with date constraints', () {
        final futureDate = DateTime.now().add(const Duration(days: 1));
        final pastDate = DateTime.now().subtract(const Duration(days: 1));

        final json = {
          'id': 'reward_1',
          'type_id': 'daily_login',
          'title': 'Future Reward',
          'points': 100,
          'diamond_value': 50,
          'is_active': true,
          'start_date': futureDate.toIso8601String(),
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z',
        };

        final futureReward = RewardModel.fromJson(json);
        expect(futureReward.isAvailable, false);

        json['start_date'] = pastDate.toIso8601String();
        json['end_date'] = futureDate.toIso8601String();
        final activeReward = RewardModel.fromJson(json);
        expect(activeReward.isAvailable, true);

        json['end_date'] = pastDate.toIso8601String();
        final expiredReward = RewardModel.fromJson(json);
        expect(expiredReward.isAvailable, false);
      });
    });

    group('LoginStreakModel', () {
      test('should detect if user logged in today', () {
        final today = DateTime.now();
        final yesterday = today.subtract(const Duration(days: 1));

        final json = {
          'id': 'streak_1',
          'user_id': 'user_1',
          'current_streak': 5,
          'longest_streak': 10,
          'last_login_date': today.toIso8601String(),
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': today.toIso8601String(),
        };

        final streak = LoginStreakModel.fromJson(json);
        expect(streak.hasLoggedInToday, true);

        json['last_login_date'] = yesterday.toIso8601String();
        final yesterdayStreak = LoginStreakModel.fromJson(json);
        expect(yesterdayStreak.hasLoggedInToday, false);
      });

      test('should detect broken streak correctly', () {
        final twoDaysAgo = DateTime.now().subtract(const Duration(days: 2));

        final json = {
          'id': 'streak_1',
          'user_id': 'user_1',
          'current_streak': 5,
          'longest_streak': 10,
          'last_login_date': twoDaysAgo.toIso8601String(),
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': twoDaysAgo.toIso8601String(),
        };

        final streak = LoginStreakModel.fromJson(json);
        expect(streak.isStreakBroken, true);
      });
    });

    group('AchievementModel', () {
      test('should calculate progress percentage correctly', () {
        final json = {
          'id': 'achievement_1',
          'user_id': 'user_1',
          'title': 'Win 10 Games',
          'description': 'Win 10 games to unlock this achievement',
          'target': 10,
          'current': 7,
          'is_completed': false,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z',
        };

        final achievement = AchievementModel.fromJson(json);
        expect(achievement.progressPercentage, 0.7);
        expect(achievement.remainingProgress, 3);
        expect(achievement.isReadyToClaim, false);
      });

      test('should detect ready to claim status', () {
        final json = {
          'id': 'achievement_1',
          'user_id': 'user_1',
          'title': 'Win 10 Games',
          'description': 'Win 10 games to unlock this achievement',
          'target': 10,
          'current': 10,
          'is_completed': false,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z',
        };

        final achievement = AchievementModel.fromJson(json);
        expect(achievement.isReadyToClaim, true);
        expect(achievement.progressPercentage, 1.0);
      });
    });

    group('DailyRewardsCalendar', () {
      test('should create calendar from status correctly', () {
        final streak = LoginStreakModel(
          id: 'streak_1',
          userId: 'user_1',
          currentStreak: 3,
          longestStreak: 5,
          lastLoginDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final rewards = [
          RewardModel(
            id: 'reward_1',
            typeId: 'daily_login',
            title: 'Day 1 Reward',
            points: 100,
            diamondValue: 50,
            requirements: {'day': 1},
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          RewardModel(
            id: 'reward_2',
            typeId: 'daily_login',
            title: 'Day 2 Reward',
            points: 150,
            diamondValue: 75,
            requirements: {'day': 2},
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          RewardModel(
            id: 'reward_3',
            typeId: 'daily_login',
            title: 'Day 3 Reward',
            points: 200,
            diamondValue: 100,
            requirements: {'day': 3},
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        final status = DailyRewardStatusModel(
          streak: streak,
          rewards: rewards,
          hasClaimedToday: false,
        );

        final calendar = DailyRewardsCalendar.fromStatus(status);

        expect(calendar.days.length, 3);
        expect(calendar.currentDay?.day, 3);
        expect(calendar.canClaimToday, true);
        expect(calendar.claimedDaysCount, 2); // Days 1 and 2 should be claimed
      });
    });
  });

  group('Error Handling Tests', () {
    test('should create error from HTTP response correctly', () {
      final error400 = RewardError.fromHttpResponse(400, 'Bad request');
      expect(error400.type, RewardErrorType.validationError);
      expect(error400.statusCode, 400);

      final error401 = RewardError.fromHttpResponse(401, 'Unauthorized');
      expect(error401.type, RewardErrorType.authenticationError);
      expect(error401.type.requiresReauth, true);

      final error429 = RewardError.fromHttpResponse(429, 'Rate limited');
      expect(error429.type, RewardErrorType.rateLimitError);
      expect(error429.type.isRetryable, true);
    });

    test('should create error from API message correctly', () {
      final alreadyClaimedError = RewardError.fromApiMessage('Reward already claimed');
      expect(alreadyClaimedError.type, RewardErrorType.rewardAlreadyClaimed);

      final expiredError = RewardError.fromApiMessage('Reward has expired');
      expect(expiredError.type, RewardErrorType.rewardExpired);

      final notFoundError = RewardError.fromApiMessage('Reward not found');
      expect(notFoundError.type, RewardErrorType.rewardNotFound);
    });

    test('should handle network exceptions correctly', () {
      final networkError = RewardError.fromException('SocketException: Network unreachable');
      expect(networkError.type, RewardErrorType.networkError);
      expect(networkError.type.isRetryable, true);
    });
  });

  group('Validation Tests', () {
    test('should validate daily reward claim correctly', () {
      // Already claimed today
      final alreadyClaimedError = RewardValidator.validateDailyRewardClaim(
        hasClaimedToday: true,
        isStreakActive: true,
        lastLoginDate: DateTime.now(),
      );
      expect(alreadyClaimedError, isNotNull);
      expect(alreadyClaimedError!.contains('already claimed'), true);

      // Valid claim
      final validClaim = RewardValidator.validateDailyRewardClaim(
        hasClaimedToday: false,
        isStreakActive: true,
        lastLoginDate: DateTime.now().subtract(const Duration(hours: 12)),
      );
      expect(validClaim, isNull);

      // Broken streak
      final brokenStreakWarning = RewardValidator.validateDailyRewardClaim(
        hasClaimedToday: false,
        isStreakActive: false,
        lastLoginDate: DateTime.now().subtract(const Duration(days: 2)),
      );
      expect(brokenStreakWarning, isNotNull);
      expect(brokenStreakWarning!.contains('streak was broken'), true);
    });

    test('should validate achievement progress correctly', () {
      // Already completed
      final completedError = RewardValidator.validateAchievementProgress(
        isCompleted: true,
        currentProgress: 10,
        targetProgress: 10,
        progressIncrement: 1,
      );
      expect(completedError, isNotNull);

      // Invalid increment
      final invalidIncrementError = RewardValidator.validateAchievementProgress(
        isCompleted: false,
        currentProgress: 5,
        targetProgress: 10,
        progressIncrement: 0,
      );
      expect(invalidIncrementError, isNotNull);

      // Valid update
      final validUpdate = RewardValidator.validateAchievementProgress(
        isCompleted: false,
        currentProgress: 5,
        targetProgress: 10,
        progressIncrement: 2,
      );
      expect(validUpdate, isNull);
    });

    test('should validate reward claim correctly', () {
      // Inactive reward
      final inactiveError = RewardValidator.validateRewardClaim(
        isActive: false,
        startDate: null,
        endDate: null,
        alreadyClaimed: false,
      );
      expect(inactiveError, isNotNull);

      // Already claimed
      final claimedError = RewardValidator.validateRewardClaim(
        isActive: true,
        startDate: null,
        endDate: null,
        alreadyClaimed: true,
      );
      expect(claimedError, isNotNull);

      // Future reward
      final futureError = RewardValidator.validateRewardClaim(
        isActive: true,
        startDate: DateTime.now().add(const Duration(days: 1)),
        endDate: null,
        alreadyClaimed: false,
      );
      expect(futureError, isNotNull);

      // Expired reward
      final expiredError = RewardValidator.validateRewardClaim(
        isActive: true,
        startDate: null,
        endDate: DateTime.now().subtract(const Duration(days: 1)),
        alreadyClaimed: false,
      );
      expect(expiredError, isNotNull);

      // Valid claim
      final validClaim = RewardValidator.validateRewardClaim(
        isActive: true,
        startDate: DateTime.now().subtract(const Duration(days: 1)),
        endDate: DateTime.now().add(const Duration(days: 1)),
        alreadyClaimed: false,
      );
      expect(validClaim, isNull);
    });
  });

  group('RewardProvider Tests', () {
    late MockRewardService mockRewardService;
    late MockAuthService mockAuthService;
    late RewardProvider rewardProvider;

    setUp(() {
      mockRewardService = MockRewardService();
      mockAuthService = MockAuthService();
      rewardProvider = RewardProvider(mockAuthService);
      // Replace the service with our mock
      // Note: This would require making the service injectable or using dependency injection
    });

    test('should handle daily reward claim success', () async {
      // Mock successful claim
      final claimResult = DailyRewardClaimResult(
        success: true,
        claimedReward: RewardModel(
          id: 'reward_1',
          typeId: 'daily_login',
          title: 'Daily Reward',
          points: 100,
          diamondValue: 50,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        diamondsEarned: 50,
        pointsEarned: 100,
      );

      when(mockRewardService.claimDailyReward(idempotencyKey: anyNamed('idempotencyKey')))
          .thenAnswer((_) async => claimResult);

      // This test would need the provider to use the mock service
      // Implementation depends on how dependency injection is set up
    });
  });

  group('Edge Case Handler Tests', () {
    test('should handle expired daily rewards correctly', () {
      final twoDaysAgo = DateTime.now().subtract(const Duration(days: 2));

      final streak = LoginStreakModel(
        id: 'streak_1',
        userId: 'user_1',
        currentStreak: 5,
        longestStreak: 10,
        lastLoginDate: twoDaysAgo,
        createdAt: DateTime.now(),
        updatedAt: twoDaysAgo,
      );

      final status = DailyRewardStatusModel(
        streak: streak,
        rewards: [],
        hasClaimedToday: false,
      );

      final handledStatus = RewardEdgeCaseHandler.handleExpiredDailyRewards(status);

      expect(handledStatus, isNotNull);
      expect(handledStatus!.streak.currentStreak, 0); // Streak should be reset
      expect(handledStatus.hasClaimedToday, false);
    });

    test('should filter expired user rewards', () {
      final now = DateTime.now();
      final expiredDate = now.subtract(const Duration(days: 1));
      final futureDate = now.add(const Duration(days: 1));

      final rewards = [
        UserRewardModel(
          id: 'reward_1',
          userId: 'user_1',
          rewardId: 'r1',
          claimedAt: now,
          expiryDate: futureDate, // Valid
        ),
        UserRewardModel(
          id: 'reward_2',
          userId: 'user_1',
          rewardId: 'r2',
          claimedAt: now,
          expiryDate: expiredDate, // Expired
        ),
        UserRewardModel(
          id: 'reward_3',
          userId: 'user_1',
          rewardId: 'r3',
          claimedAt: now,
          // No expiry date - valid
        ),
      ];

      final validRewards = RewardEdgeCaseHandler.filterExpiredRewards(rewards);

      expect(validRewards.length, 2);
      expect(validRewards.any((r) => r.id == 'reward_1'), true);
      expect(validRewards.any((r) => r.id == 'reward_3'), true);
      expect(validRewards.any((r) => r.id == 'reward_2'), false);
    });

    test('should validate achievement progress inconsistencies', () {
      final achievements = [
        AchievementModel(
          id: 'achievement_1',
          userId: 'user_1',
          title: 'Test Achievement',
          description: 'Test',
          target: 10,
          current: 15, // Exceeds target
          isCompleted: false, // Should be true
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        AchievementModel(
          id: 'achievement_2',
          userId: 'user_1',
          title: 'Test Achievement 2',
          description: 'Test',
          target: 10,
          current: -5, // Negative progress
          isCompleted: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      final validated = RewardEdgeCaseHandler.validateAchievementProgress(achievements);

      expect(validated[0].current, 10); // Should be clamped to target
      expect(validated[0].isCompleted, true); // Should be marked complete
      expect(validated[1].current, 0); // Should be reset to 0
    });

    test('should validate loyalty tier consistency', () {
      final tiers = [
        LoyaltyTierModel(
          id: 'tier_1',
          name: 'Bronze',
          minPoints: 0,
          benefits: [],
          createdAt: DateTime.now(),
        ),
        LoyaltyTierModel(
          id: 'tier_2',
          name: 'Silver',
          minPoints: 100,
          benefits: [],
          createdAt: DateTime.now(),
        ),
        LoyaltyTierModel(
          id: 'tier_3',
          name: 'Gold',
          minPoints: 500,
          benefits: [],
          createdAt: DateTime.now(),
        ),
      ];

      final loyaltyStatus = UserLoyaltyModel(
        id: 'loyalty_1',
        userId: 'user_1',
        points: 250,
        tierId: 'tier_1', // Should be tier_2
        updatedAt: DateTime.now(),
      );

      final validated = RewardEdgeCaseHandler.validateLoyaltyStatus(loyaltyStatus, tiers);

      expect(validated, isNotNull);
      expect(validated!.tierId, 'tier_2'); // Should be corrected
    });

    test('should handle reward claiming race conditions', () {
      final reward = RewardModel(
        id: 'reward_1',
        typeId: 'daily_login',
        title: 'Daily Reward',
        points: 100,
        diamondValue: 50,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final userRewards = [
        UserRewardModel(
          id: 'ur_1',
          userId: 'user_1',
          rewardId: 'reward_1', // Already claimed
          claimedAt: DateTime.now(),
        ),
      ];

      final canClaim = RewardEdgeCaseHandler.canClaimReward(
        reward: reward,
        userRewards: userRewards,
        lastClaimTime: null,
      );

      expect(canClaim, false); // Should not be able to claim again
    });

    test('should remove duplicate entries', () {
      final rewards = [
        UserRewardModel(
          id: 'reward_1',
          userId: 'user_1',
          rewardId: 'r1',
          claimedAt: DateTime.now(),
        ),
        UserRewardModel(
          id: 'reward_1', // Duplicate ID
          userId: 'user_1',
          rewardId: 'r1',
          claimedAt: DateTime.now(),
        ),
        UserRewardModel(
          id: 'reward_2',
          userId: 'user_1',
          rewardId: 'r2',
          claimedAt: DateTime.now(),
        ),
      ];

      final unique = RewardEdgeCaseHandler.removeDuplicates(
        rewards,
        (reward) => reward.id,
      );

      expect(unique.length, 2);
      expect(unique.map((r) => r.id).toSet(), {'reward_1', 'reward_2'});
    });
  });

  group('Cache Manager Tests', () {
    late RewardCacheManager cacheManager;

    setUp(() {
      cacheManager = RewardCacheManager();
    });

    test('should cache and retrieve daily reward status', () async {
      final streak = LoginStreakModel(
        id: 'streak_1',
        userId: 'user_1',
        currentStreak: 3,
        longestStreak: 5,
        lastLoginDate: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final status = DailyRewardStatusModel(
        streak: streak,
        rewards: [],
        hasClaimedToday: false,
      );

      // This test would need SharedPreferences mocking
      // await cacheManager.cacheDailyRewardStatus(status);
      // final cached = cacheManager.getCachedDailyRewardStatus();
      // expect(cached, isNotNull);
    });
  });

  group('Offline Manager Tests', () {
    late OfflineRewardManager offlineManager;

    setUp(() {
      offlineManager = OfflineRewardManager();
    });

    test('should queue daily reward claim operation', () async {
      // This test would need SharedPreferences and Connectivity mocking
      // await offlineManager.queueDailyRewardClaim(idempotencyKey: 'test_key');
      // expect(offlineManager.pendingOperationsCount, 1);
    });

    test('should queue achievement progress operation', () async {
      // await offlineManager.queueAchievementProgress('achievement_1', 5);
      // expect(offlineManager.pendingOperationsCount, 1);
    });
  });

  group('Integration Tests', () {
    test('should handle complete reward claiming flow', () async {
      // This would test the entire flow from UI action to backend update
      // Including error handling, caching, and notifications
    });

    test('should handle offline to online sync', () async {
      // Test queuing operations offline and syncing when online
    });

    test('should handle concurrent reward claims', () async {
      // Test race condition handling for simultaneous claims
    });
  });
}
