/// Main rewards screen with daily rewards, achievements, and loyalty overview
/// Central hub for all reward-related features and navigation
library;

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../providers/reward_provider.dart';
import '../theme_provider.dart';
import '../widgets/daily_reward_card.dart';
import '../widgets/shared/golden_app_bar.dart';
import '../screens/daily_rewards_screen.dart';
import '../screens/achievement_details_screen.dart';

class RewardsScreen extends StatefulWidget {
  const RewardsScreen({super.key});

  @override
  State<RewardsScreen> createState() => _RewardsScreenState();
}

class _RewardsScreenState extends State<RewardsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
    
    // Initialize reward data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<RewardProvider>().refreshAllData();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      appBar: GoldenAppBar(
        title: 'Rewards',
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<RewardProvider>().refreshAllData();
            },
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<RewardProvider>(
          builder: (context, rewardProvider, child) {
            if (rewardProvider.isLoading && !rewardProvider.isInitialized) {
              return _buildLoadingState(isDarkMode);
            }

            return RefreshIndicator(
              onRefresh: () => rewardProvider.refreshAllData(),
              color: const Color(0xFFFFCC00),
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildWelcomeHeader(isDarkMode, rewardProvider),
                    const SizedBox(height: 20),
                    _buildDailyRewardsSection(isDarkMode, rewardProvider),
                    const SizedBox(height: 20),
                    _buildLoyaltySection(isDarkMode, rewardProvider),
                    const SizedBox(height: 20),
                    _buildAchievementsSection(isDarkMode, rewardProvider),
                    const SizedBox(height: 20),
                    _buildRecentRewardsSection(isDarkMode, rewardProvider),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: const Color(0xFFFFCC00),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading rewards...',
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeHeader(bool isDarkMode, RewardProvider rewardProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFFFFCC00),
            const Color(0xFFFFCC00).withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFFCC00).withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.card_giftcard,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome to Rewards!',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Earn points, climb tiers, and unlock exclusive benefits',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDailyRewardsSection(bool isDarkMode, RewardProvider rewardProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Daily Rewards',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DailyRewardsScreen(),
                  ),
                );
              },
              child: Text(
                'View All',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFFFFCC00),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: rewardProvider.isDailyRewardsLoading
              ? _buildDailyRewardsLoading(isDarkMode)
              : rewardProvider.dailyRewardsCalendar != null
                  ? _buildDailyRewardsContent(isDarkMode, rewardProvider)
                  : _buildDailyRewardsError(isDarkMode),
        ),
      ],
    );
  }

  Widget _buildDailyRewardsLoading(bool isDarkMode) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: CircularProgressIndicator(
          color: const Color(0xFFFFCC00),
        ),
      ),
    );
  }

  Widget _buildDailyRewardsContent(bool isDarkMode, RewardProvider rewardProvider) {
    final calendar = rewardProvider.dailyRewardsCalendar!;
    
    return Column(
      children: [
        if (calendar.streak.currentStreak > 0)
          StreakIndicator(
            currentStreak: calendar.streak.currentStreak,
            longestStreak: calendar.streak.longestStreak,
            isDarkMode: isDarkMode,
          ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                isDarkMode,
                'Current Streak',
                '${calendar.streak.currentStreak} days',
                Icons.local_fire_department,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                isDarkMode,
                'This Week',
                '${calendar.claimedDaysCount}/${calendar.totalDaysCount}',
                Icons.calendar_today,
                const Color(0xFFFFCC00),
              ),
            ),
          ],
        ),
        if (calendar.canClaimToday) ...[
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFFFCC00).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFFFCC00).withOpacity(0.3),
              ),
            ),
            child: Column(
              children: [
                Text(
                  'Today\'s Reward Available!',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFFFFCC00),
                  ),
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () async {
                    debugPrint('🎁 Claim Now button pressed in rewards screen');
                    debugPrint('🔍 Can claim: ${rewardProvider.canClaimDailyReward}');

                    if (!rewardProvider.canClaimDailyReward) {
                      debugPrint('❌ Cannot claim - conditions not met');
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Daily reward cannot be claimed at this time'),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      }
                      return;
                    }

                    try {
                      final success = await rewardProvider.claimDailyReward();
                      debugPrint('🎯 Claim result from rewards screen: $success');

                      if (success && mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Daily reward claimed!'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      } else if (mounted) {
                        final error = rewardProvider.dailyRewardsError ?? 'Failed to claim reward';
                        debugPrint('❌ Claim failed: $error');
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(error),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    } catch (e) {
                      debugPrint('💥 Exception in rewards screen claim: $e');
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Error: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFFCC00),
                    foregroundColor: Colors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Claim Now',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDailyRewardsError(bool isDarkMode) {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red[400],
          ),
          const SizedBox(height: 8),
          Text(
            'Failed to load daily rewards',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoyaltySection(bool isDarkMode, RewardProvider rewardProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Loyalty Status',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: rewardProvider.isLoyaltyLoading
              ? _buildLoyaltyLoading(isDarkMode)
              : rewardProvider.loyaltyStatus != null
                  ? _buildLoyaltyContent(isDarkMode, rewardProvider)
                  : _buildLoyaltyError(isDarkMode),
        ),
      ],
    );
  }

  Widget _buildLoyaltyLoading(bool isDarkMode) {
    return Center(
      child: CircularProgressIndicator(
        color: const Color(0xFFFFCC00),
      ),
    );
  }

  Widget _buildLoyaltyContent(bool isDarkMode, RewardProvider rewardProvider) {
    final loyalty = rewardProvider.loyaltyStatus!;
    final tier = loyalty.loyaltyTier;
    
    return Column(
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: tier?.displayColor.withOpacity(0.2) ?? Colors.grey.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                tier?.displayIcon ?? Icons.military_tech,
                color: tier?.displayColor ?? Colors.grey,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    tier?.name ?? 'Unknown Tier',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  Text(
                    '${loyalty.points} points',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: tier?.displayColor ?? Colors.grey,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // View All Rewards Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              context.push('/loyalty/rewards-info');
            },
            icon: const Icon(
              Icons.card_giftcard,
              color: Colors.black,
              size: 18,
            ),
            label: Text(
              'View All Rewards',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFFCC00),
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
          ),
        ),
        if (rewardProvider.loyaltyTiers.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildLoyaltyProgress(isDarkMode, loyalty, rewardProvider.loyaltyTiers),
        ],
      ],
    );
  }

  Widget _buildLoyaltyProgress(bool isDarkMode, loyalty, List loyaltyTiers) {
    final progress = loyalty.getProgressToNextTier(loyaltyTiers);
    final nextTier = loyalty.getNextTier(loyaltyTiers);
    final pointsNeeded = loyalty.getPointsNeededForNextTier(loyaltyTiers);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress to ${nextTier?.name ?? 'Max Tier'}',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
            if (pointsNeeded > 0)
              Text(
                '$pointsNeeded points needed',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: const Color(0xFFFFCC00),
                  fontWeight: FontWeight.w600,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: isDarkMode ? Colors.grey[700] : Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            nextTier?.displayColor ?? const Color(0xFFFFCC00),
          ),
          minHeight: 6,
        ),
      ],
    );
  }

  Widget _buildLoyaltyError(bool isDarkMode) {
    return Center(
      child: Text(
        'Failed to load loyalty status',
        style: GoogleFonts.poppins(
          fontSize: 14,
          color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
        ),
      ),
    );
  }

  Widget _buildAchievementsSection(bool isDarkMode, RewardProvider rewardProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Achievements',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to achievements screen
              },
              child: Text(
                'View All',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFFFFCC00),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                isDarkMode,
                'Completed',
                '${rewardProvider.completedAchievements.length}',
                Icons.emoji_events,
                Colors.amber,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                isDarkMode,
                'Ready to Claim',
                '${rewardProvider.readyToClaimAchievements.length}',
                Icons.card_giftcard,
                const Color(0xFFFFCC00),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecentRewardsSection(bool isDarkMode, RewardProvider rewardProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Rewards',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: rewardProvider.userRewards.isEmpty
              ? Center(
                  child: Text(
                    'No recent rewards',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                )
              : Column(
                  children: rewardProvider.userRewards
                      .take(3)
                      .map((reward) => _buildRewardItem(isDarkMode, reward))
                      .toList(),
                ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    bool isDarkMode,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRewardItem(bool isDarkMode, reward) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFFFCC00).withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.card_giftcard,
              color: const Color(0xFFFFCC00),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  reward.reward?.title ?? 'Reward',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                Text(
                  'Claimed ${_formatDate(reward.claimedAt)}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            '+${reward.reward?.diamondValue ?? 0}',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: const Color(0xFFFFCC00),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'today';
    } else if (difference.inDays == 1) {
      return 'yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
