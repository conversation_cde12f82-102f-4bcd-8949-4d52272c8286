/**
 * Validation schemas for wallet-related operations
 */
import <PERSON><PERSON> from 'joi';

// Top-up request validation schema
export const topUpSchema = Joi.object({
  amount: Joi.number().positive().required().messages({
    'number.base': 'Amount must be a number',
    'number.positive': 'Amount must be greater than zero',
    'any.required': 'Amount is required'
  }),
  currency: Joi.string().length(3).default('USD').messages({
    'string.base': 'Currency must be a string',
    'string.length': 'Currency must be a 3-letter ISO code'
  }),
  payment_method: Joi.string().required().messages({
    'any.required': 'Payment method is required'
  })
});

// Withdrawal request validation schema
export const withdrawalSchema = Joi.object({
  amount: Joi.number().positive().required().messages({
    'number.base': 'Amount must be a number',
    'number.positive': 'Amount must be greater than zero',
    'any.required': 'Amount is required'
  }),
  currency: Joi.string().length(3).default('INR').messages({
    'string.base': 'Currency must be a string',
    'string.length': 'Currency must be a 3-letter ISO code'
  }),
  withdrawal_method: Joi.string().required().messages({
    'any.required': 'Withdrawal method is required'
  }),
  account_details: Joi.object().required().messages({
    'any.required': 'Account details are required'
  })
});

// Transaction query validation schema
export const transactionQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1).messages({
    'number.base': 'Page must be a number',
    'number.integer': 'Page must be an integer',
    'number.min': 'Page must be at least 1'
  }),
  limit: Joi.number().integer().min(1).max(100).default(10).messages({
    'number.base': 'Limit must be a number',
    'number.integer': 'Limit must be an integer',
    'number.min': 'Limit must be at least 1',
    'number.max': 'Limit cannot exceed 100'
  }),
  type: Joi.string().valid('all', 'deposit', 'withdrawal', 'purchase', 'refund', 'reward', 'adjustment').default('all').messages({
    'string.base': 'Type must be a string',
    'any.only': 'Type must be one of: all, deposit, withdrawal, purchase, refund, reward, adjustment'
  }),
  status: Joi.string().valid('all', 'pending', 'processing', 'completed', 'failed', 'cancelled').default('all').messages({
    'string.base': 'Status must be a string',
    'any.only': 'Status must be one of: all, pending, processing, completed, failed, cancelled'
  }),
  start_date: Joi.date().iso().messages({
    'date.base': 'Start date must be a valid date',
    'date.format': 'Start date must be in ISO format'
  }),
  end_date: Joi.date().iso().min(Joi.ref('start_date')).messages({
    'date.base': 'End date must be a valid date',
    'date.format': 'End date must be in ISO format',
    'date.min': 'End date must be after start date'
  })
});
