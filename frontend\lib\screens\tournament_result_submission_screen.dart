import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wiggyz_app/features/tournament_provider.dart';
import 'package:wiggyz_app/features/tournament_models.dart';
import 'package:wiggyz_app/services/image_upload_service.dart';
import 'package:wiggyz_app/services/tournament_status_service.dart';
import 'package:wiggyz_app/widgets/tournament_status_widget.dart';
import 'package:wiggyz_app/widgets/shared/golden_app_bar.dart';
import 'package:wiggyz_app/providers/tournament_match_state_provider.dart';

class TournamentResultSubmissionScreen extends StatefulWidget {
  final String tournamentId;

  const TournamentResultSubmissionScreen({
    super.key,
    required this.tournamentId,
  });

  @override
  State<TournamentResultSubmissionScreen> createState() =>
      _TournamentResultSubmissionScreenState();
}

class _TournamentResultSubmissionScreenState
    extends State<TournamentResultSubmissionScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _killsController = TextEditingController();
  final _rankController = TextEditingController();
  final _scoreController = TextEditingController();

  final List<XFile> _selectedImages = [];
  final List<Uint8List?> _selectedImageBytes = []; // For web compatibility
  bool _isSubmitting = false;
  bool _isLoading = true;
  Tournament? _tournament;
  bool _isDeadlinePassed = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  static const int maxScreenshots = 5; // Maximum number of screenshots allowed

  final ImagePicker _picker = ImagePicker();
  final ImageUploadService _imageUploadService = ImageUploadService();
  final TournamentStatusService _statusService = TournamentStatusService();

  bool _resultSubmitted = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadTournamentDetails();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  Future<void> _loadTournamentDetails() async {
    try {
      final tournamentProvider = Provider.of<TournamentProvider>(
        context,
        listen: false,
      );

      final tournament = await tournamentProvider.getTournamentById(widget.tournamentId);

      if (tournament != null) {
        setState(() {
          _tournament = tournament;
          _isLoading = false;
        });

        // Check if result submission deadline has passed
        _checkSubmissionDeadline();
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading tournament details: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _checkSubmissionDeadline() {
    if (_tournament == null) return;

    final now = DateTime.now();

    // Check tournament end date and allow submissions for 24 hours after tournament ends
    final endDate = _tournament!.endDate;
    final submissionCutoff = endDate.add(const Duration(hours: 24));
    if (now.isAfter(submissionCutoff)) {
      setState(() {
        _isDeadlinePassed = true;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _killsController.dispose();
    _rankController.dispose();
    _scoreController.dispose();
    _statusService.stopPolling(widget.tournamentId);
    super.dispose();
  }

  Future<void> _pickImage() async {
    if (_selectedImages.length >= maxScreenshots) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Maximum $maxScreenshots screenshots allowed'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(image);
        });

        if (kIsWeb) {
          final bytes = await image.readAsBytes();
          setState(() {
            _selectedImageBytes.add(bytes);
          });
        } else {
          setState(() {
            _selectedImageBytes.add(null);
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
      _selectedImageBytes.removeAt(index);
    });
  }

  Future<void> _submitResult() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Check deadline before proceeding
    if (_isDeadlinePassed) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Result submission deadline has passed. You can no longer submit results for this tournament.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_selectedImages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please upload at least one screenshot of your result'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final tournamentProvider = Provider.of<TournamentProvider>(
        context,
        listen: false,
      );

      // Upload all screenshots using tournament-specific upload method
      List<String> screenshotUrls = [];
      for (int i = 0; i < _selectedImages.length; i++) {
        try {
          final url = await _imageUploadService.uploadTournamentScreenshot(
            _selectedImages[i],
            widget.tournamentId,
          );
          screenshotUrls.add(url);
                } catch (e) {
          print('Error uploading tournament screenshot ${i + 1}: $e');
          // Continue with other screenshots even if one fails
        }
      }

      if (screenshotUrls.isEmpty) {
        throw Exception('Failed to upload any screenshots. Please try again.');
      }

      // Submit tournament result with multiple screenshots
      await tournamentProvider.submitTournamentResult(
        tournamentId: widget.tournamentId,
        finalPosition: int.parse(_rankController.text),
        finalScore:
            _scoreController.text.isNotEmpty
                ? int.parse(_scoreController.text)
                : null,
        killsCount:
            _killsController.text.isNotEmpty
                ? int.parse(_killsController.text)
                : null,
        screenshotUrl: screenshotUrls.first, // Primary screenshot for backward compatibility
        screenshotUrls: screenshotUrls, // All screenshots
      );

      // Update tournament match state to verification phase
      final matchStateProvider = Provider.of<TournamentMatchStateProvider>(context, listen: false);
      await matchStateProvider.submitTournamentResults(widget.tournamentId);

      if (mounted) {
        setState(() {
          _resultSubmitted = true;
        });

        // Start real-time status polling for verification updates
        _statusService.startResultSubmissionPolling(widget.tournamentId);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tournament result submitted successfully! Redirecting to verification status...'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Redirect to verification screen after a short delay
        print('=== Starting Redirect Timer ===');
        print('Tournament ID: ${widget.tournamentId}');
        print('Mounted: $mounted');

        Future.delayed(const Duration(seconds: 2), () {
          print('=== Redirect Timer Completed ===');
          print('Mounted: $mounted');
          if (mounted) {
            print('Redirecting to verification status screen...');
            context.pushReplacement('/tournaments/${widget.tournamentId}/verification-status');
          } else {
            print('Widget not mounted, skipping redirect');
          }
        });
      }
    } catch (e) {
      if (mounted) {
        final errorMessage = _parseErrorMessage(e.toString());
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Dismiss',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  String _parseErrorMessage(String error) {
    // Parse backend error codes and provide user-friendly messages
    if (error.contains('INVALID_POSITION')) {
      return 'Please enter a valid final position (1 or higher).';
    }
    if (error.contains('MISSING_SCREENSHOT')) {
      return 'At least one screenshot is required. Please upload a screenshot of your result.';
    }
    if (error.contains('INVALID_SCREENSHOT')) {
      return 'One or more screenshots are invalid. Please check your uploads and try again.';
    }
    if (error.contains('TOURNAMENT_NOT_FOUND')) {
      return 'Tournament not found. Please check if the tournament still exists.';
    }
    if (error.contains('NOT_REGISTERED')) {
      return 'You are not registered for this tournament. Please join the tournament first.';
    }
    if (error.contains('SPECTATOR_RESTRICTION')) {
      return 'Spectators cannot submit tournament results. Only participants can submit results.';
    }
    if (error.contains('DEADLINE_PASSED')) {
      return 'The result submission deadline has passed. You can no longer submit results.';
    }
    if (error.contains('TOURNAMENT_STATUS')) {
      return 'Result submission is not allowed for this tournament at this time.';
    }
    if (error.contains('ALREADY_SUBMITTED')) {
      return 'You have already submitted a result for this tournament. Contact support if you need to update it.';
    }
    if (error.contains('SUBMISSION_ERROR')) {
      // Extract the actual error message after the colon
      final parts = error.split('SUBMISSION_ERROR: ');
      if (parts.length > 1) {
        return 'Submission failed: ${parts[1]}';
      }
    }
    if (error.contains('UNKNOWN_ERROR')) {
      return 'An unexpected error occurred. Please try again or contact support if the problem persists.';
    }
    if (error.contains('Failed to upload any screenshots')) {
      return 'Failed to upload screenshots. Please check your internet connection and try again.';
    }
    if (error.contains('Network')) {
      return 'Network error. Please check your internet connection and try again.';
    }
    if (error.contains('timeout')) {
      return 'Request timed out. Please check your internet connection and try again.';
    }

    // Default error message for unhandled cases
    return 'Failed to submit result. Please try again or contact support if the problem persists.';
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: const Color(0xFF0A0A0A),
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => context.pop(),
          ),
          title: Text(
            'Submit Tournament Result',
            style: GoogleFonts.orbitron(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
        ),
        body: const Center(
          child: CircularProgressIndicator(color: Colors.green),
        ),
      );
    }

    return Consumer<TournamentMatchStateProvider>(
      builder: (context, matchStateProvider, child) {
        final matchState = matchStateProvider.getMatchState(widget.tournamentId);

        // Check if we should show different phases
        if (matchState?.phase == TournamentMatchPhase.verification) {
          return _buildVerificationPhaseScreen();
        } else if (matchState?.phase == TournamentMatchPhase.completed) {
          return _buildCompletedPhaseScreen();
        }

        return Scaffold(
          backgroundColor: const Color(0xFF0A0A0A),
          appBar: GoldenAppBar(
            title: 'Submit Tournament Result',
            onBackPressed: () => context.pop(),
          ),
          body: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Show countdown timer if in submission phase
                        if (matchState?.phase == TournamentMatchPhase.submission) ...[
                          _buildSubmissionCountdownHeader(matchState!, matchStateProvider),
                          const SizedBox(height: 24),
                        ],
                        Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              _buildHeaderSection(),
                              const SizedBox(height: 32),
                              if (_resultSubmitted) ...[
                                TournamentStatusWidget(
                                  tournamentId: widget.tournamentId,
                                  onStatusChange: () {
                                    // Handle status changes if needed
                                    setState(() {});
                                  },
                                ),
                                const SizedBox(height: 24),
                              ] else ...[
                                if (_isDeadlinePassed) ...[
                                  _buildDeadlineWarning(),
                                  const SizedBox(height: 24),
                                ],
                                _buildRequiredFieldsSection(),
                                const SizedBox(height: 24),
                                _buildOptionalFieldsSection(),
                                const SizedBox(height: 32),
                                _buildScreenshotSection(),
                                const SizedBox(height: 40),
                                _buildSubmitButton(),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  // Build countdown header for submission phase
  Widget _buildSubmissionCountdownHeader(TournamentMatchState matchState, TournamentMatchStateProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange.withOpacity(0.2), Colors.red.withOpacity(0.2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange, width: 2),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.timer, color: Colors.orange, size: 28),
              const SizedBox(width: 8),
              Text(
                'Result Submission Phase',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Time Remaining: ${provider.getFormattedRemainingTime(matchState.tournamentId)}',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Submit your results before time expires or you will be marked as lost!',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[300],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Build verification phase screen
  Widget _buildVerificationPhaseScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      appBar: GoldenAppBar(
        title: 'Verification Pending',
        onBackPressed: () => context.pop(),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.withOpacity(0.2), Colors.purple.withOpacity(0.2)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.blue, width: 2),
                ),
                child: Column(
                  children: [
                    Icon(Icons.hourglass_empty, color: Colors.blue, size: 64),
                    const SizedBox(height: 16),
                    Text(
                      'Verification Pending',
                      style: GoogleFonts.poppins(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Your results have been submitted successfully and are being verified by administrators.',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: Colors.grey[300],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => context.push('/tournaments/${widget.tournamentId}/verification-status'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'View Verification Status',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build completed phase screen
  Widget _buildCompletedPhaseScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      appBar: GoldenAppBar(
        title: 'Tournament Completed',
        onBackPressed: () => context.pop(),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.green.withOpacity(0.2), Colors.teal.withOpacity(0.2)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.green, width: 2),
                ),
                child: Column(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green, size: 64),
                    const SizedBox(height: 16),
                    Text(
                      'Tournament Completed!',
                      style: GoogleFonts.poppins(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'The tournament has been completed and results have been finalized. Check your final ranking and rewards below.',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: Colors.grey[300],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => context.push('/tournaments/${widget.tournamentId}/verification-status'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'View Final Results',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDeadlineWarning() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red, width: 1),
      ),
      child: Row(
        children: [
          Icon(Icons.warning, color: Colors.red, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Submission Deadline Passed',
                  style: GoogleFonts.orbitron(
                    color: Colors.red,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'The deadline for submitting tournament results has passed. You can no longer submit results for this tournament.',
                  style: GoogleFonts.inter(
                    color: Colors.red.withOpacity(0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF1E3A8A), Color(0xFF3B82F6)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF3B82F6).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(Icons.emoji_events, size: 48, color: Colors.white),
          const SizedBox(height: 12),
          Text(
            'Tournament Result Submission',
            style: GoogleFonts.orbitron(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Submit your final tournament results for verification',
            style: GoogleFonts.inter(
              color: Colors.white.withOpacity(0.9),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRequiredFieldsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF333333)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.star, color: Colors.amber, size: 20),
              const SizedBox(width: 8),
              Text(
                'Required Information',
                style: GoogleFonts.orbitron(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildTextField(
            controller: _rankController,
            label: 'Final Rank/Position *',
            hint: 'Enter your final position (e.g., 1, 2, 3...)',
            icon: Icons.leaderboard,
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Final position is required';
              }
              final rank = int.tryParse(value);
              if (rank == null || rank < 1) {
                return 'Please enter a valid position (1 or higher)';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOptionalFieldsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF333333)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              Text(
                'Optional Information',
                style: GoogleFonts.orbitron(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildTextField(
            controller: _scoreController,
            label: 'Final Score',
            hint: 'Enter your final score (optional)',
            icon: Icons.score,
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _killsController,
            label: 'Kills/Eliminations',
            hint: 'Enter number of kills (optional)',
            icon: Icons.gps_fixed,
            keyboardType: TextInputType.number,
          ),
        ],
      ),
    );
  }

  Widget _buildScreenshotSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF333333)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.camera_alt, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text(
                'Screenshot Upload *',
                style: GoogleFonts.orbitron(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${_selectedImages.length}/$maxScreenshots',
                style: GoogleFonts.inter(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Upload screenshots of your tournament result. Multiple screenshots help with verification.',
            style: GoogleFonts.inter(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 20),

          // Display selected images
          if (_selectedImages.isNotEmpty) ...[
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(right: 12),
                    child: Stack(
                      children: [
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.green, width: 2),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: kIsWeb
                                ? Image.memory(
                                    _selectedImageBytes[index]!,
                                    fit: BoxFit.cover,
                                  )
                                : Image.file(
                                    File(_selectedImages[index].path),
                                    fit: BoxFit.cover,
                                  ),
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeImage(index),
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Add image button
          GestureDetector(
            onTap: _pickImage,
            child: Container(
              height: _selectedImages.isEmpty ? 200 : 60,
              decoration: BoxDecoration(
                color: const Color(0xFF2A2A2A),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _selectedImages.length >= maxScreenshots
                      ? const Color(0xFF444444)
                      : Colors.green.withOpacity(0.5),
                  width: 2,
                  style: BorderStyle.solid,
                ),
              ),
              child: _selectedImages.isEmpty
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.cloud_upload,
                          size: 48,
                          color: Colors.white.withOpacity(0.5),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          'Tap to upload first screenshot',
                          style: GoogleFonts.inter(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'PNG, JPG up to 10MB',
                          style: GoogleFonts.inter(
                            color: Colors.white.withOpacity(0.5),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.add_photo_alternate,
                          color: _selectedImages.length >= maxScreenshots
                              ? Colors.grey
                              : Colors.green,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _selectedImages.length >= maxScreenshots
                              ? 'Maximum screenshots reached'
                              : 'Add another screenshot',
                          style: GoogleFonts.inter(
                            color: _selectedImages.length >= maxScreenshots
                                ? Colors.grey
                                : Colors.white.withOpacity(0.7),
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
            ),
          ),

          // Success message for uploaded screenshots
          if (_selectedImages.isNotEmpty) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${_selectedImages.length} screenshot${_selectedImages.length > 1 ? 's' : ''} uploaded successfully',
                    style: GoogleFonts.inter(color: Colors.green, fontSize: 14),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    final isDisabled = _isSubmitting || _isDeadlinePassed;

    return Container(
      height: 56,
      decoration: BoxDecoration(
        gradient: isDisabled
            ? const LinearGradient(
                colors: [Color(0xFF666666), Color(0xFF555555)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : const LinearGradient(
                colors: [Color(0xFF10B981), Color(0xFF059669)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: isDisabled
            ? []
            : [
                BoxShadow(
                  color: const Color(0xFF10B981).withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: isDisabled ? null : _submitResult,
          child: Center(
            child: _isSubmitting
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    _isDeadlinePassed ? 'Submission Deadline Passed' : 'Submit Result',
                    style: GoogleFonts.orbitron(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      style: GoogleFonts.inter(color: Colors.white, fontSize: 16),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.white.withOpacity(0.7)),
        labelStyle: GoogleFonts.inter(
          color: Colors.white.withOpacity(0.7),
          fontSize: 14,
        ),
        hintStyle: GoogleFonts.inter(
          color: Colors.white.withOpacity(0.5),
          fontSize: 14,
        ),
        filled: true,
        fillColor: const Color(0xFF2A2A2A),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF444444)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF444444)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF3B82F6), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
      ),
    );
  }
}
