/// Referral system models for the WiggyZ app
/// These models handle referral codes, sharing, and referral information
library;

/// Model for user's referral information
class ReferralInfo {
  final String id;
  final String referrerId;
  final String referralCode;
  final int referredCount;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const ReferralInfo({
    required this.id,
    required this.referrerId,
    required this.referralCode,
    required this.referredCount,
    required this.createdAt,
    this.updatedAt,
  });

  factory ReferralInfo.fromJson(Map<String, dynamic> json) {
    return ReferralInfo(
      id: json['id'] as String,
      referrerId: json['referrer_id'] as String,
      referralCode: json['referral_code'] as String,
      referredCount: json['referred_count'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'referrer_id': referrerId,
      'referral_code': referralCode,
      'referred_count': referredCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'ReferralInfo(id: $id, referralCode: $referralCode, referredCount: $referredCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReferralInfo &&
        other.id == id &&
        other.referralCode == referralCode;
  }

  @override
  int get hashCode => id.hashCode ^ referralCode.hashCode;
}

/// Model for referred users
class ReferredUser {
  final String id;
  final String referrerId;
  final String referredId;
  final DateTime signupDate;
  final bool rewardClaimed;
  final String? verificationStatus;
  final ReferredUserInfo? referredUserInfo;

  const ReferredUser({
    required this.id,
    required this.referrerId,
    required this.referredId,
    required this.signupDate,
    required this.rewardClaimed,
    this.verificationStatus,
    this.referredUserInfo,
  });

  factory ReferredUser.fromJson(Map<String, dynamic> json) {
    return ReferredUser(
      id: json['id'] as String,
      referrerId: json['referrer_id'] as String,
      referredId: json['referred_id'] as String,
      signupDate: DateTime.parse(json['signup_date'] as String),
      rewardClaimed: json['reward_claimed'] as bool? ?? false,
      verificationStatus: json['verification_status'] as String?,
      referredUserInfo: json['referred_user'] != null
          ? ReferredUserInfo.fromJson(json['referred_user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'referrer_id': referrerId,
      'referred_id': referredId,
      'signup_date': signupDate.toIso8601String(),
      'reward_claimed': rewardClaimed,
      'verification_status': verificationStatus,
      'referred_user': referredUserInfo?.toJson(),
    };
  }

  @override
  String toString() {
    return 'ReferredUser(id: $id, referredId: $referredId, rewardClaimed: $rewardClaimed)';
  }
}

/// Model for referred user basic information
class ReferredUserInfo {
  final String id;
  final String name;

  const ReferredUserInfo({
    required this.id,
    required this.name,
  });

  factory ReferredUserInfo.fromJson(Map<String, dynamic> json) {
    return ReferredUserInfo(
      id: json['id'] as String,
      name: json['name'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }

  @override
  String toString() {
    return 'ReferredUserInfo(id: $id, name: $name)';
  }
}

/// Model for referral sharing content
class ReferralShareContent {
  final String referralCode;
  final String shareText;
  final String shareUrl;
  final String appDownloadUrl;

  const ReferralShareContent({
    required this.referralCode,
    required this.shareText,
    required this.shareUrl,
    required this.appDownloadUrl,
  });

  factory ReferralShareContent.create({
    required String referralCode,
    String? customMessage,
  }) {
    const String appName = 'WiggyZ';
    const String baseUrl = 'https://wiggyz.com'; // Update with actual app URL
    const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.wiggyz.app';
    const String appStoreUrl = 'https://apps.apple.com/app/wiggyz/id123456789'; // Update with actual App Store ID
    
    final shareUrl = '$baseUrl/refer/$referralCode';
    
    final defaultMessage = customMessage ?? 
        '🎮 Join me on $appName and let\'s earn rewards together! '
        'Use my referral code: $referralCode or click the link below to get started. '
        'We both get 25 points when you sign up! 🎁';
    
    final shareText = '$defaultMessage\n\n'
        '📱 Download $appName:\n'
        '🤖 Android: $playStoreUrl\n'
        '🍎 iOS: $appStoreUrl\n\n'
        '🔗 Direct link: $shareUrl';

    return ReferralShareContent(
      referralCode: referralCode,
      shareText: shareText,
      shareUrl: shareUrl,
      appDownloadUrl: playStoreUrl, // Default to Play Store
    );
  }

  @override
  String toString() {
    return 'ReferralShareContent(referralCode: $referralCode, shareUrl: $shareUrl)';
  }
}

/// Enum for referral sharing result status
enum ReferralShareStatus {
  success,
  cancelled,
  error,
  unavailable,
}

/// Model for referral sharing result
class ReferralShareResult {
  final ReferralShareStatus status;
  final String? errorMessage;
  final String? platform;

  const ReferralShareResult({
    required this.status,
    this.errorMessage,
    this.platform,
  });

  factory ReferralShareResult.success({String? platform}) {
    return ReferralShareResult(
      status: ReferralShareStatus.success,
      platform: platform,
    );
  }

  factory ReferralShareResult.cancelled() {
    return const ReferralShareResult(
      status: ReferralShareStatus.cancelled,
    );
  }

  factory ReferralShareResult.error(String errorMessage) {
    return ReferralShareResult(
      status: ReferralShareStatus.error,
      errorMessage: errorMessage,
    );
  }

  factory ReferralShareResult.unavailable() {
    return const ReferralShareResult(
      status: ReferralShareStatus.unavailable,
      errorMessage: 'Sharing is not available on this device',
    );
  }

  bool get isSuccess => status == ReferralShareStatus.success;
  bool get isCancelled => status == ReferralShareStatus.cancelled;
  bool get isError => status == ReferralShareStatus.error;
  bool get isUnavailable => status == ReferralShareStatus.unavailable;

  @override
  String toString() {
    return 'ReferralShareResult(status: $status, errorMessage: $errorMessage, platform: $platform)';
  }
}
