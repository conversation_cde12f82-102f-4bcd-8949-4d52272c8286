import { SupabaseClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';
import { supabase } from '../../../config/supabase';
import { logger } from '../../../utils/logger';
import { walletService } from '../../wallet/services/walletService';
import { tournamentAuditService } from './tournamentAuditService';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Define MulterFile interface for file uploads
interface MulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination?: string;
  filename?: string;
  path?: string;
  buffer?: Buffer;
}

/**
 * Service for tournament management
 */
export class TournamentService {
  private supabase: SupabaseClient;

  constructor(supabaseClient: SupabaseClient = supabase) {
    this.supabase = supabaseClient;
  }

  /**
   * Get all tournaments with optional filtering (optimized with indexes)
   * @param options Filter options for tournaments
   * @returns List of tournaments
   */
  async getTournaments(options: {
    status?: 'upcoming' | 'active' | 'completed' | 'cancelled';
    game_id?: number;
    limit?: number;
    offset?: number;
  } = {}) {
    try {
      const { status, game_id, limit = 10, offset = 0 } = options;

      // Use optimized query with proper index utilization
      let query = this.supabase
        .from('tournaments')
        .select(`
          id,
          name,
          description,
          game_id,
          start_date,
          end_date,
          max_participants,
          entry_fee,
          prize_pool,
          status,
          created_by,
          rules,
          created_at,
          updated_at
        `);

      // Apply filters in order of selectivity for optimal index usage
      if (status && game_id) {
        // Use composite index idx_tournaments_status_game
        query = query.eq('status', status).eq('game_id', game_id);
      } else if (status) {
        // Use index idx_tournaments_status
        query = query.eq('status', status);
      } else if (game_id) {
        // Use index idx_tournaments_game_id
        query = query.eq('game_id', game_id);
      }

      // Order by start_date (uses index idx_tournaments_status_start_date when status filter is applied)
      query = query.order('start_date', { ascending: true });

      const { data: tournaments, error } = await query
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error(`Error fetching tournaments: ${error instanceof Error ? error.message : String(error)}`);
        throw new Error('Failed to fetch tournaments');
      }

      // Fetch participant counts separately for better performance (uses indexes)
      if (tournaments && tournaments.length > 0) {
        const tournamentIds = tournaments.map(t => t.id);

        const { data: participantCounts, error: countError } = await this.supabase
          .from('tournament_participants')
          .select('tournament_id, participant_type, status')
          .in('tournament_id', tournamentIds);

        if (!countError && participantCounts) {
          // Add participant counts to tournaments (only count active participants)
          tournaments.forEach(tournament => {
            const counts = participantCounts.filter(p => p.tournament_id === tournament.id);
            const activeParticipants = counts.filter(p =>
              p.participant_type === 'participant' &&
              (p.status === 'registered' || p.status === 'active')
            );
            const spectators = counts.filter(p => p.participant_type === 'spectator');

            // Use the database current_participants field if available, otherwise calculate
            const tournamentWithParticipants = tournament as any;
            if (tournamentWithParticipants.current_participants === null || tournamentWithParticipants.current_participants === undefined) {
              tournamentWithParticipants.current_participants = activeParticipants.length;
            }
            (tournament as any).spectator_count = spectators.length;
            (tournament as any).total_participants = activeParticipants.length + spectators.length;
          });
        }
      }

      return tournaments || [];
    } catch (error) {
      logger.error(`Error in getTournaments: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get all matches with optional filtering
   * @param options Filter options for matches
   * @returns List of matches
   */
  async getAllMatches(options: {
    status?: 'scheduled' | 'live' | 'completed' | 'cancelled';
    limit?: number;
    offset?: number;
  } = {}) {
    try {
      const { status, limit = 50, offset = 0 } = options;
      
      let query = this.supabase
        .from('matches')
        .select(`
          *,
          games(*),
          participant1:users!matches_participant1_id_fkey(*),
          participant2:users!matches_participant2_id_fkey(*)
        `)
        .order('match_time', { ascending: true });
      
      if (status) {
        query = query.eq('status', status);
      }
      
      const { data, error } = await query
        .range(offset, offset + limit - 1);
      
      if (error) {
        logger.error(`Error fetching all matches: ${error instanceof Error ? error.message : String(error)}`);
        throw new Error('Failed to fetch matches');
      }
      
      return data || [];
    } catch (error) {
      logger.error(`Error in getAllMatches: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get a tournament by ID
   * @param id Tournament ID
   * @returns Tournament details
   */
  async getTournamentById(id: string) {
    try {
      // Get tournament data
      const { data, error } = await this.supabase
        .from('tournaments')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching tournament:', error);
        throw new Error('Failed to fetch tournament');
      }

      if (!data) {
        throw new Error('Tournament not found');
      }

      // Fetch participants separately to avoid complex query issues
      // Uses index idx_tournament_participants_tournament_id for optimal performance
      const { data: participants, error: participantsError } = await this.supabase
        .from('tournament_participants')
        .select(`
          id,
          user_id,
          status,
          participant_type,
          joined_at,
          users (
            id,
            name,
            email
          )
        `)
        .eq('tournament_id', id);

      if (participantsError) {
        console.error('Error fetching tournament participants:', participantsError);
        // Don't throw error, just log it and continue without participants
      }

      // Add participants to tournament data
      const tournamentWithParticipants = {
        ...data,
        tournament_participants: participants || []
      };

      return tournamentWithParticipants;
    } catch (error) {
      logger.error(`Error in getTournamentById for ID ${id}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get matches by tournament ID
   * @param tournamentId Tournament ID
   * @param options Filter options for matches (limit, offset)
   * @returns List of matches for the specified tournament
   */
  async getMatchesByTournamentId(tournamentId: string, options: {
    limit?: number;
    offset?: number;
  } = {}) {
    try {
      const { limit = 20, offset = 0 } = options; // Default limit for specific tournament matches

      const { data, error } = await this.supabase
        .from('matches')
        .select(`
          *,
          games(*),
          participant1:users!matches_participant1_id_fkey(*),
          participant2:users!matches_participant2_id_fkey(*)
        `)
        .eq('tournament_id', tournamentId)
        .order('match_time', { ascending: true })
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error(`Error fetching matches for tournament ID ${tournamentId}: ${error instanceof Error ? error.message : String(error)}`);
        throw new Error('Failed to fetch tournament matches');
      }

      return data || [];
    } catch (error) {
      logger.error(`Error in getMatchesByTournamentId for tournament ID ${tournamentId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get pending tournament results for admin verification (optimized)
   * Uses partial index idx_tournament_results_pending for optimal performance
   */
  async getPendingTournamentResults(filters: { limit?: number; offset?: number } = {}) {
    const { limit = 20, offset = 0 } = filters;
    try {
      const { data, error, count } = await this.supabase
        .from('tournament_results')
        .select(`
          id,
          tournament_id,
          user_id,
          final_position,
          final_score,
          kills_count,
          screenshot_url,
          screenshot_urls,
          verification_status,
          submitted_at,
          tournaments!inner (
            id,
            name,
            status,
            game_id
          ),
          users!tournament_results_user_id_fkey (
            id,
            name,
            email
          )
        `, { count: 'exact' })
        .eq('verification_status', 'pending')
        .order('submitted_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw new Error(`Failed to fetch pending tournament results: ${error.message}`);
      }

      return {
        results: data || [],
        count: count || 0,
        hasMore: count ? count > (offset + (data?.length || 0)) : false
      };
    } catch (error) {
      logger.error(`Error in getPendingTournamentResults: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Create a new tournament
   * @param tournamentData Tournament data
   * @param creatorUserId User ID of the tournament creator
   * @returns Created tournament
   */  async createTournament(tournamentData: {
    title: string;
    description?: string;
    start_date: string;
    end_date: string;
    registration_deadline?: string;
    min_participants?: number;
    max_participants?: number;
    entry_fee?: number;
    prize_pool?: number;
    room_id?: string;
    room_password?: string;
    format?: string;
    type?: string;
    is_public?: boolean;
    allow_teams?: boolean;
    team_size?: number;
    map?: string;
    rules?: string;
    game_id?: number;
  }, creatorUserId?: string) {
    try {
      // Process entry fee for creator if applicable and creator is provided
      if (creatorUserId && tournamentData.entry_fee && tournamentData.entry_fee > 0) {
        console.log(`Processing entry fee ${tournamentData.entry_fee} for tournament creator ${creatorUserId}`);

        // Check if creator has sufficient balance
        const hasSufficientBalance = await walletService.checkSufficientBalance(creatorUserId, tournamentData.entry_fee);
        if (!hasSufficientBalance) {
          const currentBalance = await walletService.getCurrentBalance(creatorUserId);
          throw new Error(`Insufficient funds to create tournament. Current balance: ${currentBalance}, Required entry fee: ${tournamentData.entry_fee}. Please top up your wallet.`);
        }

        // Process the entry fee for tournament creation
        try {
          await walletService.processTournamentEntryFee(creatorUserId, 'temp-tournament-id', tournamentData.entry_fee, 'NPR', 'creation');
          console.log(`Successfully processed entry fee for tournament creator ${creatorUserId}`);
        } catch (walletError: any) {
          console.error(`Wallet error for tournament creator ${creatorUserId}:`, walletError);
          throw new Error(`Failed to process entry fee: ${walletError.message}`);
        }
      }

      // Map API fields to database fields
      const databaseData = {
        name: tournamentData.title, // Map 'title' to 'name' for database
        description: tournamentData.description,
        game_id: tournamentData.game_id,
        start_date: tournamentData.start_date,
        end_date: tournamentData.end_date,
        rules: tournamentData.rules || '', // Ensure rules is never null
        max_participants: tournamentData.max_participants,
        entry_fee: tournamentData.entry_fee || 0,
        prize_pool: tournamentData.prize_pool || 0,
        status: 'upcoming',
        created_by: creatorUserId,
        // Note: other fields may not exist in current database schema
        // Only include fields that exist in the tournaments table
      };

      const { data: tournament, error } = await this.supabase
        .from('tournaments')
        .insert(databaseData)
        .select()
        .single();

      if (error) {
        logger.error(`Error creating tournament: ${error instanceof Error ? error.message : String(error)}`);
        throw new Error('Failed to create tournament');
      }

      // Update transaction with actual tournament ID if entry fee was processed
      if (creatorUserId && tournamentData.entry_fee && tournamentData.entry_fee > 0) {
        await this.supabase
          .from('wallet_transactions')
          .update({
            description: `Entry fee for tournament creation ${tournament.id}`,
            metadata: { tournamentId: tournament.id, transactionType: 'creation' }
          })
          .eq('user_id', creatorUserId)
          .eq('description', 'Entry fee for tournament creation temp-tournament-id');

        // Log entry fee processing for audit trail
        await tournamentAuditService.logEntryFeeProcessing(
          tournament.id,
          creatorUserId,
          tournamentData.entry_fee,
          'NPR',
          'creation'
        );
      }

      // Auto-register creator as spectator if creator is provided
      if (creatorUserId) {
        try {
          await this.supabase
            .from('tournament_participants')
            .insert({
              tournament_id: tournament.id,
              user_id: creatorUserId,
              status: 'registered',
              participant_type: 'spectator',
              joined_at: new Date().toISOString()
            });
          console.log(`Successfully auto-registered creator ${creatorUserId} as spectator for tournament ${tournament.id}`);
        } catch (participantError) {
          console.error(`Error auto-registering creator as spectator:`, participantError);
          // Don't throw error here as tournament creation was successful
        }
      }

      // Log tournament creation for audit trail
      await tournamentAuditService.logTournamentCreation(
        tournament.id,
        creatorUserId || 'system',
        tournamentData
      );

      logger.info(`Tournament created successfully: ${tournament.id} by user ${creatorUserId || 'system'}`);
      return tournament;
    } catch (error) {
      logger.error(`Error in createTournament: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Register a user for a tournament
   * @param tournamentId Tournament ID
   * @param userId User ID
   * @returns Registration result
   */
  async registerForTournament(tournamentId: string, userId: string) {
    try {
      console.log(`=== COMPREHENSIVE TOURNAMENT REGISTRATION DEBUG ===`);
      console.log(`Tournament ID: ${tournamentId}`);
      console.log(`User ID: ${userId}`);
      console.log(`Timestamp: ${new Date().toISOString()}`);

      // Step 1: Tournament Existence and Details Check
      console.log('Step 1: Fetching tournament details...');
      const { data: tournament, error: tournamentError } = await this.supabase
        .from('tournaments')
        .select('*')
        .eq('id', tournamentId)
        .single();

      console.log(`Tournament query result:`, {
        data: tournament ? {
          id: tournament.id,
          title: tournament.title,
          status: tournament.status,
          max_participants: tournament.max_participants,
          entry_fee: tournament.entry_fee,
          registration_deadline: tournament.registration_deadline,
          start_date: tournament.start_date,
          end_date: tournament.end_date
        } : null,
        error: tournamentError
      });

      if (tournamentError || !tournament) {
        console.log(`REGISTRATION FAILED: Tournament not found`);
        logger.error(`Tournament not found with ID ${tournamentId}: ${tournamentError instanceof Error ? tournamentError.message : String(tournamentError)}`);
        throw new Error('Tournament not found');
      }
      console.log('✓ Tournament exists and fetched successfully');

      // Step 2: Tournament Status Validation
      console.log('Step 2: Validating tournament status...');
      console.log(`Tournament status: "${tournament.status}"`);
      console.log(`Valid statuses: ["upcoming", "registration"]`);
      console.log(`Status check: ${tournament.status === 'upcoming' || tournament.status === 'registration'}`);
      
      if (tournament.status !== 'upcoming' && tournament.status !== 'registration') {
        console.log(`REGISTRATION FAILED: Invalid tournament status - "${tournament.status}"`);
        throw new Error(`Tournament is not open for registration (status: ${tournament.status})`);
      }
      console.log('✓ Tournament status validation passed');

      // Step 3: Registration Deadline Check
      console.log('Step 3: Checking registration deadline...');
      const now = new Date();
      console.log(`Current time: ${now.toISOString()}`);
      console.log(`Registration deadline: ${tournament.registration_deadline}`);
      
      if (tournament.registration_deadline) {
        const deadline = new Date(tournament.registration_deadline);
        console.log(`Deadline parsed: ${deadline.toISOString()}`);
        console.log(`Deadline passed: ${deadline < now}`);
        
        if (deadline < now) {
          console.log(`REGISTRATION FAILED: Registration deadline has passed`);
          throw new Error('Registration deadline has passed');
        }
      } else {
        console.log('No registration deadline set');
      }
      console.log('✓ Registration deadline check passed');

      // Step 4: Duplicate Registration Check
      console.log('Step 4: Checking for existing registration...');
      const { data: existingRegistration, error: registrationCheckError } = await this.supabase
        .from('tournament_participants')
        .select('id, participant_type, status, joined_at')
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .maybeSingle();

      console.log(`Existing registration query result:`, {
        data: existingRegistration,
        error: registrationCheckError
      });

      if (registrationCheckError) {
        console.log(`REGISTRATION FAILED: Error checking existing registration:`, registrationCheckError);
        throw new Error(`Error checking registration status: ${registrationCheckError.message}`);
      }

      if (existingRegistration) {
        console.log(`REGISTRATION FAILED: User already registered - ID: ${existingRegistration.id}, Type: ${existingRegistration.participant_type}`);
        throw new Error(`User already registered for this tournament (type: ${existingRegistration.participant_type})`);
      }
      console.log('✓ No existing registration found');
      
      // Step 5: Tournament Capacity Check
      console.log('Step 5: Checking tournament capacity...');
      if (tournament.max_participants) {
        const { count, error: countError } = await this.supabase
          .from('tournament_participants')
          .select('id', { count: 'exact' })
          .eq('tournament_id', tournamentId);

        console.log(`Participant count query result:`, {
          count: count,
          error: countError,
          maxParticipants: tournament.max_participants
        });

        if (countError) {
          console.log(`REGISTRATION FAILED: Error counting participants:`, countError);
          throw new Error(`Error checking tournament capacity: ${countError.message}`);
        }

        console.log(`Capacity check: ${count}/${tournament.max_participants} participants`);
        console.log(`At capacity: ${count !== null && count >= tournament.max_participants}`);

        if (!countError && count !== null && count >= tournament.max_participants) {
          console.log(`REGISTRATION FAILED: Tournament at maximum capacity (${count}/${tournament.max_participants})`);
          throw new Error(`Tournament has reached maximum participants (${count}/${tournament.max_participants})`);
        }
      } else {
        console.log('No participant limit set for this tournament');
      }
      console.log('✓ Tournament capacity check passed');

      // Process entry fee if applicable
      if (tournament.entry_fee && tournament.entry_fee > 0) {
        console.log(`Processing entry fee ${tournament.entry_fee} for user ${userId} registering for tournament ${tournamentId}`);

        // Check if user has sufficient balance
        const hasSufficientBalance = await walletService.checkSufficientBalance(userId, tournament.entry_fee);
        if (!hasSufficientBalance) {
          const currentBalance = await walletService.getCurrentBalance(userId);
          throw new Error(`Insufficient funds to register for tournament. Current balance: ${currentBalance}, Required entry fee: ${tournament.entry_fee}. Please top up your wallet.`);
        }

        // Process the entry fee for tournament registration
        try {
          await walletService.processTournamentEntryFee(userId, tournamentId, tournament.entry_fee, 'NPR', 'registration');
          console.log(`Successfully processed entry fee for user ${userId} registering for tournament ${tournamentId}`);

          // Log entry fee processing for audit trail
          await tournamentAuditService.logEntryFeeProcessing(
            tournamentId,
            userId,
            tournament.entry_fee,
            'NPR',
            'registration'
          );
        } catch (walletError: any) {
          console.error(`Wallet error for user ${userId} registering for tournament ${tournamentId}:`, walletError);
          throw new Error(`Failed to process entry fee: ${walletError.message}`);
        }
      } else {
        console.log(`No entry fee required for user ${userId} registering for tournament ${tournamentId}`);
      }

      // Register user for tournament
      // TEMPORARY FIX: Handle missing columns gracefully
      console.log('Attempting tournament registration with fallback for missing columns...');

      let registrationResult: any;

      try {
        // First attempt with all expected columns
        const fullInsertData = {
          tournament_id: tournamentId,
          user_id: userId,
          status: 'registered',
          participant_type: 'participant',
          joined_at: new Date().toISOString()
        };

        const { data: fullData, error: fullError } = await this.supabase
          .from('tournament_participants')
          .insert(fullInsertData)
          .select()
          .single();

        if (fullError) {
          // If error is due to missing columns, try with basic fields only
          // Handle both PostgreSQL (42703) and Supabase (PGRST204) error codes for missing columns
          if ((fullError.code === '42703' || fullError.code === 'PGRST204') && (fullError.message.includes('participant_type') || fullError.message.includes('joined_at'))) {
            console.log('Missing columns detected, retrying with basic fields...');

            const basicInsertData = {
              tournament_id: tournamentId,
              user_id: userId,
              status: 'registered',
              registration_date: new Date().toISOString() // Use existing column instead of joined_at
            };

            const { data: basicData, error: basicError } = await this.supabase
              .from('tournament_participants')
              .insert(basicInsertData)
              .select()
              .single();

            if (basicError) {
              logger.error(`Error registering for tournament (retry): ${basicError instanceof Error ? basicError.message : String(basicError)}`);
              throw new Error('Failed to register for tournament');
            }

            console.log('✓ Registration successful with basic fields');
            registrationResult = basicData;
          } else {
            logger.error(`Error registering for tournament: ${fullError instanceof Error ? fullError.message : String(fullError)}`);
            throw new Error('Failed to register for tournament');
          }
        } else {
          console.log('✓ Registration successful with all fields');
          registrationResult = fullData;
        }
      } catch (insertError) {
        logger.error(`Exception during tournament registration: ${insertError instanceof Error ? insertError.message : String(insertError)}`);
        throw new Error('Failed to register for tournament');
      }

      if (!registrationResult) {
        throw new Error('Failed to register for tournament');
      }

      // Log participant joining for audit trail
      await tournamentAuditService.logParticipantJoined(
        tournamentId,
        userId,
        'participant',
        tournament.entry_fee
      );

      return registrationResult || [];
    } catch (error) {
      logger.error(`Error in registerForTournament for tournament ${tournamentId} and user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get tournament leaderboard
   * @param tournamentId Tournament ID
   * @returns Tournament leaderboard
   */
  async getTournamentLeaderboard(tournamentId: string) {
    try {
      const { data, error } = await this.supabase
        .from('tournament_participants')
        .select(`
          user_id,
          users:user_id (name, email),
          result_score,
          result_position,
          result_kills,
          is_winner,
          result_submitted_at
        `)
        .eq('tournament_id', tournamentId)
        .not('result_score', 'is', null)
        .order('result_position', { ascending: true });

      if (error) {
        logger.error(`Error fetching tournament leaderboard: ${error instanceof Error ? error.message : String(error)}`);
        throw new Error('Failed to fetch tournament leaderboard');
      }

      // Map the data to match expected format
      const leaderboard = (data || []).map((participant, index) => ({
        user_id: participant.user_id,
        users: participant.users,
        score: participant.result_score,
        rank: participant.result_position || (index + 1),
        kills: participant.result_kills,
        is_winner: participant.is_winner,
        submitted_at: participant.result_submitted_at
      }));

      return leaderboard;
    } catch (error) {
      logger.error(`Error in getTournamentLeaderboard: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Update participant score
   * @param tournamentId Tournament ID
   * @param userId User ID
   * @param score New score
   * @returns Updated participant data
   */
  async updateParticipantScore(tournamentId: string, userId: string, score: number) {
    try {
      // Check if tournament is active
      const { data: tournament, error: tournamentError } = await this.supabase
        .from('tournaments')
        .select('status')
        .eq('id', tournamentId)
        .single();
      
      if (tournamentError || !tournament) {
        throw new Error('Tournament not found');
      }
      
      if (tournament.status !== 'active') {
        throw new Error('Cannot update score for inactive tournament');
      }
      
      // Update participant score
      const { data, error } = await this.supabase
        .from('tournament_participants')
        .update({ current_score: score })
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .select()
        .single();
      
      if (error) {
        logger.error(`Error updating score for user ${userId} in tournament ${tournamentId}: ${error instanceof Error ? error.message : String(error)}`);
        throw new Error('Failed to update participant score');
      }
      
      return data || [];
    } catch (error) {
      logger.error(`Error in updateParticipantScore for tournament ${tournamentId} and user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Finalize tournament and assign ranks
   * @param tournamentId Tournament ID
   * @returns Success status
   */
  async finalizeTournament(tournamentId: string) {
    try {
      // Get tournament participants ordered by score
      const { data: participants, error: participantsError } = await this.supabase
        .from('tournament_participants')
        .select('id, user_id, current_score')
        .eq('tournament_id', tournamentId)
        .order('current_score', { ascending: false });
      
      if (participantsError) {
        logger.error(`Error fetching participants for tournament ${tournamentId}: ${participantsError instanceof Error ? participantsError.message : String(participantsError)}`);
        throw new Error('Failed to fetch tournament participants');
      }
      
      // Assign ranks to participants
      const updates = participants.map((participant, index) => ({
        id: participant.id,
        final_rank: index + 1,
        status: 'completed'
      }));
      
      // Update ranks in batch
      if (updates.length > 0) {
        const { error: updateError } = await this.supabase
          .from('tournament_participants')
          .upsert(updates);
        
        if (updateError) {
          logger.error(`Error updating participant ranks for tournament ${tournamentId}: ${updateError instanceof Error ? updateError.message : String(updateError)}`);
          throw new Error('Failed to update participant ranks');
        }
      }
      
      // Update tournament status to completed
      const { error: tournamentUpdateError } = await this.supabase
        .from('tournaments')
        .update({ status: 'completed' })
        .eq('id', tournamentId);
      
      if (tournamentUpdateError) {
        logger.error(`Error updating tournament status for ${tournamentId}: ${tournamentUpdateError instanceof Error ? tournamentUpdateError.message : String(tournamentUpdateError)}`);
        throw new Error('Failed to update tournament status');
      }
      
      return { success: true };
    } catch (error) {
      logger.error(`Error in finalizeTournament for tournament ${tournamentId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get participant information for a tournament
   * @param tournamentId Tournament ID
   * @param userId User ID
   * @returns Participant data
   */
  async getParticipant(tournamentId: string, userId: string) {
    try {
      return await this.supabase
        .from('tournament_participants')
        .select('*')
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .maybeSingle();
    } catch (error) {
      logger.error(`Error in getParticipant for tournament ${tournamentId} and user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Update claim transaction ID
   * @param claimId Claim ID
   * @param transactionId Transaction ID
   * @returns Update result
   */
  async updateClaimTransaction(claimId: string, transactionId: string) {
    try {
      return await this.supabase
        .from('tournament_reward_claims')
        .update({ transaction_id: transactionId })
        .eq('id', claimId);
    } catch (error) {
      logger.error(`Error in updateClaimTransaction for claim ${claimId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Claim tournament reward
   * @param tournamentId Tournament ID
   * @param userId User ID
   * @param idempotencyKey Unique key to prevent duplicate claims
   * @returns Claim result
   */
  async claimTournamentReward(tournamentId: string, userId: string, idempotencyKey: string) {
    try {
      // Check if tournament is completed
      const { data: tournament, error: tournamentError } = await this.supabase
        .from('tournaments')
        .select('status')
        .eq('id', tournamentId)
        .single();
      
      if (tournamentError || !tournament) {
        throw new Error('Tournament not found');
      }
      
      if (tournament.status !== 'completed') {
        throw new Error('Cannot claim rewards for uncompleted tournament');
      }
      
      // Check if user participated and get their rank
      const { data: participant, error: participantError } = await this.supabase
        .from('tournament_participants')
        .select('final_rank')
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .maybeSingle();
      
      if (participantError || !participant) {
        throw new Error('User did not participate in this tournament');
      }
      
      // Check if reward already claimed
      const { data: existingClaim, error: claimCheckError } = await this.supabase
        .from('tournament_reward_claims')
        .select('id')
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .maybeSingle();
      
      if (existingClaim) {
        throw new Error('Reward already claimed for this tournament');
      }
      
      // Find applicable reward based on rank
      const { data: reward, error: rewardError } = await this.supabase
        .from('tournament_rewards')
        .select('*')
        .eq('tournament_id', tournamentId)
        .lte('rank_from', participant.final_rank)
        .gte('rank_to', participant.final_rank)
        .single();
      
      if (rewardError || !reward) {
        throw new Error('No reward available for your rank');
      }
      
      // Record the claim
      const { data: claim, error: claimError } = await this.supabase
        .from('tournament_reward_claims')
        .insert({
          tournament_id: tournamentId,
          user_id: userId,
          tournament_reward_id: reward.id,
          idempotency_key: idempotencyKey
        })
        .select()
        .single();
      
      if (claimError) {
        if (claimError.code === '23505') { // Unique constraint violation
          throw new Error('Reward already claimed (idempotency key conflict)');
        }
        logger.error(`Error claiming reward for user ${userId} in tournament ${tournamentId}: ${claimError instanceof Error ? claimError.message : String(claimError)}`);
        throw new Error('Failed to claim tournament reward');
      }
        return {
        success: true,
        reward,
        claim
      };
    } catch (error) {
      logger.error(`Error in claimTournamentReward for tournament ${tournamentId} and user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get user's joined tournaments based on tournament_participants table
   */
  async getUserJoinedTournaments(userId: string, filters: any = {}): Promise<any[]> {
    try {
      let query = this.supabase
        .from('tournament_participants')
        .select(`
          id,
          tournament_id,
          user_id,
          registration_date,
          status as user_tournament_status,
          tournaments (
            id,
            name,
            description,
            game_id,
            start_date,
            end_date,
            status,
            prize_pool,
            rules,
            max_participants,
            current_participants,
            image_url,
            created_at,
            updated_at,
            games (id, name, image)
          )
        `)
        .eq('user_id', userId)
        .order('registration_date', { ascending: false });

      // Apply filters
      if (filters.user_tournament_status) {
        query = query.eq('status', filters.user_tournament_status);
      }

      if (filters.status && filters.status !== 'all') {
        query = query.eq('tournaments.status', filters.status);
      }

      // Apply limit and offset
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      const { data, error } = await query;

      if (error) {
        logger.error(`Error fetching user joined tournaments: ${error.message}`);
        throw new Error(`Failed to fetch user joined tournaments: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      logger.error(`Error in getUserJoinedTournaments for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get user's active tournaments (registered, active)
   */
  async getUserActiveTournaments(userId: string): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('tournament_participants')
        .select(`
          id,
          tournament_id,
          user_id,
          registration_date,
          status as user_tournament_status,
          tournaments (
            id,
            name,
            description,
            game_id,
            start_date,
            end_date,
            status,
            prize_pool,
            rules,
            max_participants,
            current_participants,
            image_url,
            created_at,
            updated_at,
            games (id, name, image)
          )
        `)
        .eq('user_id', userId)
        .in('status', ['registered', 'active'])
        .in('tournaments.status', ['upcoming', 'active'])
        .order('registration_date', { ascending: false });

      if (error) {
        logger.error(`Error fetching user active tournaments: ${error.message}`);
        throw new Error(`Failed to fetch user active tournaments: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      logger.error(`Error in getUserActiveTournaments for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Submit tournament result
   */
  async submitTournamentResult(tournamentId: string, userId: string, resultData: {
    final_position?: number;
    final_score?: number;
    kills_count?: number;
    screenshot_url?: string;
    screenshot_urls?: string[];
  }) {
    try {
      // Enhanced validation for result data
      if (!resultData.final_position || resultData.final_position < 1) {
        throw new Error('Final position is required and must be a positive number');
      }

      // Validate screenshot requirements - at least one screenshot is required
      const hasScreenshots = (resultData.screenshot_urls && resultData.screenshot_urls.length > 0) ||
                            resultData.screenshot_url;
      if (!hasScreenshots) {
        throw new Error('At least one screenshot is required for result verification');
      }

      // Validate screenshot URLs format
      const allScreenshots = [
        ...(resultData.screenshot_urls || []),
        ...(resultData.screenshot_url ? [resultData.screenshot_url] : [])
      ];

      for (const url of allScreenshots) {
        if (!url || typeof url !== 'string' || !url.trim()) {
          throw new Error('Invalid screenshot URL provided');
        }
        // Basic URL validation
        try {
          new URL(url);
        } catch {
          throw new Error(`Invalid screenshot URL format: ${url}`);
        }
      }

      // Get tournament details to check deadline and status
      const { data: tournament, error: tournamentError } = await this.supabase
        .from('tournaments')
        .select('status, end_date, result_submission_deadline')
        .eq('id', tournamentId)
        .single();

      if (tournamentError || !tournament) {
        throw new Error('Tournament not found');
      }

      // Check if tournament allows result submission
      if (tournament.status !== 'active' && tournament.status !== 'completed') {
        throw new Error('Result submission is not allowed for this tournament status');
      }

      // Check result submission deadline if set
      if (tournament.result_submission_deadline) {
        const deadline = new Date(tournament.result_submission_deadline);
        const now = new Date();
        if (now > deadline) {
          throw new Error('Result submission deadline has passed');
        }
      }

      // First, get the participant record
      const { data: participant, error: participantError } = await this.supabase
        .from('tournament_participants')
        .select('id, participant_type, result_submitted, result_submitted_at')
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .single();

      if (participantError || !participant) {
        throw new Error('User is not registered for this tournament');
      }

      // Only players can submit results, not spectators
      if (participant.participant_type === 'spectator') {
        throw new Error('Spectators cannot submit tournament results');
      }

      // Check if user has already submitted results
      if (participant.result_submitted) {
        throw new Error('You have already submitted results for this tournament. Multiple submissions are not allowed.');
      }

      // Check if result already exists
      const { data: existingResult, error: existingError } = await this.supabase
        .from('tournament_results')
        .select('id, verification_status')
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .maybeSingle();

      if (existingResult && existingResult.verification_status !== 'pending') {
        throw new Error('Result has already been submitted and verified');
      }

      // Prepare result data with enhanced screenshot handling
      const consolidatedScreenshots = [
        ...(resultData.screenshot_urls || []),
        ...(resultData.screenshot_url ? [resultData.screenshot_url] : [])
      ].filter(url => url && url.trim()); // Remove empty URLs

      const resultPayload = {
        tournament_id: tournamentId,
        user_id: userId,
        participant_id: participant.id,
        final_position: resultData.final_position,
        final_score: resultData.final_score || null,
        kills_count: resultData.kills_count || 0,
        screenshot_url: consolidatedScreenshots[0] || null, // Primary screenshot
        screenshot_urls: consolidatedScreenshots.length > 0 ? consolidatedScreenshots : null,
        verification_status: 'pending',
        submitted_at: new Date().toISOString()
      };

      let result;
      if (existingResult) {
        // Update existing result
        const { data, error } = await this.supabase
          .from('tournament_results')
          .update(resultPayload)
          .eq('id', existingResult.id)
          .select()
          .single();

        if (error) throw error;
        result = data;
      } else {
        // Insert new result
        const { data, error } = await this.supabase
          .from('tournament_results')
          .insert(resultPayload)
          .select()
          .single();

        if (error) throw error;
        result = data;
      }

      // Update participant record with result data
      await this.supabase
        .from('tournament_participants')
        .update({
          result_score: resultData.final_score,
          result_position: resultData.final_position,
          result_kills: resultData.kills_count || 0,
          result_screenshot_url: resultData.screenshot_url,
          result_submitted_at: new Date().toISOString(),
          result_submitted: true,
          status: 'result_submitted'
        })
        .eq('id', participant.id);

      // Log result submission for audit trail
      await tournamentAuditService.logResultSubmission(
        tournamentId,
        userId,
        resultData
      );

      return result;
    } catch (error) {
      // Enhanced error handling with specific error types
      if (error instanceof Error) {
        // Log the full error for debugging
        logger.error(`Error submitting tournament result for tournament ${tournamentId}, user ${userId}: ${error.message}. Stack: ${error.stack}. Data: ${JSON.stringify({ tournamentId, userId, resultData })}`);

        // Provide specific error messages based on error type
        if (error.message.includes('Final position is required')) {
          throw new Error('INVALID_POSITION: Please provide a valid final position (1 or higher)');
        }
        if (error.message.includes('screenshot is required')) {
          throw new Error('MISSING_SCREENSHOT: At least one screenshot is required for result verification');
        }
        if (error.message.includes('Invalid screenshot URL')) {
          throw new Error('INVALID_SCREENSHOT: One or more screenshot URLs are invalid');
        }
        if (error.message.includes('Tournament not found')) {
          throw new Error('TOURNAMENT_NOT_FOUND: The specified tournament could not be found');
        }
        if (error.message.includes('not registered')) {
          throw new Error('NOT_REGISTERED: You are not registered for this tournament');
        }
        if (error.message.includes('Spectators cannot submit')) {
          throw new Error('SPECTATOR_RESTRICTION: Spectators cannot submit tournament results');
        }
        if (error.message.includes('deadline has passed')) {
          throw new Error('DEADLINE_PASSED: The result submission deadline has passed');
        }
        if (error.message.includes('not allowed for this tournament status')) {
          throw new Error('TOURNAMENT_STATUS: Result submission is not allowed for this tournament status');
        }
        if (error.message.includes('duplicate key value')) {
          throw new Error('ALREADY_SUBMITTED: You have already submitted a result for this tournament');
        }

        // Generic error for unhandled cases
        throw new Error(`SUBMISSION_ERROR: ${error.message}`);
      }

      // Handle non-Error objects
      logger.error(`Unknown error submitting tournament result: ${String(error)}`);
      throw new Error('UNKNOWN_ERROR: An unexpected error occurred while submitting your result');
    }
  }

  /**
   * Get tournament verification status for a user
   */
  async getTournamentVerificationStatus(tournamentId: string, userId: string) {
    try {
      const { data, error } = await this.supabase
        .from('tournament_results')
        .select(`
          id,
          verification_status,
          final_position,
          final_score,
          kills_count,
          submitted_at,
          verified_at,
          admin_notes,
          tournaments (
            id,
            name,
            status,
            end_date
          )
        `)
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .maybeSingle();

      if (error) {
        throw new Error(`Failed to get verification status: ${error.message}`);
      }

      // Also get participant info
      const { data: participant, error: participantError } = await this.supabase
        .from('tournament_participants')
        .select('is_winner, status')
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .single();

      if (participantError) {
        throw new Error('User is not registered for this tournament');
      }

      return {
        result: data,
        participant,
        hasSubmittedResult: !!data,
        isWinner: participant.is_winner
      };
    } catch (error) {
      logger.error(`Error getting tournament verification status: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }



  /**
   * Verify tournament result (admin action)
   */
  async verifyTournamentResult(resultId: string, action: 'verified' | 'rejected', adminUserId: string, adminNotes?: string) {
    try {
      const { data, error } = await this.supabase
        .from('tournament_results')
        .update({
          verification_status: action,
          verified_by: adminUserId,
          verified_at: new Date().toISOString(),
          admin_notes: adminNotes
        })
        .eq('id', resultId)
        .select(`
          id,
          tournament_id,
          user_id,
          verification_status,
          tournaments (id, name),
          users (id, name)
        `)
        .single();

      if (error) {
        throw new Error(`Failed to verify tournament result: ${error.message}`);
      }

      // Log result verification for audit trail
      await tournamentAuditService.logResultVerification(
        data.tournament_id,
        resultId,
        adminUserId,
        action,
        data.user_id,
        adminNotes
      );

      return data;
    } catch (error) {
      logger.error(`Error verifying tournament result: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Assign tournament winners
   */
  async assignTournamentWinners(tournamentId: string, winners: Array<{ user_id: string; position: number }>, adminUserId: string) {
    try {
      // Start a transaction-like operation
      const updates = [];

      // Update all participants to not be winners first
      const { error: resetError } = await this.supabase
        .from('tournament_participants')
        .update({ is_winner: false })
        .eq('tournament_id', tournamentId);

      if (resetError) {
        throw new Error(`Failed to reset winners: ${resetError.message}`);
      }

      // Update specified winners
      for (const winner of winners) {
        const { error: winnerError } = await this.supabase
          .from('tournament_participants')
          .update({
            is_winner: true,
            result_position: winner.position
          })
          .eq('tournament_id', tournamentId)
          .eq('user_id', winner.user_id);

        if (winnerError) {
          throw new Error(`Failed to assign winner ${winner.user_id}: ${winnerError.message}`);
        }
      }

      // Update tournament status to completed
      const { error: tournamentError } = await this.supabase
        .from('tournaments')
        .update({
          status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('id', tournamentId);

      if (tournamentError) {
        throw new Error(`Failed to update tournament status: ${tournamentError.message}`);
      }

      // Get updated tournament data
      const { data: tournament, error: fetchError } = await this.supabase
        .from('tournaments')
        .select(`
          id,
          name,
          status,
          tournament_participants (
            user_id,
            is_winner,
            result_position,
            users (id, name)
          )
        `)
        .eq('id', tournamentId)
        .single();

      if (fetchError) {
        throw new Error(`Failed to fetch updated tournament: ${fetchError.message}`);
      }

      // Log winner assignment for audit trail
      const winnersWithPrizes = winners.map(w => ({ ...w, prize_amount: 0 })); // Add prize amount if available
      await tournamentAuditService.logWinnerAssignment(
        tournamentId,
        adminUserId,
        winnersWithPrizes
      );

      // Log tournament completion
      await tournamentAuditService.logTournamentCompletion(
        tournamentId,
        'admin',
        adminUserId,
        'Winners assigned by admin'
      );

      return tournament;
    } catch (error) {
      logger.error(`Error assigning tournament winners: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Start tournament match (spectator/admin action)
   */
  async startTournamentMatch(tournamentId: string, userId: string, matchData: {
    room_id: string;
    room_password: string;
  }) {
    try {
      console.log(`Starting tournament match for tournament ID: ${tournamentId}, user ID: ${userId}`);

      // First, verify the tournament exists with a simple query
      const { data: tournamentExists, error: existsError } = await this.supabase
        .from('tournaments')
        .select('id')
        .eq('id', tournamentId)
        .single();

      console.log(`Tournament exists check:`, { tournamentExists, existsError });

      if (existsError || !tournamentExists) {
        console.error(`Tournament does not exist. Error:`, existsError);
        throw new Error('Tournament not found');
      }

      // Now get the full tournament data
      const { data: tournament, error: tournamentError } = await this.supabase
        .from('tournaments')
        .select('id, name, status, created_by')
        .eq('id', tournamentId)
        .single();

      console.log(`Tournament query result:`, { tournament, tournamentError });

      if (tournamentError || !tournament) {
        console.error(`Tournament not found. Error:`, tournamentError);
        throw new Error('Tournament not found');
      }

      // Separately get tournament participants
      const { data: participants, error: participantsError } = await this.supabase
        .from('tournament_participants')
        .select('user_id, participant_type')
        .eq('tournament_id', tournamentId);

      console.log(`Participants query result:`, { participants, participantsError });

      // Check if user is the creator (spectator) or has admin permissions
      const isCreator = tournament.created_by === userId;
      const isSpectator = participants?.some(
        p => p.user_id === userId && p.participant_type === 'spectator'
      );

      console.log(`Permission check - isCreator: ${isCreator}, isSpectator: ${isSpectator}`);

      if (!isCreator && !isSpectator) {
        throw new Error('Only tournament creators or spectators can start the match');
      }

      // Update tournament with room information and set status to active
      const { data: updatedTournament, error: updateError } = await this.supabase
        .from('tournaments')
        .update({
          room_id: matchData.room_id,
          room_password: matchData.room_password,
          status: 'active',
          updated_at: new Date().toISOString()
        })
        .eq('id', tournamentId)
        .select(`
          id,
          name,
          status,
          room_id,
          room_password,
          tournament_participants (
            user_id,
            participant_type,
            users (id, name)
          )
        `)
        .single();

      if (updateError) {
        throw new Error(`Failed to start tournament match: ${updateError.message}`);
      }

      logger.info(`Tournament match started by user ${userId} for tournament ${tournamentId}`);
      return updatedTournament;
    } catch (error) {
      logger.error(`Error starting tournament match: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * End tournament match (spectator/admin action)
   */
  async endTournamentMatch(tournamentId: string, userId: string) {
    try {
      // First, verify the user has permission to end the tournament
      const { data: tournament, error: tournamentError } = await this.supabase
        .from('tournaments')
        .select(`
          id,
          name,
          status,
          created_by,
          tournament_participants (
            user_id,
            participant_type
          )
        `)
        .eq('id', tournamentId)
        .single();

      if (tournamentError || !tournament) {
        throw new Error('Tournament not found');
      }

      // Check if user is creator or spectator
      const isCreator = tournament.created_by === userId;
      const isSpectator = tournament.tournament_participants?.some(
        (p: any) => p.user_id === userId && p.participant_type === 'spectator'
      );

      if (!isCreator && !isSpectator) {
        throw new Error('Only tournament creators and spectators can end matches');
      }

      // Update tournament status to completed
      const { data: updatedTournament, error: updateError } = await this.supabase
        .from('tournaments')
        .update({
          status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('id', tournamentId)
        .select(`
          id,
          name,
          status,
          updated_at
        `)
        .single();

      if (updateError) {
        throw new Error(`Failed to end tournament match: ${updateError.message}`);
      }

      return updatedTournament;
    } catch (error) {
      logger.error(`Error in endTournamentMatch: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Check if user has already submitted results for a tournament
   */
  async hasUserSubmittedResults(tournamentId: string, userId: string): Promise<boolean> {
    try {
      const { data: participant, error } = await this.supabase
        .from('tournament_participants')
        .select('result_submitted')
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .single();

      if (error || !participant) {
        return false;
      }

      return participant.result_submitted === true;
    } catch (error) {
      logger.error(`Error checking submission status: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Check if user is registered for a tournament
   */
  async isUserRegistered(tournamentId: string, userId: string): Promise<boolean> {
    try {
      const { data: participant, error } = await this.supabase
        .from('tournament_participants')
        .select('id')
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .single();

      return !error && !!participant;
    } catch (error) {
      logger.error(`Error checking registration status: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Upload screenshot to Supabase Storage for tournament results
   */
  async uploadScreenshot(file: MulterFile, tournamentId: string, userId: string): Promise<string> {
    try {
      const supabaseAdmin = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_KEY!);
      console.log(`tournamentService.uploadScreenshot: Received file - originalname: ${file.originalname}, mimetype: ${file.mimetype}, path: ${file.path}, size: ${file.size}, bufferExists: ${!!file.buffer}`);

      const fileExt = path.extname(file.originalname);
      const randomFileNamePart = `${userId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
      const randomFileName = `${randomFileNamePart}${fileExt}`;
      const supabasePath = `tournament-results/${tournamentId}/${randomFileName}`;

      let fileBody: Buffer;
      const contentType: string = file.mimetype;

      if (file.buffer) {
        console.log('tournamentService.uploadScreenshot: Using file.buffer for Supabase upload.');
        fileBody = file.buffer;
      } else if (file.path) {
        console.log(`tournamentService.uploadScreenshot: Reading file from disk path: ${file.path} for Supabase upload.`);
        if (!fs.existsSync(file.path)) {
          console.error(`tournamentService.uploadScreenshot: File not found at multer path: ${file.path}`);
          throw new Error(`File not found at path: ${file.path}`);
        }
        fileBody = fs.readFileSync(file.path);
      } else {
        console.error('tournamentService.uploadScreenshot: No file buffer or path available in multer file object.');
        throw new Error('No file buffer or path available in multer file object');
      }

      console.log(`tournamentService.uploadScreenshot: Attempting to upload to Supabase. Path: ${supabasePath}, ContentType: ${contentType}`);

      const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
        .from('match-screenshots')
        .upload(supabasePath, fileBody, {
          contentType: contentType,
          upsert: false,
        });

      if (uploadError) {
        console.error('tournamentService.uploadScreenshot: Supabase upload error:', uploadError);
        throw new Error(`Failed to upload screenshot to Supabase: ${uploadError.message}`);
      }

      if (!uploadData) {
        console.error('tournamentService.uploadScreenshot: Supabase upload returned no data and no error.');
        throw new Error('Failed to upload screenshot: Supabase returned no data.');
      }

      const { data: urlData } = this.supabase.storage
        .from('match-screenshots')
        .getPublicUrl(supabasePath);

      if (!urlData || !urlData.publicUrl) {
        console.error('tournamentService.uploadScreenshot: Supabase getPublicUrl returned no publicUrl.');
        throw new Error('Failed to get public URL for screenshot from Supabase.');
      }

      console.log(`tournamentService.uploadScreenshot: Upload successful. Public URL: ${urlData.publicUrl}`);
      return urlData.publicUrl;

    } catch (error) {
      console.error('Error in tournamentService.uploadScreenshot:', error);
      if (error instanceof Error) {
        throw new Error(`TournamentService Error: ${error.message}`);
      }
      throw new Error('Screenshot upload failed due to an unexpected error in tournamentService.');
    }
  }
}

// Create a singleton instance
const tournamentServiceInstance = new TournamentService();

// Export both the class type and the instance
export default tournamentServiceInstance;
