import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wiggyz_app/widgets/shared/golden_app_bar.dart';
import 'package:wiggyz_app/widgets/shared/standard_form_components.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  // General notification settings
  bool _pushNotificationsEnabled = true;
  bool _emailNotificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  // Game-specific notifications
  bool _matchStartNotifications = true;
  bool _matchResultNotifications = true;
  bool _tournamentNotifications = true;
  bool _achievementNotifications = true;
  bool _walletNotifications = true;
  bool _promotionNotifications = true;

  // Marketing notifications
  bool _newsAndUpdatesNotifications = false;
  bool _specialOffersNotifications = false;

  bool _isLoading = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadNotificationSettings();
  }

  Future<void> _loadNotificationSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      
      setState(() {
        // General settings
        _pushNotificationsEnabled = prefs.getBool('push_notifications') ?? true;
        _emailNotificationsEnabled = prefs.getBool('email_notifications') ?? true;
        _soundEnabled = prefs.getBool('notification_sound') ?? true;
        _vibrationEnabled = prefs.getBool('notification_vibration') ?? true;

        // Game-specific settings
        _matchStartNotifications = prefs.getBool('match_start_notifications') ?? true;
        _matchResultNotifications = prefs.getBool('match_result_notifications') ?? true;
        _tournamentNotifications = prefs.getBool('tournament_notifications') ?? true;
        _achievementNotifications = prefs.getBool('achievement_notifications') ?? true;
        _walletNotifications = prefs.getBool('wallet_notifications') ?? true;
        _promotionNotifications = prefs.getBool('promotion_notifications') ?? true;

        // Marketing settings
        _newsAndUpdatesNotifications = prefs.getBool('news_updates_notifications') ?? false;
        _specialOffersNotifications = prefs.getBool('special_offers_notifications') ?? false;
      });
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveNotificationSettings() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Save general settings
      await prefs.setBool('push_notifications', _pushNotificationsEnabled);
      await prefs.setBool('email_notifications', _emailNotificationsEnabled);
      await prefs.setBool('notification_sound', _soundEnabled);
      await prefs.setBool('notification_vibration', _vibrationEnabled);

      // Save game-specific settings
      await prefs.setBool('match_start_notifications', _matchStartNotifications);
      await prefs.setBool('match_result_notifications', _matchResultNotifications);
      await prefs.setBool('tournament_notifications', _tournamentNotifications);
      await prefs.setBool('achievement_notifications', _achievementNotifications);
      await prefs.setBool('wallet_notifications', _walletNotifications);
      await prefs.setBool('promotion_notifications', _promotionNotifications);

      // Save marketing settings
      await prefs.setBool('news_updates_notifications', _newsAndUpdatesNotifications);
      await prefs.setBool('special_offers_notifications', _specialOffersNotifications);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Notification settings saved successfully',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error saving notification settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to save settings. Please try again.',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.white,
      appBar: GoldenAppBar(
        title: 'Notification Settings',
        actions: [
          TextButton(
            onPressed: _isSaving ? null : _saveNotificationSettings,
            child: _isSaving
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                    ),
                  )
                : Text(
                    'Save',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFFFCC00),
              ),
            )
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoCard(isDarkMode),
                    const SizedBox(height: 24),
                    _buildGeneralNotificationsSection(isDarkMode),
                    const SizedBox(height: 24),
                    _buildGameNotificationsSection(isDarkMode),
                    const SizedBox(height: 24),
                    _buildMarketingNotificationsSection(isDarkMode),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildInfoCard(bool isDarkMode) {
    return StandardCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.notifications_outlined,
                color: isDarkMode ? const Color(0xFFD4AF37) : Colors.blue[700],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Notification Preferences',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? const Color(0xFFD4AF37) : Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Customize how and when you receive notifications from WiggyZ Gaming. You can always change these settings later.',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralNotificationsSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'General Settings',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        _buildNotificationToggle(
          'Push Notifications',
          'Receive notifications on your device',
          _pushNotificationsEnabled,
          (value) => setState(() => _pushNotificationsEnabled = value),
          Icons.notifications_outlined,
        ),
        _buildNotificationToggle(
          'Email Notifications',
          'Receive notifications via email',
          _emailNotificationsEnabled,
          (value) => setState(() => _emailNotificationsEnabled = value),
          Icons.email_outlined,
        ),
        _buildNotificationToggle(
          'Sound',
          'Play sound for notifications',
          _soundEnabled,
          (value) => setState(() => _soundEnabled = value),
          Icons.volume_up_outlined,
        ),
        _buildNotificationToggle(
          'Vibration',
          'Vibrate for notifications',
          _vibrationEnabled,
          (value) => setState(() => _vibrationEnabled = value),
          Icons.vibration,
        ),
      ],
    );
  }

  Widget _buildGameNotificationsSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Game Notifications',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        _buildNotificationToggle(
          'Match Start',
          'When your matches are about to begin',
          _matchStartNotifications,
          (value) => setState(() => _matchStartNotifications = value),
          Icons.play_circle_outline,
        ),
        _buildNotificationToggle(
          'Match Results',
          'When match results are available',
          _matchResultNotifications,
          (value) => setState(() => _matchResultNotifications = value),
          Icons.emoji_events_outlined,
        ),
        _buildNotificationToggle(
          'Tournament Updates',
          'Tournament registrations and updates',
          _tournamentNotifications,
          (value) => setState(() => _tournamentNotifications = value),
          Icons.emoji_events,
        ),
        _buildNotificationToggle(
          'Achievements',
          'When you unlock new achievements',
          _achievementNotifications,
          (value) => setState(() => _achievementNotifications = value),
          Icons.star_outline,
        ),
        _buildNotificationToggle(
          'Wallet Activity',
          'Deposits, withdrawals, and earnings',
          _walletNotifications,
          (value) => setState(() => _walletNotifications = value),
          Icons.account_balance_wallet_outlined,
        ),
        _buildNotificationToggle(
          'Promotions',
          'Special events and bonus opportunities',
          _promotionNotifications,
          (value) => setState(() => _promotionNotifications = value),
          Icons.local_offer_outlined,
        ),
      ],
    );
  }

  Widget _buildMarketingNotificationsSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Marketing & Updates',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        _buildNotificationToggle(
          'News & Updates',
          'App updates and gaming news',
          _newsAndUpdatesNotifications,
          (value) => setState(() => _newsAndUpdatesNotifications = value),
          Icons.newspaper,
        ),
        _buildNotificationToggle(
          'Special Offers',
          'Exclusive deals and discounts',
          _specialOffersNotifications,
          (value) => setState(() => _specialOffersNotifications = value),
          Icons.local_offer,
        ),
      ],
    );
  }

  Widget _buildNotificationToggle(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
    IconData icon,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: StandardCard(
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: value,
              onChanged: onChanged,
              activeColor: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }
}
