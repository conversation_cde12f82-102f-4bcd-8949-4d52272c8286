/**
 * Wallet service for handling wallet operations and transactions
 */
import { v4 as uuidv4 } from 'uuid';
import { Request } from 'express';
import { supabase } from '../../../config/supabase';
import { supabaseHelpers } from '../../../utils/supabaseHelpers';
import { 
  initializePayment, 
  verifyWebhookSignature, 
  parseWebhookPayload,
  processPayout,
  PaymentGateway,
  PaymentMethod,
  TransactionStatus
} from './paymentGateway';

// Transaction types to match database schema
export enum TransactionType {
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
  MATCH_FEE = 'purchase', // Use 'purchase' for match entry fees as it's a valid enum value
  TOURNAMENT_FEE = 'purchase', // Use 'purchase' for tournament entry fees
  TOURNAMENT_CREATION = 'purchase', // Use 'purchase' for tournament creation fees
  REFUND = 'refund',
  PRIZE_PAYOUT = 'reward' // Use 'reward' for prize payouts as it's a valid enum value
}

// Severity levels for notifications
export enum Severity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

export const walletService = {
  /**
   * Get user's wallet details including balance and pending transactions
   * @param userId - User ID
   * @returns Wallet details
   */
  async getWalletDetails(userId: string) {
    // Get user's wallet from the dedicated wallets table
    let { data: wallet, error } = await supabase
      .from('wallets')
      .select('balance, currency, reward_points, status')
      .eq('user_id', userId)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        // Create a new wallet if one doesn't exist
        const { data: newWallet, error: createError } = await supabase
          .from('wallets')
          .insert({
            user_id: userId,
            balance: 0,
            currency: 'NPR',
            status: 'active',
            reward_points: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select('balance, currency, reward_points, status')
          .single();
          
        if (createError) throw new Error(`Error creating wallet: ${createError.message}`);
        
        wallet = newWallet;
      } else {
        throw new Error(`Error fetching wallet: ${error.message}`);
      }
    }      // Get pending transactions
    const { data: pendingTransactions, error: txError } = await supabase
      .from('wallet_transactions')
      .select('id, type, amount, status, created_at')
      .eq('user_id', userId)
      .in('status', ['pending', 'processing'])
      .order('created_at', { ascending: false });
    
    if (txError) throw new Error(`Error fetching transactions: ${txError.message}`);
    
    return {
      balance: wallet.balance || 0,
      currency: wallet.currency || 'NPR',
      reward_points: wallet.reward_points || 0,
      status: wallet.status || 'active',
      pending_transactions: pendingTransactions || []
    };
  },

  /**
   * Get transaction history for a user with pagination
   * @param userId - User ID
   * @param page - Page number
   * @param limit - Items per page
   * @returns Transactions with pagination metadata
   */
  async getTransactionHistory(userId: string, page: number = 1, limit: number = 10) {
    // Calculate offset
    const offset = (page - 1) * limit;      // Get transactions with pagination
    const { data: transactions, error, count } = await supabase
      .from('wallet_transactions')
      .select('id, type, amount, status, created_at, description, metadata', { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (error) throw new Error(`Error fetching transaction history: ${error.message}`);
    
    // Calculate pagination metadata
    const totalPages = count ? Math.ceil(count / limit) : 0;
    
    return {
      transactions: transactions || [],
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_items: count || 0,
        items_per_page: limit
      }
    };
  },

  /**
   * Initiate a wallet top-up (deposit)
   * @param userId - User ID
   * @param amount - Amount to deposit
   * @param currency - Currency code
   * @param paymentMethod - Payment method
   * @returns Payment initialization details
   */
  async initiateTopUp(
    userId: string,
    amount: number,
    currency: string = 'USD',
    paymentMethod: string
  ) {
    // Create a transaction record in pending state
    const transactionId = uuidv4();

    // First, ensure the user has a wallet and get the wallet_id
    const wallet = await this.ensureWalletExists(userId, 0);
    console.log(`Using wallet ID ${wallet.id} for user ${userId}`);

    // Determine which payment gateway to use based on payment method
    let gateway: PaymentGateway;
    switch (paymentMethod) {
      case PaymentMethod.CREDIT_CARD:
      case PaymentMethod.DEBIT_CARD:
        gateway = PaymentGateway.STRIPE;
        break;
      case PaymentMethod.BANK_TRANSFER:
        gateway = PaymentGateway.PAYPAL;
        break;
      case PaymentMethod.UPI:
        gateway = PaymentGateway.RAZORPAY;
        break;
      default:
        gateway = PaymentGateway.STRIPE;
    }

    // Initialize payment with the gateway
    const paymentDetails = await initializePayment(
      gateway,
      amount,
      currency,
      {
        transaction_id: transactionId,
        user_id: userId
      }
    );

    // Create transaction record in the database with wallet_id
    const transactionData = {
      id: transactionId,
      wallet_id: wallet.id, // Add the missing wallet_id
      user_id: userId,
      type: TransactionType.DEPOSIT,
      amount: amount,
      currency: currency,
      status: TransactionStatus.PENDING,
      payment_method: paymentMethod,
      payment_gateway: gateway,
      payment_token: paymentDetails.payment_token,
      metadata: {
        redirect_url: paymentDetails.redirect_url,
        expires_at: paymentDetails.expires_at
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log(`Creating transaction record:`, {
      transactionId,
      userId,
      amount,
      currency,
      paymentMethod,
      gateway,
      paymentToken: paymentDetails.payment_token
    });

    const { error } = await supabase
      .from('wallet_transactions')
      .insert(transactionData);

    if (error) {
      console.error(`Error creating transaction:`, error);
      throw new Error(`Error creating transaction: ${error.message}`);
    }

    console.log(`Transaction created successfully with ID: ${transactionId}`);
    
    // Return payment details for the frontend
    return {
      transaction_id: transactionId,
      payment_token: paymentDetails.payment_token,
      redirect_url: paymentDetails.redirect_url,
      gateway: gateway,
      expires_at: paymentDetails.expires_at
    };
  },

  /**
   * Verify the signature of a payment gateway webhook
   * @param gateway - Payment gateway name
   * @param request - Express request object
   * @returns Whether the signature is valid
   */
  async verifyWebhookSignature(gateway: string, request: Request): Promise<boolean> {
    switch (gateway) {
      case PaymentGateway.STRIPE:
      case PaymentGateway.PAYPAL:
      case PaymentGateway.RAZORPAY:
        return verifyWebhookSignature(gateway as PaymentGateway, request);
      default:
        return false;
    }
  },

  /**
   * Process a webhook payload from a payment gateway
   * @param gateway - Payment gateway name
   * @param payload - Webhook payload
   * @returns Processing result
   */
  async processWebhookPayload(gateway: string, payload: any) {
    // Extract transaction ID and status from the payload
    const { transactionId, status, metadata } = parseWebhookPayload(
      gateway as PaymentGateway,
      payload
    );
    
    if (!transactionId) {
      throw new Error('Invalid webhook payload: missing transaction ID');
    }
      // Update transaction status
    const { data: transaction, error } = await supabase
      .from('wallet_transactions')
      .update({
        status: status,
        metadata: { 
          ...metadata,
          webhook_received_at: new Date().toISOString()
        },
        updated_at: new Date().toISOString()
      })
      .eq('id', transactionId)
      .select()
      .single();
    
    if (error) throw new Error(`Error updating transaction: ${error.message}`);
    if (!transaction) throw new Error(`Transaction not found: ${transactionId}`);
    
    // If payment was successful, update user's balance
    if (status === TransactionStatus.COMPLETED) {
      await this.updateUserBalance(
        transaction.user_id,
        transaction.amount,
        transaction.type
      );
      
      // Create notification for user
      await this.createNotification(
        transaction.user_id,
        'Payment Successful',
        `Your deposit of ${transaction.amount} ${transaction.currency} has been processed successfully.`,
        Severity.INFO,
        {
          transaction_id: transaction.id,
          amount: transaction.amount,
          currency: transaction.currency
        }
      );
    } else if (status === TransactionStatus.FAILED) {
      // Create failure notification
      await this.createNotification(
        transaction.user_id,
        'Payment Failed',
        `Your deposit of ${transaction.amount} ${transaction.currency} could not be processed.`,
        Severity.WARNING,
        {
          transaction_id: transaction.id,
          amount: transaction.amount,
          currency: transaction.currency
        }
      );
    }
    
    return {
      transaction_id: transaction.id,
      status: transaction.status
    };
  },

  /**
   * Initiate a withdrawal from the user's wallet (Manual Processing)
   * Creates a withdrawal request that requires admin approval instead of automatic processing
   * @param userId - User ID
   * @param amount - Amount to withdraw
   * @param currency - Currency code
   * @param withdrawalMethod - Withdrawal method
   * @param accountDetails - User's withdrawal account details
   * @returns Withdrawal details
   */
  async initiateWithdrawal(
    userId: string,
    amount: number,
    currency: string = 'USD',
    withdrawalMethod: string,
    accountDetails: any
  ) {
    // First, check if user has sufficient balance
    let { data: wallet, error: walletError } = await supabase
      .from('wallets')
      .select('id, balance')
      .eq('user_id', userId)
      .single();

    if (walletError) {
      if (walletError.code === 'PGRST116') {
        // Create a new wallet if one doesn't exist
        const { data: newWallet, error: createError } = await supabase
          .from('wallets')
          .insert({
            user_id: userId,
            balance: 0,
            currency: 'NPR',
            status: 'active',
            reward_points: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select('id, balance')
          .single();

        if (createError) throw new Error(`Error creating wallet: ${createError.message}`);

        wallet = newWallet;
      } else {
        throw new Error(`Error fetching wallet: ${walletError.message}`);
      }
    }

    // Verify sufficient balance
    const currentBalance = wallet.balance || 0;
    if (currentBalance < amount) {
      throw new Error('Insufficient balance');
    }

    // Create a transaction record in pending state (awaiting admin approval)
    const transactionId = uuidv4();

    // Determine which payment gateway to use based on withdrawal method
    let gateway: PaymentGateway;
    switch (withdrawalMethod) {
      case 'bank_transfer':
        gateway = PaymentGateway.RAZORPAY; // Use Razorpay for bank transfers in India
        break;
      default:
        gateway = PaymentGateway.RAZORPAY; // Default to Razorpay for Indian market
    }

    // Calculate fees and net amount for withdrawal
    // Minimum ₹5 transaction fee OR 2% of withdrawal amount (whichever is higher)
    const calculatedFee = amount * 0.02; // 2% transaction fee
    const transactionFee = Math.max(5.0, calculatedFee); // Minimum ₹5
    const gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
    const totalDeductions = transactionFee + gstOnFee;
    const netAmountToTransfer = amount - totalDeductions; // Net amount to be transferred to user's bank

    console.log(`Manual withdrawal request for transaction ${transactionId}:`, {
      withdrawalAmount: amount,
      transactionFee,
      gstOnFee,
      totalDeductions,
      netAmountToTransfer
    });

    // Create transaction record with PENDING status (awaiting admin approval)
    const { error: txError } = await supabase
      .from('wallet_transactions')
      .insert({
        id: transactionId,
        wallet_id: wallet.id,
        user_id: userId,
        type: TransactionType.WITHDRAWAL,
        amount: -amount, // Negative amount for withdrawals (full amount deducted from wallet)
        currency: currency,
        status: TransactionStatus.PENDING, // Changed from PROCESSING to PENDING
        payment_method: withdrawalMethod,
        payment_gateway: gateway,
        metadata: {
          account_details: accountDetails,
          fee_breakdown: {
            gross_amount: amount,
            transaction_fee: transactionFee,
            gst_on_fee: gstOnFee,
            total_deductions: totalDeductions,
            net_amount_transferred: netAmountToTransfer
          },
          manual_processing: true // Flag to indicate this requires manual processing
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (txError) throw new Error(`Error creating transaction: ${txError.message}`);

    // Update user's balance immediately (as per requirement)
    await this.updateUserBalance(userId, -amount, TransactionType.WITHDRAWAL);

    // Create withdrawal request for admin processing
    const withdrawalRequestId = uuidv4();
    const { error: wrError } = await supabase
      .from('withdrawal_requests')
      .insert({
        id: withdrawalRequestId,
        user_id: userId,
        wallet_id: wallet.id,
        transaction_id: transactionId,
        amount: amount,
        currency: currency,
        account_details: accountDetails,
        status: 'pending',
        requested_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (wrError) {
      // If withdrawal request creation fails, revert the transaction and balance
      await supabase
        .from('wallet_transactions')
        .update({
          status: TransactionStatus.FAILED,
          metadata: {
            ...accountDetails,
            error_message: 'Failed to create withdrawal request'
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', transactionId);

      // Refund the amount to the user's balance
      await this.updateUserBalance(userId, amount, TransactionType.REFUND);

      throw new Error(`Error creating withdrawal request: ${wrError.message}`);
    }

    // Create notification for user about pending withdrawal
    await this.createNotification(
      userId,
      'Withdrawal Request Submitted',
      `Your withdrawal request of ₹${amount} has been submitted and is pending admin approval. The amount has been deducted from your wallet.`,
      Severity.INFO,
      {
        transaction_id: transactionId,
        withdrawal_request_id: withdrawalRequestId,
        amount: amount,
        currency: currency,
        net_amount: netAmountToTransfer
      }
    );

    // Create admin notification for new withdrawal request
    await this.createAdminNotification(
      'withdrawal_request',
      'New Withdrawal Request',
      `New withdrawal request of ₹${amount} from user requires approval.`,
      'warning',
      null, // Send to all admins
      'withdrawal_request',
      withdrawalRequestId,
      {
        user_id: userId,
        amount: amount,
        currency: currency,
        net_amount: netAmountToTransfer,
        transaction_id: transactionId,
        account_details: accountDetails
      },
      true, // Action required
      `/admin/withdrawals/${withdrawalRequestId}`
    );

    return {
      transaction_id: transactionId,
      withdrawal_request_id: withdrawalRequestId,
      status: 'pending',
      message: 'Withdrawal request submitted successfully. Awaiting admin approval.',
      amount: amount,
      net_amount: netAmountToTransfer,
      fees: {
        transaction_fee: transactionFee,
        gst_on_fee: gstOnFee,
        total_deductions: totalDeductions
      }
    };
  },

  /**
   * Create a notification for a user
   * @param userId - User ID
   * @param title - Notification title
   * @param message - Notification message
   * @param severity - Notification severity
   * @param metadata - Additional notification metadata
   */
  async createNotification(
    userId: string,
    title: string,
    message: string,
    severity: Severity = Severity.INFO,
    metadata?: any
  ) {
    const { error: notifError } = await supabase
      .from('notifications')
      .insert({
        id: uuidv4(),
        user_id: userId,
        title: title,
        message: message,
        severity: severity,
        read: false,
        metadata: metadata,
        created_at: new Date().toISOString()
      });
    
    if (notifError) {
      console.error('Error creating notification:', notifError);
    }
  },

  /**
   * Create an admin notification
   * @param type - Notification type
   * @param title - Notification title
   * @param message - Notification message
   * @param severity - Notification severity
   * @param targetAdminId - Specific admin ID (null for all admins)
   * @param relatedEntityType - Type of related entity
   * @param relatedEntityId - ID of related entity
   * @param metadata - Additional notification metadata
   * @param actionRequired - Whether action is required
   * @param actionUrl - URL for taking action
   */
  async createAdminNotification(
    type: string,
    title: string,
    message: string,
    severity: string = 'info',
    targetAdminId?: string | null,
    relatedEntityType?: string,
    relatedEntityId?: string,
    metadata?: any,
    actionRequired: boolean = false,
    actionUrl?: string
  ) {
    try {
      const { data, error } = await supabase.rpc('create_admin_notification', {
        p_type: type,
        p_title: title,
        p_message: message,
        p_severity: severity,
        p_target_admin_id: targetAdminId,
        p_related_entity_type: relatedEntityType,
        p_related_entity_id: relatedEntityId,
        p_metadata: metadata,
        p_action_required: actionRequired,
        p_action_url: actionUrl
      });

      if (error) {
        console.error('Error creating admin notification:', error);
      } else {
        console.log(`Admin notification created: ${data} (${type})`);
      }

      return data;
    } catch (error) {
      console.error('Error creating admin notification:', error);
    }
  },

  /**
   * Update a user's wallet balance securely
   * @param userId - User ID
   * @param amount - Amount to add (positive) or subtract (negative)
   * @param transactionType - Type of transaction causing the update
   * @returns Updated balance
   */
  async updateUserBalance(
    userId: string,
    amount: number,
    transactionType: TransactionType
  ) {
    try {
      // Lock the wallet record for update
      let { data: wallet, error: lockError } = await supabase
        .from('wallets')
        .select('balance')
        .eq('user_id', userId)
        .single();

      if (lockError) {
        if (lockError.code === 'PGRST116') {
          // Create wallet if it doesn't exist
          const { data: newWallet, error: createError } = await supabase
            .from('wallets')
            .insert({
              user_id: userId,
              balance: 0,
              currency: 'NPR',
              status: 'active',
              reward_points: 0,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select('balance')
            .single();
            
          if (createError) throw new Error(`Error creating wallet: ${createError.message}`);
          wallet = newWallet;
        } else {
          throw new Error(`Error locking wallet: ${lockError.message}`);
        }
      }
      
      // Check for sufficient balance when amount is negative (withdrawal)
      if (amount < 0 && wallet.balance + amount < 0) {
        throw new Error('Insufficient balance');
      }

      // Update the balance
      const newBalance = wallet.balance + amount;
      const { error: updateError } = await supabase
        .from('wallets')
        .update({ 
          balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) throw new Error(`Error updating wallet balance: ${updateError.message}`);

      return { balance: newBalance };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Add diamonds to a user's wallet
   * @param userId - User ID
   * @param amount - Amount of diamonds to add
   * @param source - Source of the diamonds (e.g., 'daily_reward', 'achievement', etc.)
   * @param description - Description of the diamonds source
   * @param idempotencyKey - Unique key to prevent duplicate transactions
   * @returns Updated wallet balance
   */
  async addDiamonds(
    userId: string,
    amount: number,
    source: string,
    description: string,
    idempotencyKey?: string
  ) {
    try {
      // Check for existing transaction with this idempotency key
      if (idempotencyKey) {
        const { data: existingTx, error: txError } = await supabase
          .from('transactions')
          .select('id')
          .eq('idempotency_key', idempotencyKey)
          .single();

        // If we found a transaction with this key, return success without making changes
        if (!txError && existingTx) {
          return { success: true, message: 'Transaction already processed', idempotent: true };
        }
      }

      // Update the user's balance
      const newBalance = await this.updateUserBalance(userId, amount, TransactionType.PRIZE_PAYOUT);

      // Record the transaction
      const transactionId = uuidv4();
      const { error: txError } = await supabase
        .from('transactions')
        .insert({
          id: transactionId,
          user_id: userId,
          amount,
          type: TransactionType.PRIZE_PAYOUT,
          status: TransactionStatus.COMPLETED,
          description,
          source,
          idempotency_key: idempotencyKey || null,
          created_at: new Date().toISOString()
        });

      if (txError) throw new Error(`Error recording transaction: ${txError.message}`);

      // Create a notification
      await this.createNotification(
        userId,
        'Diamonds Added',
        `You received ${amount} diamonds from ${description}`,
        Severity.INFO,
        { amount, source }
      );

      return { 
        success: true, 
        diamonds: amount, 
        balance: newBalance, 
        transactionId 
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Processes the entry fee for a match, deducting from user's wallet
   * and recording the transaction.
   * @param userId - The ID of the user.
   * @param matchId - The ID of the match.
   * @param entryFee - The amount to deduct.
   * @param currency - The currency of the transaction.
   * @returns The created transaction record.
   * @throws Error if insufficient funds or any database operation fails.
   */  async processMatchEntryFee(userId: string, matchId: string, entryFee: number, currency: string = 'NPR') {
    console.log(`Processing match entry fee for user ${userId}, match ${matchId}, amount ${entryFee}`);
    
    if (entryFee <= 0) {
      console.log(`No entry fee to process (amount: ${entryFee})`);
      return null;
    }

    // Ensure wallet exists - create if not found
    let { data: wallet, error: walletError } = await supabase
      .from('wallets')
      .select('id, balance, currency')
      .eq('user_id', userId)
      .single();

    if (walletError) {
      if (walletError.code === 'PGRST116') { // Not found
        console.log(`Wallet not found for user ${userId}, creating new wallet`);
        // Create wallet if it doesn't exist
        const { data: newWallet, error: createError } = await supabase
          .from('wallets')
          .insert({
            user_id: userId,
            balance: 0,
            currency: currency,
            status: 'active',
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select('id, balance, currency')
          .single();
          
        if (createError) {
          console.error(`Error creating wallet for user ${userId}:`, createError);
          throw new Error(`Error creating wallet: ${createError.message}`);
        }
        
        wallet = newWallet;
        console.log(`Created new wallet for user ${userId} with balance ${wallet.balance}`);
      } else {
        console.error(`Error fetching wallet for user ${userId}:`, walletError);
        throw new Error(`Error fetching wallet: ${walletError.message}`);
      }
    }    console.log(`Current wallet balance: ${wallet.balance}, required: ${entryFee}`);
    
    if (wallet.balance < entryFee) {
      console.log(`Insufficient funds: wallet balance ${wallet.balance} < entry fee ${entryFee}`);
      throw new Error(`Insufficient funds to join the match. Current balance: ${wallet.balance}, Required: ${entryFee}`);
    }

    const newBalance = wallet.balance - entryFee;
    console.log(`Deducting ${entryFee} from wallet. New balance will be: ${newBalance}`);

    // 1. Update wallet balance
    const { error: updateError } = await supabase
      .from('wallets')
      .update({ balance: newBalance, updated_at: new Date().toISOString() })
      .eq('user_id', userId);

    if (updateError) {
      console.error(`Error updating wallet balance for user ${userId}:`, updateError);
      throw new Error(`Error updating wallet balance: ${updateError.message}`);
    }

    console.log(`Successfully updated wallet balance for user ${userId}. New balance: ${newBalance}`);    // 2. Record the transaction
    const transactionData = {      id: uuidv4(), 
      wallet_id: wallet.id, // Add wallet_id reference
      user_id: userId,
      type: TransactionType.MATCH_FEE, // Use MATCH_FEE for entry fees
      amount: -entryFee, // Negative for deduction
      currency: wallet.currency || currency,
      status: TransactionStatus.COMPLETED,      description: `Entry fee for match ${matchId}`,
      metadata: { matchId: matchId }, // Store reference in metadata instead
      created_at: new Date().toISOString(),
    };

    console.log(`Recording transaction for match entry fee:`, transactionData);

    const { data: newTransaction, error: transactionError } = await supabase
      .from('wallet_transactions') // Use wallet_transactions table
      .insert(transactionData)
      .select()
      .single();

    if (transactionError) {
      console.error(`CRITICAL: Wallet balance updated for user ${userId} (new balance: ${newBalance}) but failed to log transaction for match ${matchId}. Attempting to revert balance. Error:`, transactionError);
      // Attempt to revert balance (best effort without a formal transaction manager)
      const { error: revertError } = await supabase
        .from('wallets')
        .update({ balance: wallet.balance, updated_at: new Date().toISOString() })
        .eq('user_id', userId);
      if (revertError) {
        console.error(`CRITICAL: Failed to revert wallet balance for user ${userId} after transaction log failure. Manual correction needed. Revert Error:`, revertError);
      } else {
        console.log(`Successfully reverted wallet balance for user ${userId} back to ${wallet.balance}`);
      }
      throw new Error(`Failed to record match entry fee transaction: ${transactionError.message}. Wallet update was reverted.`);
    }

    console.log(`Successfully recorded transaction for match entry fee. Transaction ID: ${newTransaction.id}`);
    return newTransaction;
  },

  /**
   * Ensure wallet exists for user, create if not found
   * @param userId - User ID
   * @param initialBalance - Initial balance for new wallet
   * @returns Wallet details
   */
  async ensureWalletExists(userId: string, initialBalance: number = 0) {
    try {      // Try to get existing wallet
      const { data: wallet, error } = await supabase
        .from('wallets')
        .select('id, balance, currency, status')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // Wallet doesn't exist, create it          console.log(`Creating new wallet for user ${userId} with initial balance ${initialBalance}`);
          const { data: newWallet, error: createError } = await supabase
            .from('wallets')
            .insert({
              user_id: userId,
              balance: initialBalance,
              currency: 'NPR',
              status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select('id, balance, currency, status')
            .single();

          if (createError) {
            console.error(`Error creating wallet for user ${userId}:`, createError);
            throw new Error(`Failed to create wallet: ${createError.message}`);
          }

          console.log(`Successfully created wallet for user ${userId}`);
          return newWallet;
        } else {
          console.error(`Error fetching wallet for user ${userId}:`, error);
          throw new Error(`Failed to fetch wallet: ${error.message}`);
        }
      }

      // Wallet exists, return it
      return wallet;
    } catch (error) {
      console.error(`Error in ensureWalletExists for user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Check if user has sufficient balance for a transaction
   * @param userId - User ID
   * @param amount - Amount to check
   * @returns Boolean indicating if user has sufficient balance
   */
  async checkSufficientBalance(userId: string, amount: number): Promise<boolean> {
    try {
      const wallet = await this.ensureWalletExists(userId);
      console.log(`Checking balance for user ${userId}: wallet balance ${wallet.balance}, required ${amount}`);
      return wallet.balance >= amount;
    } catch (error) {
      console.error(`Error checking balance for user ${userId}:`, error);
      return false;
    }
  },

  /**
   * Processes the entry fee for a tournament, deducting from user's wallet
   * and recording the transaction.
   * @param userId - The ID of the user.
   * @param tournamentId - The ID of the tournament.
   * @param entryFee - The amount to deduct.
   * @param currency - The currency of the transaction.
   * @param transactionType - Type of transaction (creation or registration).
   * @returns The created transaction record.
   * @throws Error if insufficient funds or any database operation fails.
   */
  async processTournamentEntryFee(userId: string, tournamentId: string, entryFee: number, currency: string = 'NPR', transactionType: 'creation' | 'registration' = 'registration') {
    console.log(`Processing tournament entry fee for user ${userId}, tournament ${tournamentId}, amount ${entryFee}, type ${transactionType}`);

    if (entryFee <= 0) {
      console.log(`No entry fee to process (amount: ${entryFee})`);
      return null;
    }

    // Get user's wallet
    let { data: wallet, error: walletError } = await supabase
      .from('wallets')
      .select('id, balance, currency')
      .eq('user_id', userId)
      .single();

    if (walletError) {
      if (walletError.code === 'PGRST116') {
        // Create a new wallet if one doesn't exist
        const { data: newWallet, error: createError } = await supabase
          .from('wallets')
          .insert({
            user_id: userId,
            balance: 0,
            currency: currency,
            status: 'active',
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select('id, balance, currency')
          .single();

        if (createError) {
          console.error(`Error creating wallet for user ${userId}:`, createError);
          throw new Error(`Error creating wallet: ${createError.message}`);
        }

        wallet = newWallet;
        console.log(`Created new wallet for user ${userId} with balance ${wallet.balance}`);
      } else {
        console.error(`Error fetching wallet for user ${userId}:`, walletError);
        throw new Error(`Error fetching wallet: ${walletError.message}`);
      }
    }

    console.log(`Current wallet balance: ${wallet.balance}, required: ${entryFee}`);

    if (wallet.balance < entryFee) {
      console.log(`Insufficient funds: wallet balance ${wallet.balance} < entry fee ${entryFee}`);
      throw new Error(`Insufficient funds to ${transactionType === 'creation' ? 'create' : 'join'} the tournament. Current balance: ${wallet.balance}, Required: ${entryFee}`);
    }

    const newBalance = wallet.balance - entryFee;
    console.log(`Deducting ${entryFee} from wallet. New balance will be: ${newBalance}`);

    // 1. Update wallet balance
    const { error: updateError } = await supabase
      .from('wallets')
      .update({ balance: newBalance, updated_at: new Date().toISOString() })
      .eq('user_id', userId);

    if (updateError) {
      console.error(`Error updating wallet balance for user ${userId}:`, updateError);
      throw new Error(`Error updating wallet balance: ${updateError.message}`);
    }

    console.log(`Successfully updated wallet balance for user ${userId}. New balance: ${newBalance}`);

    // 2. Record the transaction
    const transactionData = {
      id: uuidv4(),
      wallet_id: wallet.id,
      user_id: userId,
      type: transactionType === 'creation' ? TransactionType.TOURNAMENT_CREATION : TransactionType.TOURNAMENT_FEE,
      amount: -entryFee, // Negative for deduction
      currency: wallet.currency || currency,
      status: TransactionStatus.COMPLETED,
      description: `Entry fee for tournament ${transactionType} ${tournamentId}`,
      metadata: { tournamentId: tournamentId, transactionType: transactionType },
      created_at: new Date().toISOString(),
    };

    const { data: transaction, error: transactionError } = await supabase
      .from('wallet_transactions')
      .insert(transactionData)
      .select()
      .single();

    if (transactionError) {
      console.error(`Error recording transaction for user ${userId}:`, transactionError);
      // Try to rollback wallet balance update
      await supabase
        .from('wallets')
        .update({ balance: wallet.balance, updated_at: new Date().toISOString() })
        .eq('user_id', userId);
      throw new Error(`Error recording transaction: ${transactionError.message}`);
    }

    console.log(`Successfully recorded transaction for user ${userId}:`, transaction);
    return transaction;
  },

  /**
   * Get current wallet balance for user
   * @param userId - User ID
   * @returns Current balance
   */
  async getCurrentBalance(userId: string): Promise<number> {
    try {
      const wallet = await this.ensureWalletExists(userId);
      return wallet.balance;
    } catch (error) {
      console.error(`Error getting balance for user ${userId}:`, error);
      return 0;
    }
  },

  /**
   * Process a refund to user's wallet with proper transaction record
   * @param userId - User ID
   * @param amount - Amount to refund (must be positive)
   * @param description - Description for the refund transaction
   * @param metadata - Optional metadata for the transaction (e.g., match_id)
   * @returns Updated wallet and transaction details
   */
  async processRefund(userId: string, amount: number, description: string, metadata: any = null) {
    console.log(`Processing refund for user ${userId}: amount ${amount}, description: ${description}`);

    if (amount <= 0) {
      throw new Error('Refund amount must be positive');
    }

    // Ensure wallet exists
    let wallet = await this.ensureWalletExists(userId);

    const newBalance = (wallet.balance || 0) + amount;
    console.log(`Updating wallet balance from ${wallet.balance} to ${newBalance} (refund)`);

    // Update wallet balance
    const { error: updateError } = await supabase
      .from('wallets')
      .update({
        balance: newBalance,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (updateError) {
      console.error(`Error updating wallet balance for user ${userId}:`, updateError);
      throw new Error(`Error updating wallet balance: ${updateError.message}`);
    }

    // Record the refund transaction
    const transactionData = {
      id: uuidv4(),
      wallet_id: wallet.id,
      user_id: userId,
      type: TransactionType.REFUND,
      amount: amount, // Positive for refund (money being added back)
      currency: wallet.currency || 'NPR',
      status: TransactionStatus.COMPLETED,
      description: description,
      metadata: metadata,
      created_at: new Date().toISOString(),
    };

    console.log(`Recording refund transaction:`, transactionData);

    const { data: newTransaction, error: transactionError } = await supabase
      .from('wallet_transactions')
      .insert(transactionData)
      .select()
      .single();

    if (transactionError) {
      console.error(`Error recording refund transaction for user ${userId}:`, transactionError);
      // Try to revert balance update
      await supabase
        .from('wallets')
        .update({
          balance: wallet.balance,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);
      throw new Error(`Failed to record refund transaction: ${transactionError.message}`);
    }

    console.log(`Successfully processed refund of ${amount} for user ${userId}. New balance: ${newBalance}`);
    return {
      wallet: { ...wallet, balance: newBalance },
      transaction: newTransaction
    };
  },

  /**
   * Add balance to user's wallet (for testing/admin purposes)
   * @param userId - User ID
   * @param amount - Amount to add
   * @param description - Transaction description
   * @returns Updated wallet and transaction details
   */
  async addBalance(userId: string, amount: number, description: string = 'Manual balance addition') {
    console.log(`Adding balance for user ${userId}: amount ${amount}, description: ${description}`);
    
    if (amount <= 0) {
      throw new Error('Amount must be positive');
    }

    // Ensure wallet exists
    let wallet = await this.ensureWalletExists(userId);
    
    const newBalance = (wallet.balance || 0) + amount;
    console.log(`Updating wallet balance from ${wallet.balance} to ${newBalance}`);

    // Update wallet balance
    const { error: updateError } = await supabase
      .from('wallets')
      .update({ 
        balance: newBalance, 
        updated_at: new Date().toISOString() 
      })
      .eq('user_id', userId);

    if (updateError) {
      console.error(`Error updating wallet balance for user ${userId}:`, updateError);
      throw new Error(`Error updating wallet balance: ${updateError.message}`);
    }

    // Record the transaction
    const transactionData = {
      id: uuidv4(),
      wallet_id: wallet.id, // Add wallet_id reference
      user_id: userId,
      type: TransactionType.DEPOSIT,
      amount: amount, // Positive for addition
      currency: wallet.currency || 'NPR',
      status: TransactionStatus.COMPLETED,
      description: description,
      metadata: null, // No specific reference for manual addition
      created_at: new Date().toISOString(),
    };

    console.log(`Recording transaction:`, transactionData);

    const { data: newTransaction, error: transactionError } = await supabase
      .from('wallet_transactions')
      .insert(transactionData)
      .select()
      .single();

    if (transactionError) {
      console.error(`Error recording transaction for user ${userId}:`, transactionError);
      // Try to revert balance update
      await supabase
        .from('wallets')
        .update({ 
          balance: wallet.balance, 
          updated_at: new Date().toISOString() 
        })
        .eq('user_id', userId);
      throw new Error(`Failed to record transaction: ${transactionError.message}`);
    }

    console.log(`Successfully added ${amount} to wallet for user ${userId}. New balance: ${newBalance}`);

    return {
      wallet: {
        balance: newBalance,
        currency: wallet.currency,
        updated_at: new Date().toISOString()
      },
      transaction: newTransaction
    };
  },

  /**
   * Process a successful payment and update wallet balance
   * @param userId - User ID
   * @param transactionId - Transaction ID from our system
   * @param paymentDetails - Payment details from Razorpay
   * @returns Updated wallet balance and transaction details
   */
  async processSuccessfulPayment(
    userId: string,
    transactionId: string,
    paymentDetails: {
      razorpay_order_id: string;
      razorpay_payment_id: string;
      amount: number;
      currency: string;
      method: string;
      status: string;
    }
  ) {
    try {
      console.log(`Processing payment for user ${userId}`, {
        transactionId,
        paymentId: paymentDetails.razorpay_payment_id,
        amount: paymentDetails.amount,
        currency: paymentDetails.currency
      });

      // Check for idempotency - if this payment was already processed successfully
      const { data: existingProcessedTx, error: idempotencyError } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', userId)
        .eq('status', TransactionStatus.COMPLETED)
        .contains('payment_gateway_response', { razorpay_payment_id: paymentDetails.razorpay_payment_id })
        .single();

      if (!idempotencyError && existingProcessedTx) {
        console.log(`Payment already processed successfully for payment ID ${paymentDetails.razorpay_payment_id}`);
        const amountInRupees = paymentDetails.amount / 100;
        return {
          transactionId: existingProcessedTx.id,
          paymentId: paymentDetails.razorpay_payment_id,
          amount: amountInRupees,
          currency: paymentDetails.currency,
          newBalance: (await this.getWalletDetails(userId)).balance,
          status: 'completed'
        };
      }

      // First, try to find the transaction with more flexible criteria
      console.log(`Looking up transaction with ID: ${transactionId} for user: ${userId}`);

      const { data: existingTransaction, error: txError } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('id', transactionId)
        .eq('user_id', userId)
        .single();

      if (txError || !existingTransaction) {
        console.error(`Transaction lookup failed for ID ${transactionId}:`, txError);

        // Try to find any transactions for this user to debug
        const { data: userTransactions, error: debugError } = await supabase
          .from('wallet_transactions')
          .select('id, user_id, status, created_at')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(5);

        console.log(`Recent transactions for user ${userId}:`, userTransactions);

        throw new Error(`Transaction not found: ${transactionId}. Please contact support if this issue persists.`);
      }

      console.log(`Found transaction:`, {
        id: existingTransaction.id,
        status: existingTransaction.status,
        amount: existingTransaction.amount,
        currency: existingTransaction.currency
      });

      // Check if transaction is already processed
      if (existingTransaction.status === TransactionStatus.COMPLETED) {
        console.log(`Transaction ${transactionId} already completed`);
        const amountInRupees = paymentDetails.amount / 100;
        return {
          transactionId,
          paymentId: paymentDetails.razorpay_payment_id,
          amount: amountInRupees,
          currency: paymentDetails.currency,
          newBalance: (await this.getWalletDetails(userId)).balance,
          status: 'completed'
        };
      }

      // Check if transaction is in a failed state
      if (existingTransaction.status === TransactionStatus.FAILED) {
        throw new Error(`Transaction ${transactionId} has failed and cannot be processed. Please create a new payment.`);
      }

      // Verify transaction is in a processable state
      if (existingTransaction.status !== TransactionStatus.PENDING) {
        throw new Error(`Transaction ${transactionId} is in ${existingTransaction.status} state and cannot be processed.`);
      }

      // Convert amounts for comparison (Razorpay amount is in paise, our stored amount is in rupees)
      const amountInRupees = paymentDetails.amount / 100;
      const storedAmount = existingTransaction.amount;

      console.log(`Amount comparison:`, {
        razorpayAmountPaise: paymentDetails.amount,
        razorpayAmountRupees: amountInRupees,
        storedAmountRupees: storedAmount,
        match: Math.abs(storedAmount - amountInRupees) < 0.01 // Allow for small floating point differences
      });

      // Verify amounts match (with small tolerance for floating point precision)
      if (Math.abs(storedAmount - amountInRupees) >= 0.01) {
        throw new Error(`Payment amount mismatch: expected ₹${storedAmount}, received ₹${amountInRupees}`);
      }

      // Set transaction to processing state to prevent race conditions
      console.log(`Setting transaction ${transactionId} to processing state`);

      // First, check current transaction status
      const { data: currentTx, error: checkError } = await supabase
        .from('wallet_transactions')
        .select('id, status, user_id, amount')
        .eq('id', transactionId)
        .single();

      console.log(`Current transaction before processing update:`, { currentTx, checkError });

      if (checkError || !currentTx) {
        throw new Error(`Transaction ${transactionId} not found: ${checkError?.message || 'Unknown error'}`);
      }

      // If already completed, return success
      if (currentTx.status === TransactionStatus.COMPLETED) {
        console.log(`Transaction ${transactionId} is already completed, skipping processing`);
        return {
          success: true,
          transactionId,
          paymentId: paymentDetails.razorpay_payment_id,
          amount: parseFloat(currentTx.amount),
          currency: 'INR',
          status: 'completed'
        };
      }

      const { data: processingResult, error: processingUpdateError } = await supabase
        .from('wallet_transactions')
        .update({
          status: TransactionStatus.PROCESSING,
          updated_at: new Date().toISOString()
        })
        .eq('id', transactionId)
        .eq('status', TransactionStatus.PENDING) // Only update if still pending
        .select();

      console.log(`Processing state update result:`, { processingResult, processingUpdateError, affectedRows: processingResult?.length || 0 });

      if (processingUpdateError) {
        console.error(`Processing state update error:`, processingUpdateError);
        // Don't throw error here, continue with payment processing
        console.warn(`Warning: Could not set transaction to processing state, but continuing with payment verification`);
      }

      if (!processingResult || processingResult.length === 0) {
        console.warn(`Warning: Transaction ${transactionId} may already be processed or may not exist in pending state. Continuing with verification.`);
        // Check if transaction exists and what its current status is
        const { data: existingTransaction, error: checkError } = await supabase
          .from('wallet_transactions')
          .select('id, status, user_id, amount')
          .eq('id', transactionId)
          .single();

        if (checkError || !existingTransaction) {
          throw new Error(`Transaction ${transactionId} not found: ${checkError?.message || 'Unknown error'}`);
        }

        console.log(`Existing transaction status: ${existingTransaction.status}`);

        // If transaction is already completed, return success
        if (existingTransaction.status === TransactionStatus.COMPLETED) {
          console.log(`Transaction ${transactionId} is already completed, returning success`);
          return {
            success: true,
            transactionId,
            paymentId: paymentDetails.razorpay_payment_id,
            amount: parseFloat(existingTransaction.amount),
            currency: 'INR',
            status: 'completed'
          };
        }

        // If transaction is in processing state, continue
        if (existingTransaction.status !== TransactionStatus.PROCESSING && existingTransaction.status !== TransactionStatus.PENDING) {
          throw new Error(`Transaction ${transactionId} is in invalid state: ${existingTransaction.status}`);
        }
      }

      // Update transaction status to completed
      console.log(`Updating transaction ${transactionId} to completed status`);

      // Check current status before final update
      const { data: preUpdateTx, error: preUpdateError } = await supabase
        .from('wallet_transactions')
        .select('id, status, user_id, amount')
        .eq('id', transactionId)
        .single();

      console.log(`Transaction status before completion update:`, { preUpdateTx, preUpdateError });

      const { data: updateResult, error: updateTxError } = await supabase
        .from('wallet_transactions')
        .update({
          status: TransactionStatus.COMPLETED,
          payment_gateway_response: {
            razorpay_order_id: paymentDetails.razorpay_order_id,
            razorpay_payment_id: paymentDetails.razorpay_payment_id,
            method: paymentDetails.method,
            status: paymentDetails.status,
            processed_at: new Date().toISOString()
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', transactionId)
        .select();

      console.log(`Transaction completion update result:`, { updateResult, updateTxError, affectedRows: updateResult?.length || 0 });

      if (updateTxError) {
        console.error(`Transaction update error:`, updateTxError);
        throw new Error(`Failed to update transaction: ${updateTxError.message}`);
      }

      if (!updateResult || updateResult.length === 0) {
        console.warn(`No transaction was updated for ID: ${transactionId}. Checking current status...`);

        // Check if transaction is already completed
        const { data: finalCheck, error: finalCheckError } = await supabase
          .from('wallet_transactions')
          .select('id, status, amount, currency')
          .eq('id', transactionId)
          .single();

        if (finalCheckError || !finalCheck) {
          throw new Error(`Transaction verification failed: ${finalCheckError?.message || 'Transaction not found'}`);
        }

        if (finalCheck.status === TransactionStatus.COMPLETED) {
          console.log(`Transaction ${transactionId} is already completed, proceeding with success response`);
        } else {
          throw new Error(`Transaction ${transactionId} could not be updated to completed status. Current status: ${finalCheck.status}`);
        }
      }

      // Calculate fees and net amount to credit to wallet
      const transactionFee = amountInRupees * 0.02; // 2% transaction fee
      const gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
      const totalDeductions = transactionFee + gstOnFee;
      const netAmountToWallet = amountInRupees - totalDeductions; // Net amount to be credited to wallet

      console.log(`Fee calculation for transaction ${transactionId}:`, {
        grossAmount: amountInRupees,
        transactionFee,
        gstOnFee,
        totalDeductions,
        netAmountToWallet
      });

      // Update wallet balance with net amount (after deducting fees)
      const updatedWallet = await this.updateUserBalance(userId, netAmountToWallet, TransactionType.DEPOSIT);

      // Update transaction record with fee breakdown
      const existingMetadata = existingTransaction.metadata || {};
      const { error: feeUpdateError } = await supabase
        .from('wallet_transactions')
        .update({
          metadata: {
            ...existingMetadata,
            fee_breakdown: {
              gross_amount: amountInRupees,
              transaction_fee: transactionFee,
              gst_on_fee: gstOnFee,
              total_deductions: totalDeductions,
              net_amount_credited: netAmountToWallet
            }
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', transactionId);

      if (feeUpdateError) {
        console.warn(`Failed to update fee breakdown for transaction ${transactionId}:`, feeUpdateError);
        // Don't throw error as the main transaction is already processed
      }

      console.log(`Payment processed successfully for user ${userId}`, {
        transactionId,
        paymentId: paymentDetails.razorpay_payment_id,
        grossAmount: amountInRupees,
        netAmountCredited: netAmountToWallet,
        newBalance: updatedWallet.balance
      });

      return {
        transactionId,
        paymentId: paymentDetails.razorpay_payment_id,
        amount: amountInRupees, // Gross amount charged
        netAmountCredited: netAmountToWallet, // Net amount credited to wallet
        currency: paymentDetails.currency,
        newBalance: updatedWallet.balance,
        feeBreakdown: {
          transactionFee,
          gstOnFee,
          totalDeductions
        },
        status: 'completed'
      };
    } catch (error: any) {
      console.error(`Failed to process successful payment for user ${userId}:`, error);

      // Try fallback payment processing before marking as failed
      try {
        console.log('Attempting fallback payment processing...');

        // Check if transaction exists and get its details
        const { data: transaction, error: txError } = await supabase
          .from('wallet_transactions')
          .select('*')
          .eq('id', transactionId)
          .single();

        if (txError || !transaction) {
          throw new Error(`Transaction not found: ${txError?.message || 'Unknown error'}`);
        }

        // If transaction is already completed, return success
        if (transaction.status === TransactionStatus.COMPLETED) {
          console.log('Transaction already completed in fallback, returning success');
          return {
            success: true,
            transactionId,
            paymentId: paymentDetails.razorpay_payment_id,
            amount: parseFloat(transaction.amount),
            currency: transaction.currency,
            status: 'completed'
          };
        }

        // Force update the transaction to completed using a direct approach
        const { error: forceUpdateError } = await supabase
          .from('wallet_transactions')
          .update({
            status: TransactionStatus.COMPLETED,
            payment_gateway_response: {
              razorpay_order_id: paymentDetails.razorpay_order_id,
              razorpay_payment_id: paymentDetails.razorpay_payment_id,
              method: paymentDetails.method || 'upi',
              status: paymentDetails.status || 'captured',
              processed_at: new Date().toISOString(),
              fallback_processed: true
            },
            updated_at: new Date().toISOString()
          })
          .eq('id', transactionId);

        if (forceUpdateError) {
          console.error('Fallback transaction update failed:', forceUpdateError);
          throw error; // Re-throw original error to continue with failure handling
        }

        // Calculate fees for fallback processing
        const grossAmount = parseFloat(transaction.amount);
        const transactionFee = grossAmount * 0.02; // 2% transaction fee
        const gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
        const totalDeductions = transactionFee + gstOnFee;
        const netAmountToWallet = grossAmount - totalDeductions; // Net amount to be credited to wallet

        // Force update wallet balance using RPC function for atomic increment with net amount
        const { error: forceWalletError } = await supabase.rpc('increment_wallet_balance', {
          p_user_id: transaction.user_id,
          p_amount: netAmountToWallet
        });

        if (forceWalletError) {
          console.error('Fallback wallet update failed:', forceWalletError);
          throw error; // Re-throw original error to continue with failure handling
        }

        console.log('Fallback payment processing completed successfully');
        return {
          success: true,
          transactionId,
          paymentId: paymentDetails.razorpay_payment_id,
          amount: grossAmount, // Gross amount charged
          netAmountCredited: netAmountToWallet, // Net amount credited to wallet
          currency: transaction.currency,
          feeBreakdown: {
            transactionFee,
            gstOnFee,
            totalDeductions
          },
          status: 'completed'
        };

      } catch (fallbackError) {
        console.error('Fallback payment processing also failed:', fallbackError);
        // Continue with original failure handling
      }

      // Mark transaction as failed if processing fails (only if it exists and isn't already completed)
      try {
        await supabase
          .from('wallet_transactions')
          .update({
            status: TransactionStatus.FAILED,
            error_message: error.message,
            updated_at: new Date().toISOString()
          })
          .eq('id', transactionId)
          .neq('status', TransactionStatus.COMPLETED); // Don't mark completed transactions as failed
      } catch (updateError) {
        console.error(`Failed to update transaction status to failed:`, updateError);
      }

      throw new Error(`Payment processing failed: ${error.message}`);
    }
  },

  /**
   * Get all withdrawal requests for admin dashboard
   * @param page - Page number for pagination
   * @param limit - Number of items per page
   * @param status - Filter by status (optional)
   * @returns Paginated withdrawal requests with user details
   */
  async getWithdrawalRequests(page: number = 1, limit: number = 20, status?: string) {
    let query = supabase
      .from('withdrawal_requests_admin_view')
      .select('*', { count: 'exact' });

    if (status) {
      query = query.eq('status', status);
    }

    const { data: requests, error, count } = await query
      .order('requested_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (error) throw new Error(`Error fetching withdrawal requests: ${error.message}`);

    const totalPages = Math.ceil((count || 0) / limit);

    return {
      requests: requests || [],
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_items: count || 0,
        items_per_page: limit
      }
    };
  },

  /**
   * Get a specific withdrawal request by ID
   * @param requestId - Withdrawal request ID
   * @returns Withdrawal request details
   */
  async getWithdrawalRequest(requestId: string) {
    const { data: request, error } = await supabase
      .from('withdrawal_requests_admin_view')
      .select('*')
      .eq('id', requestId)
      .single();

    if (error) throw new Error(`Error fetching withdrawal request: ${error.message}`);
    if (!request) throw new Error('Withdrawal request not found');

    return request;
  },

  /**
   * Approve a withdrawal request
   * @param requestId - Withdrawal request ID
   * @param adminId - Admin user ID
   * @param adminNotes - Optional admin notes
   * @returns Updated withdrawal request
   */
  async approveWithdrawalRequest(requestId: string, adminId: string, adminNotes?: string) {
    // First get the withdrawal request to validate it exists and is pending
    const { data: request, error: fetchError } = await supabase
      .from('withdrawal_requests')
      .select('*')
      .eq('id', requestId)
      .eq('status', 'pending')
      .single();

    if (fetchError) throw new Error(`Error fetching withdrawal request: ${fetchError.message}`);
    if (!request) throw new Error('Withdrawal request not found or not in pending status');

    // Update withdrawal request status to approved
    const { data: updatedRequest, error: updateError } = await supabase
      .from('withdrawal_requests')
      .update({
        status: 'approved',
        admin_id: adminId,
        admin_notes: adminNotes,
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId)
      .select()
      .single();

    if (updateError) throw new Error(`Error approving withdrawal request: ${updateError.message}`);

    // Update the corresponding transaction status
    await supabase
      .from('wallet_transactions')
      .update({
        status: TransactionStatus.PROCESSING,
        updated_at: new Date().toISOString()
      })
      .eq('id', request.transaction_id);

    // Create notification for user
    await this.createNotification(
      request.user_id,
      'Withdrawal Approved',
      `Your withdrawal request of ₹${request.amount} has been approved and is being processed.`,
      Severity.INFO,
      {
        withdrawal_request_id: requestId,
        transaction_id: request.transaction_id,
        amount: request.amount
      }
    );

    return updatedRequest;
  },

  /**
   * Reject a withdrawal request
   * @param requestId - Withdrawal request ID
   * @param adminId - Admin user ID
   * @param adminNotes - Reason for rejection
   * @returns Updated withdrawal request
   */
  async rejectWithdrawalRequest(requestId: string, adminId: string, adminNotes: string) {
    // First get the withdrawal request to validate it exists and is pending
    const { data: request, error: fetchError } = await supabase
      .from('withdrawal_requests')
      .select('*')
      .eq('id', requestId)
      .eq('status', 'pending')
      .single();

    if (fetchError) throw new Error(`Error fetching withdrawal request: ${fetchError.message}`);
    if (!request) throw new Error('Withdrawal request not found or not in pending status');

    // Update withdrawal request status to rejected
    const { data: updatedRequest, error: updateError } = await supabase
      .from('withdrawal_requests')
      .update({
        status: 'rejected',
        admin_id: adminId,
        admin_notes: adminNotes,
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId)
      .select()
      .single();

    if (updateError) throw new Error(`Error rejecting withdrawal request: ${updateError.message}`);

    // Update the corresponding transaction status to failed
    await supabase
      .from('wallet_transactions')
      .update({
        status: TransactionStatus.FAILED,
        updated_at: new Date().toISOString()
      })
      .eq('id', request.transaction_id);

    // Refund the amount to the user's balance
    await this.updateUserBalance(request.user_id, request.amount, TransactionType.REFUND);

    // Create notification for user
    await this.createNotification(
      request.user_id,
      'Withdrawal Rejected',
      `Your withdrawal request of ₹${request.amount} has been rejected. The amount has been refunded to your wallet. Reason: ${adminNotes}`,
      Severity.WARNING,
      {
        withdrawal_request_id: requestId,
        transaction_id: request.transaction_id,
        amount: request.amount,
        reason: adminNotes
      }
    );

    return updatedRequest;
  },

  /**
   * Mark a withdrawal request as completed (money transferred)
   * @param requestId - Withdrawal request ID
   * @param adminId - Admin user ID
   * @param adminNotes - Optional completion notes
   * @returns Updated withdrawal request
   */
  async completeWithdrawalRequest(requestId: string, adminId: string, adminNotes?: string) {
    // First get the withdrawal request to validate it exists and is approved
    const { data: request, error: fetchError } = await supabase
      .from('withdrawal_requests')
      .select('*')
      .eq('id', requestId)
      .eq('status', 'approved')
      .single();

    if (fetchError) throw new Error(`Error fetching withdrawal request: ${fetchError.message}`);
    if (!request) throw new Error('Withdrawal request not found or not in approved status');

    // Update withdrawal request status to completed
    const { data: updatedRequest, error: updateError } = await supabase
      .from('withdrawal_requests')
      .update({
        status: 'completed',
        admin_id: adminId,
        admin_notes: adminNotes || request.admin_notes,
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId)
      .select()
      .single();

    if (updateError) throw new Error(`Error completing withdrawal request: ${updateError.message}`);

    // Update the corresponding transaction status to completed
    await supabase
      .from('wallet_transactions')
      .update({
        status: TransactionStatus.COMPLETED,
        updated_at: new Date().toISOString()
      })
      .eq('id', request.transaction_id);

    // Create notification for user
    await this.createNotification(
      request.user_id,
      'Withdrawal Completed',
      `Your withdrawal of ₹${request.amount} has been successfully transferred to your bank account.`,
      Severity.INFO,
      {
        withdrawal_request_id: requestId,
        transaction_id: request.transaction_id,
        amount: request.amount
      }
    );

    return updatedRequest;
  }
};
