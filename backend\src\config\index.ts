import dotenv from 'dotenv';
dotenv.config();

// Validate required environment variables
function validateEnv() {
  const requiredVars = [
    'SUPABASE_URL',
    'SUPABASE_KEY'
  ];
  
  // JWT secrets are no longer required as we use RS256 with key files
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    // Log warning in development, throw error in production
    const message = `Missing required environment variables: ${missingVars.join(', ')}`;
    if (process.env.NODE_ENV === 'production') {
      throw new Error(message);
    } else {
      console.warn(`⚠️ WARNING: ${message}`);
      console.warn('Application may not function correctly without these variables.');
    }
  }
}

// Call validation on startup
validateEnv();

export const config = {
  server: {
    port: process.env.PORT || 5000, // Using default port 5000 as specified in project rules
    env: process.env.NODE_ENV || 'development',
    rateLimit: {
      windowMs: process.env.RATE_LIMIT_WINDOW_MS ? parseInt(process.env.RATE_LIMIT_WINDOW_MS) : 15 * 60 * 1000, // 15 minutes
      max: process.env.RATE_LIMIT_MAX ? parseInt(process.env.RATE_LIMIT_MAX) : 100 // 100 requests per windowMs
    },
    jwks: {
      path: '/.well-known/jwks.json',
      cacheMaxAge: 24 * 60 * 60 // 24 hours in seconds
    }
  },
  jwt: {
    // Legacy HS256 secrets for backward compatibility
    // These will be phased out in favor of RS256
    // accessSecret: process.env.JWT_ACCESS_SECRET || 'legacy-access-secret',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'legacy-refresh-secret',
    // Updated to use RS256
    algorithm: 'RS256',
    accessExpiry: process.env.JWT_ACCESS_EXPIRY || '15m',
    refreshExpiry: process.env.JWT_REFRESH_EXPIRY || '7d',
    keyId: 'wiggyz-auth-key-1',
    issuer: process.env.JWT_ISSUER || 'wiggyz-auth'
  },
  supabase: {
    url: process.env.SUPABASE_URL!,
    key: process.env.SUPABASE_KEY!,
    timeout: process.env.SUPABASE_TIMEOUT ? parseInt(process.env.SUPABASE_TIMEOUT) : 10000
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info'
  }
};
