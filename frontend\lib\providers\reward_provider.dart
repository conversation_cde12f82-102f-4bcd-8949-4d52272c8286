/// Comprehensive reward state management provider for the WiggyZ app
/// Manages daily rewards, achievements, loyalty status, and reward claiming
library;

import 'package:flutter/foundation.dart';
import '../models/reward_models.dart';
import '../models/daily_reward_models.dart';
import '../models/achievement_models.dart';
import '../models/loyalty_models.dart';
import '../services/reward_service.dart';
import '../services/reward_notification_service.dart';
import '../utils/reward_cache_manager.dart';
import '../utils/reward_edge_case_handler.dart';
import '../utils/reward_error_handler.dart';
import '../utils/offline_reward_manager.dart';
import '../utils/reward_performance_monitor.dart';
import '../services/auth_service.dart';

/// Provider class for managing all reward-related state
class RewardProvider extends ChangeNotifier {
  final RewardService _rewardService;
  final RewardNotificationService _notificationService = RewardNotificationService();
  final RewardCacheManager _cacheManager = RewardCacheManager();
  final OfflineRewardManager _offlineManager = OfflineRewardManager();
  final RewardPerformanceMonitor _performanceMonitor = RewardPerformanceMonitor();
  final RewardMemoryManager _memoryManager = RewardMemoryManager();
  
  // ==================== STATE VARIABLES ====================
  
  // Daily Rewards State
  DailyRewardStatusModel? _dailyRewardStatus;
  DailyRewardsCalendar? _dailyRewardsCalendar;
  bool _isDailyRewardsLoading = false;
  String? _dailyRewardsError;
  
  // General Rewards State
  List<UserRewardModel> _userRewards = [];
  List<RewardTransactionModel> _rewardTransactions = [];
  bool _isRewardsLoading = false;
  String? _rewardsError;
  
  // Achievements State
  List<AchievementModel> _achievements = [];
  bool _isAchievementsLoading = false;
  String? _achievementsError;
  
  // Loyalty State
  UserLoyaltyModel? _loyaltyStatus;
  List<LoyaltyTierModel> _loyaltyTiers = [];
  List<LoyaltyTransactionModel> _loyaltyHistory = [];
  bool _isLoyaltyLoading = false;
  String? _loyaltyError;
  
  // General State
  bool _isInitialized = false;
  DateTime? _lastRefresh;

  // ==================== CONSTRUCTOR ====================
  
  RewardProvider(AuthService authService) : _rewardService = RewardService(authService) {
    _initialize();
  }

  // ==================== GETTERS ====================
  
  // Daily Rewards Getters
  DailyRewardStatusModel? get dailyRewardStatus => _dailyRewardStatus;
  DailyRewardsCalendar? get dailyRewardsCalendar => _dailyRewardsCalendar;
  bool get isDailyRewardsLoading => _isDailyRewardsLoading;
  String? get dailyRewardsError => _dailyRewardsError;
  bool get canClaimDailyReward => _dailyRewardsCalendar?.canClaimToday ?? false;
  
  // General Rewards Getters
  List<UserRewardModel> get userRewards => _userRewards;
  List<RewardTransactionModel> get rewardTransactions => _rewardTransactions;
  bool get isRewardsLoading => _isRewardsLoading;
  String? get rewardsError => _rewardsError;
  
  // Achievements Getters
  List<AchievementModel> get achievements => _achievements;
  bool get isAchievementsLoading => _isAchievementsLoading;
  String? get achievementsError => _achievementsError;
  List<AchievementModel> get completedAchievements => 
      _achievements.where((a) => a.isCompleted).toList();
  List<AchievementModel> get inProgressAchievements => 
      _achievements.where((a) => !a.isCompleted).toList();
  List<AchievementModel> get readyToClaimAchievements => 
      _achievements.where((a) => a.isReadyToClaim).toList();
  
  // Loyalty Getters
  UserLoyaltyModel? get loyaltyStatus => _loyaltyStatus;
  List<LoyaltyTierModel> get loyaltyTiers => _loyaltyTiers;
  List<LoyaltyTransactionModel> get loyaltyHistory => _loyaltyHistory;
  bool get isLoyaltyLoading => _isLoyaltyLoading;
  String? get loyaltyError => _loyaltyError;
  
  // General Getters
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isDailyRewardsLoading || _isRewardsLoading || 
                       _isAchievementsLoading || _isLoyaltyLoading;
  DateTime? get lastRefresh => _lastRefresh;

  // ==================== INITIALIZATION ====================
  
  /// Initialize the reward provider
  Future<void> _initialize() async {
    if (_isInitialized) return;

    // Initialize cache manager and offline manager
    await _cacheManager.initialize();
    await _offlineManager.initialize();

    // Start performance monitoring
    _memoryManager.startMonitoring();

    // Load cached data first for immediate UI update
    _loadCachedData();

    // Then refresh from API
    await refreshAllData();
    _isInitialized = true;
    notifyListeners();
  }

  /// Load cached data for immediate UI response
  void _loadCachedData() {
    // Load cached daily rewards
    final cachedDailyRewards = _cacheManager.getCachedDailyRewardStatus();
    if (cachedDailyRewards != null) {
      _dailyRewardStatus = cachedDailyRewards;
      _dailyRewardsCalendar = DailyRewardsCalendar.fromStatus(cachedDailyRewards);
    }

    // Load cached achievements
    final cachedAchievements = _cacheManager.getCachedAchievements();
    if (cachedAchievements != null) {
      _achievements = cachedAchievements;
    }

    // Load cached loyalty status
    final cachedLoyaltyStatus = _cacheManager.getCachedLoyaltyStatus();
    if (cachedLoyaltyStatus != null) {
      _loyaltyStatus = cachedLoyaltyStatus;
    }

    // Load cached loyalty tiers
    final cachedLoyaltyTiers = _cacheManager.getCachedLoyaltyTiers();
    if (cachedLoyaltyTiers != null) {
      _loyaltyTiers = cachedLoyaltyTiers;
    }

    // Load cached user rewards
    final cachedUserRewards = _cacheManager.getCachedUserRewards();
    if (cachedUserRewards != null) {
      _userRewards = cachedUserRewards;
    }

    // Notify listeners if we have any cached data
    if (cachedDailyRewards != null || cachedAchievements != null ||
        cachedLoyaltyStatus != null || cachedUserRewards != null) {
      notifyListeners();
    }
  }

  /// Refresh all reward data
  Future<void> refreshAllData() async {
    await Future.wait([
      fetchDailyRewardStatus(),
      fetchUserRewards(),
      fetchAchievements(),
      fetchLoyaltyStatus(),
    ]);
    
    _lastRefresh = DateTime.now();
    notifyListeners();
  }

  // ==================== DAILY REWARDS METHODS ====================
  
  /// Fetch daily reward status
  Future<void> fetchDailyRewardStatus() async {
    debugPrint('🎁 Starting fetchDailyRewardStatus...');
    _performanceMonitor.startOperation('fetchDailyRewardStatus');

    _isDailyRewardsLoading = true;
    _dailyRewardsError = null;
    notifyListeners();

    try {
      debugPrint('📡 Calling reward service getDailyRewardStatus...');
      final status = await _rewardService.getDailyRewardStatus();
      debugPrint('📊 Received status: ${status != null}');

      if (status != null) {
        debugPrint('🔍 Status data - Streak: ${status.streak.currentStreak}, Rewards: ${status.rewards.length}, Claimed: ${status.hasClaimedToday}');
        debugPrint('🔍 Raw backend response - hasClaimedToday: ${status.hasClaimedToday}, canClaimToday: ${status.canClaimToday}');

        // Handle edge cases
        final validatedStatus = RewardEdgeCaseHandler.handleExpiredDailyRewards(status);
        debugPrint('✅ Status validated: ${validatedStatus != null}');

        _dailyRewardStatus = validatedStatus;

        // Try to create calendar
        try {
          _dailyRewardsCalendar = validatedStatus != null
              ? DailyRewardsCalendar.fromStatus(validatedStatus)
              : null;
          debugPrint('📅 Calendar created: ${_dailyRewardsCalendar != null}');
          if (_dailyRewardsCalendar != null) {
            debugPrint('📊 Calendar days: ${_dailyRewardsCalendar!.days.length}');
            debugPrint('📊 Calendar can claim today: ${_dailyRewardsCalendar!.canClaimToday}');
            debugPrint('📊 Calendar hasClaimedToday: ${_dailyRewardsCalendar!.hasClaimedToday}');
            if (_dailyRewardsCalendar!.days.isEmpty) {
              debugPrint('⚠️ Calendar has no days - no rewards configured');
              _dailyRewardsError = 'No daily rewards are currently configured. Please contact support.';
              _performanceMonitor.endOperation('fetchDailyRewardStatus', success: false, error: 'No rewards configured');
              return;
            }
          }
        } catch (calendarError) {
          debugPrint('❌ Error creating calendar: $calendarError');
          _dailyRewardsError = 'Error processing daily rewards data. Please try again later.';
          _performanceMonitor.endOperation('fetchDailyRewardStatus', success: false, error: calendarError.toString());
          return;
        }

        _dailyRewardsError = null;

        // Cache the data
        if (validatedStatus != null) {
          await _cacheManager.cacheDailyRewardStatus(validatedStatus);
          debugPrint('💾 Data cached successfully');
        }

        _performanceMonitor.endOperation('fetchDailyRewardStatus', success: true);
        debugPrint('✅ fetchDailyRewardStatus completed successfully');
      } else {
        debugPrint('❌ No status data returned from service');
        _dailyRewardsError = 'Failed to load daily rewards';
        _performanceMonitor.endOperation('fetchDailyRewardStatus', success: false, error: 'No data returned');
      }
    } catch (e) {
      debugPrint('💥 Exception in fetchDailyRewardStatus: $e');
      final error = RewardErrorHandler.handleError(e, context: 'fetchDailyRewardStatus');
      _dailyRewardsError = error.type.userFriendlyMessage;
      _performanceMonitor.endOperation('fetchDailyRewardStatus', success: false, error: e.toString());
      debugPrint('Error fetching daily reward status: $e');
    } finally {
      _isDailyRewardsLoading = false;
      notifyListeners();
      debugPrint('🏁 fetchDailyRewardStatus finished - Error: $_dailyRewardsError');
    }
  }

  /// Claim daily reward
  Future<bool> claimDailyReward() async {
    debugPrint('🎁 [PROVIDER] Starting claimDailyReward...');

    // Clear cache and refresh status before claiming to ensure we have the latest data
    debugPrint('🔄 [PROVIDER] Clearing cache and refreshing status before claim...');
    await _cacheManager.clearDailyRewardStatusCache();
    await fetchDailyRewardStatus();

    debugPrint('🔍 [PROVIDER] Can claim check: $canClaimDailyReward');

    if (!canClaimDailyReward) {
      debugPrint('❌ [PROVIDER] Cannot claim daily reward - conditions not met');
      debugPrint('📊 [PROVIDER] Calendar exists: ${_dailyRewardsCalendar != null}');
      debugPrint('📊 [PROVIDER] Can claim today: ${_dailyRewardsCalendar?.canClaimToday ?? false}');
      return false;
    }

    try {
      final idempotencyKey = 'daily_${DateTime.now().millisecondsSinceEpoch}';
      debugPrint('🔑 [PROVIDER] Using idempotency key: $idempotencyKey');

      debugPrint('📡 [PROVIDER] About to call reward service claimDailyReward...');
      debugPrint('🔧 [PROVIDER] Reward service instance: ${_rewardService.runtimeType}');

      final result = await _rewardService.claimDailyReward(idempotencyKey: idempotencyKey);
      debugPrint('📊 [PROVIDER] Claim result received: success=${result.success}, error=${result.error}');

      if (result.success) {
        debugPrint('✅ Daily reward claimed successfully');

        // Send notification about successful claim
        _notificationService.notifyDailyRewardClaimed(result);

        // Check for streak milestones
        if (result.updatedStreak != null) {
          debugPrint('🔥 Streak milestone: ${result.updatedStreak!.currentStreak}');
          _notificationService.notifyStreakMilestone(result.updatedStreak!.currentStreak);
        }

        // Clear cache and refresh daily reward status to update UI
        debugPrint('🔄 Clearing cache and refreshing daily reward status...');
        await _cacheManager.clearDailyRewardStatusCache();
        await fetchDailyRewardStatus();
        return true;
      } else {
        debugPrint('❌ Daily reward claim failed: ${result.error}');
        _dailyRewardsError = result.error ?? 'Failed to claim daily reward';
        notifyListeners();
        return false;
      }
    } catch (e, stackTrace) {
      debugPrint('💥 [PROVIDER] Exception in claimDailyReward: $e');
      debugPrint('📚 [PROVIDER] Exception type: ${e.runtimeType}');
      debugPrint('📚 [PROVIDER] Stack trace: $stackTrace');
      _dailyRewardsError = 'Error claiming daily reward: $e';
      notifyListeners();
      return false;
    }
  }

  // ==================== GENERAL REWARDS METHODS ====================
  
  /// Fetch user rewards
  Future<void> fetchUserRewards() async {
    _isRewardsLoading = true;
    _rewardsError = null;
    notifyListeners();

    try {
      final rewards = await _rewardService.getUserRewards();

      // Use performance optimizer to check if data actually changed
      final hasChanges = RewardPerformanceOptimizer.updateListIfChanged(_userRewards, rewards);

      if (hasChanges) {
        // Cache the new data
        await _cacheManager.cacheUserRewards(_userRewards);

        // Check for expiring rewards
        _notificationService.checkExpiringRewards(_userRewards);
      }

      _rewardsError = null;
    } catch (e) {
      final error = RewardErrorHandler.handleError(e, context: 'fetchUserRewards');
      _rewardsError = error.type.userFriendlyMessage;

      // If it's a network error and we have cached data, continue with cached data
      if (error.type == RewardErrorType.networkError && _userRewards.isNotEmpty) {
        _rewardsError = 'Using offline data. ${error.type.userFriendlyMessage}';
      }

      debugPrint('Error fetching user rewards: $e');
    } finally {
      _isRewardsLoading = false;
      notifyListeners();
    }
  }

  /// Fetch reward transactions
  Future<void> fetchRewardTransactions({int limit = 20, int offset = 0}) async {
    try {
      final transactions = await _rewardService.getRewardTransactions(
        limit: limit,
        offset: offset,
      );
      
      if (offset == 0) {
        _rewardTransactions = transactions;
      } else {
        _rewardTransactions.addAll(transactions);
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error fetching reward transactions: $e');
    }
  }

  /// Claim a specific reward
  Future<bool> claimReward(String rewardId) async {
    try {
      final idempotencyKey = 'reward_${rewardId}_${DateTime.now().millisecondsSinceEpoch}';
      final result = await _rewardService.claimReward(rewardId, idempotencyKey: idempotencyKey);
      
      if (result['success'] == true) {
        // Refresh user rewards to update UI
        await fetchUserRewards();
        return true;
      } else {
        _rewardsError = result['message'] ?? 'Failed to claim reward';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _rewardsError = 'Error claiming reward: $e';
      notifyListeners();
      return false;
    }
  }

  // ==================== ACHIEVEMENTS METHODS ====================
  
  /// Fetch user achievements
  Future<void> fetchAchievements() async {
    _performanceMonitor.startOperation('fetchAchievements');

    _isAchievementsLoading = true;
    _achievementsError = null;
    notifyListeners();

    try {
      final achievements = await _rewardService.getUserAchievements();

      // Validate achievement progress for edge cases
      final validatedAchievements = RewardEdgeCaseHandler.validateAchievementProgress(achievements);

      // Check for newly completed achievements
      final previouslyCompleted = _achievements.where((a) => a.isCompleted).map((a) => a.id).toSet();
      final newlyCompleted = validatedAchievements.where((a) => a.isCompleted && !previouslyCompleted.contains(a.id));

      // Update achievements list efficiently
      final hasChanges = RewardPerformanceOptimizer.updateListIfChanged(_achievements, validatedAchievements);

      if (hasChanges) {
        // Optimize memory usage
        _memoryManager.optimizeRewardData(_achievements, maxItems: 50);

        // Cache the new data
        await _cacheManager.cacheAchievements(_achievements);

        // Send notifications for newly completed achievements
        for (final achievement in newlyCompleted) {
          _notificationService.notifyAchievementUnlocked(achievement);
          if (achievement.isReadyToClaim) {
            _notificationService.notifyAchievementReadyToClaim(achievement);
          }
        }
      }

      _achievementsError = null;
      _performanceMonitor.endOperation('fetchAchievements', success: true, metadata: {
        'achievementCount': _achievements.length,
        'newlyCompleted': newlyCompleted.length,
      });
    } catch (e) {
      final error = RewardErrorHandler.handleError(e, context: 'fetchAchievements');
      _achievementsError = error.type.userFriendlyMessage;

      // If it's a network error and we have cached data, continue with cached data
      if (error.type == RewardErrorType.networkError && _achievements.isNotEmpty) {
        _achievementsError = 'Using offline data. ${error.type.userFriendlyMessage}';
      }

      _performanceMonitor.endOperation('fetchAchievements', success: false, error: e.toString());
      debugPrint('Error fetching achievements: $e');
    } finally {
      _isAchievementsLoading = false;
      notifyListeners();
    }
  }

  /// Update achievement progress
  Future<bool> updateAchievementProgress(
    String achievementId,
    int progressIncrement, {
    String? description,
  }) async {
    try {
      final result = await _rewardService.updateAchievementProgress(
        achievementId,
        progressIncrement,
        description: description,
      );

      if (result['success'] == true) {
        // Update local achievement data
        final achievementIndex = _achievements.indexWhere((a) => a.id == achievementId);
        if (achievementIndex != -1) {
          final achievement = _achievements[achievementIndex];
          final previousProgress = achievement.current;
          final newCurrent = achievement.current + progressIncrement;
          final isCompleted = newCurrent >= achievement.target;
          final wasCompleted = achievement.isCompleted;

          _achievements[achievementIndex] = achievement.copyWith(
            current: newCurrent,
            isCompleted: isCompleted,
            completedAt: isCompleted ? DateTime.now() : null,
          );

          // Send notifications for progress milestones
          _notificationService.notifyAchievementProgress(
            _achievements[achievementIndex],
            previousProgress
          );

          // Send notification if achievement was just completed
          if (isCompleted && !wasCompleted) {
            _notificationService.notifyAchievementUnlocked(_achievements[achievementIndex]);

            // Check if ready to claim
            if (_achievements[achievementIndex].isReadyToClaim) {
              _notificationService.notifyAchievementReadyToClaim(_achievements[achievementIndex]);
            }
          }

          notifyListeners();
        }

        return true;
      } else {
        _achievementsError = result['message'] ?? 'Failed to update achievement progress';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _achievementsError = 'Error updating achievement progress: $e';
      notifyListeners();
      return false;
    }
  }

  // ==================== LOYALTY METHODS ====================
  
  /// Fetch loyalty status
  Future<void> fetchLoyaltyStatus() async {
    _isLoyaltyLoading = true;
    _loyaltyError = null;
    notifyListeners();

    try {
      final futures = await Future.wait([
        _rewardService.getLoyaltyStatus(),
        _rewardService.getLoyaltyTiers(),
      ]);
      
      _loyaltyStatus = futures[0] as UserLoyaltyModel?;
      _loyaltyTiers = futures[1] as List<LoyaltyTierModel>;
      _loyaltyError = null;
    } catch (e) {
      _loyaltyError = 'Error loading loyalty status: $e';
      debugPrint('Error fetching loyalty status: $e');
    } finally {
      _isLoyaltyLoading = false;
      notifyListeners();
    }
  }

  /// Fetch loyalty history
  Future<void> fetchLoyaltyHistory({int limit = 20, int offset = 0}) async {
    try {
      final history = await _rewardService.getLoyaltyHistory(
        limit: limit,
        offset: offset,
      );
      
      if (offset == 0) {
        _loyaltyHistory = history;
      } else {
        _loyaltyHistory.addAll(history);
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error fetching loyalty history: $e');
    }
  }

  // ==================== UTILITY METHODS ====================
  
  /// Clear all errors
  void clearErrors() {
    _dailyRewardsError = null;
    _rewardsError = null;
    _achievementsError = null;
    _loyaltyError = null;
    notifyListeners();
  }

  /// Reset provider state
  void reset() {
    _dailyRewardStatus = null;
    _dailyRewardsCalendar = null;
    _userRewards.clear();
    _rewardTransactions.clear();
    _achievements.clear();
    _loyaltyStatus = null;
    _loyaltyTiers.clear();
    _loyaltyHistory.clear();
    _isInitialized = false;
    _lastRefresh = null;
    clearErrors();
    notifyListeners();
  }

  /// Get performance metrics
  Map<String, dynamic> getPerformanceMetrics() {
    return _performanceMonitor.getPerformanceSummary();
  }

  /// Check system health
  bool get isSystemHealthy {
    return _performanceMonitor.isOperationHealthy('fetchDailyRewardStatus') &&
           _performanceMonitor.isOperationHealthy('fetchAchievements') &&
           _performanceMonitor.isOperationHealthy('fetchLoyaltyStatus');
  }

  /// Cleanup resources
  @override
  void dispose() {
    _memoryManager.stopMonitoring();
    RewardPerformanceUtils.cleanup();
    super.dispose();
  }
}
