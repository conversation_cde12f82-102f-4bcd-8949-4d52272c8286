import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../features/wallet/providers/wallet_provider.dart';

class TransactionHistoryScreen extends StatefulWidget {
  const TransactionHistoryScreen({super.key});

  @override
  State<TransactionHistoryScreen> createState() => _TransactionHistoryScreenState();
}

class _TransactionHistoryScreenState extends State<TransactionHistoryScreen> {
  String _selectedFilter = 'All';
  String _selectedDateRange = 'All Time';
  final List<String> _filters = ['All', 'Credit', 'Debit'];
  final List<String> _dateRanges = ['All Time', 'This Month', 'Last Month', 'Last 3 Months'];

  @override
  void initState() {
    super.initState();
    // Fetch transaction history when the screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final walletProvider = Provider.of<WalletProvider>(context, listen: false);
      walletProvider.fetchWalletTransactions(page: 1, limit: 50);
    });
  }

  // Convert API transaction data to display format
  List<TransactionModel> _convertApiTransactions(List<dynamic> apiTransactions) {
    return apiTransactions.map((transaction) {
      final Map<String, dynamic> txData = transaction as Map<String, dynamic>;

      // Determine transaction type and formatting
      final String type = txData['type'] ?? 'unknown';
      final double amount = double.tryParse(txData['amount']?.toString() ?? '0') ?? 0;
      final bool isDebit = amount < 0 || type.contains('purchase') || type.contains('fee');

      // Format amount with currency symbol
      final String formattedAmount = isDebit
          ? '-₹${amount.abs().toStringAsFixed(2)}'
          : '+₹${amount.toStringAsFixed(2)}';

      // Format date and time
      final DateTime? createdAt = DateTime.tryParse(txData['created_at'] ?? '');
      final String formattedDate = createdAt != null
          ? DateFormat('MMM dd, yyyy').format(createdAt)
          : 'Unknown Date';
      final String formattedTime = createdAt != null
          ? DateFormat('HH:mm').format(createdAt)
          : 'Unknown Time';

      // Determine title and category based on transaction type
      String title;
      String category;
      IconData iconData;

      switch (type.toLowerCase()) {
        case 'deposit':
          title = 'Added Funds';
          category = 'Deposit';
          iconData = Icons.account_balance;
          break;
        case 'purchase':
        case 'tournament_fee':
        case 'tournament_creation':
          title = 'Tournament Entry';
          category = 'Tournament';
          iconData = Icons.videogame_asset;
          break;
        case 'withdrawal':
          // Check if this is a manual withdrawal request based on metadata
          final metadata = txData['metadata'] as Map<String, dynamic>?;
          final isManualWithdrawal = metadata?['manual_processing'] == true;

          if (isManualWithdrawal) {
            title = 'Withdrawal Request';
            category = 'Withdrawal';
          } else {
            title = 'Withdrawal';
            category = 'Withdrawal';
          }
          iconData = Icons.money_off;
          break;
        case 'reward':
        case 'prize':
          title = 'Tournament Winnings';
          category = 'Prize';
          iconData = Icons.emoji_events;
          break;
        default:
          title = txData['description'] ?? 'Transaction';
          category = 'Other';
          iconData = Icons.receipt;
      }

      return TransactionModel(
        id: txData['id'] ?? 'unknown',
        title: title,
        amount: formattedAmount,
        date: formattedDate,
        time: formattedTime,
        isDebit: isDebit,
        category: category,
        iconData: iconData,
        status: _formatStatus(txData['status'] ?? 'unknown'),
      );
    }).toList();
  }

  String _formatStatus(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'Completed';
      case 'pending':
        return 'Pending';
      case 'processing':
        return 'Processing';
      case 'failed':
        return 'Failed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'failed':
        return Colors.red;
      case 'cancelled':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }



  List<TransactionModel> getFilteredTransactions(WalletProvider walletProvider) {
    // Convert API transactions to display format
    List<TransactionModel> allTransactions = _convertApiTransactions(walletProvider.transactions);

    return allTransactions.where((transaction) {
      // Apply category filter
      if (_selectedFilter == 'Credit' && transaction.isDebit) {
        return false;
      }
      if (_selectedFilter == 'Debit' && !transaction.isDebit) {
        return false;
      }

      // Apply date filter
      if (_selectedDateRange == 'This Month') {
        final now = DateTime.now();
        final transactionDate = _parseDate(transaction.date);
        return transactionDate.month == now.month && transactionDate.year == now.year;
      } else if (_selectedDateRange == 'Last Month') {
        final now = DateTime.now();
        final lastMonth = DateTime(now.year, now.month - 1);
        final transactionDate = _parseDate(transaction.date);
        return transactionDate.month == lastMonth.month && transactionDate.year == lastMonth.year;
      } else if (_selectedDateRange == 'Last 3 Months') {
        final now = DateTime.now();
        final threeMonthsAgo = DateTime(now.year, now.month - 3);
        final transactionDate = _parseDate(transaction.date);
        return transactionDate.isAfter(threeMonthsAgo) || transactionDate.isAtSameMomentAs(threeMonthsAgo);
      }

      return true;
    }).toList();
  }
  
  DateTime _parseDate(String date) {
    final parts = date.split(' ');
    final monthMap = {
      'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
      'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12,
    };
    
    final month = monthMap[parts[0]] ?? 1;
    final day = int.parse(parts[1].replaceAll(',', ''));
    final year = int.parse(parts[2]);
    
    return DateTime(year, month, day);
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Consumer<WalletProvider>(
      builder: (context, walletProvider, child) {
        final filteredTransactions = getFilteredTransactions(walletProvider);

        return Scaffold(
          backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.grey[100],
      appBar: AppBar(
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFFFFCC00),
                Color(0xFFFF9500),
              ],
            ),
          ),
        ),
        backgroundColor: Colors.transparent,
        centerTitle: true,
        title: Text(
          'Transaction History',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.download_outlined, color: Colors.black),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Statement download started'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilters(isDarkMode, filteredTransactions.length),
          Expanded(
            child: walletProvider.isLoading && walletProvider.transactions.isEmpty
                ? _buildLoadingState(isDarkMode)
                : walletProvider.errorMessage != null && walletProvider.transactions.isEmpty
                ? _buildErrorState(isDarkMode, walletProvider.errorMessage!)
                : filteredTransactions.isEmpty
                ? _buildEmptyState(isDarkMode)
                : _buildTransactionsList(isDarkMode, filteredTransactions),
          ),
        ],
      ),
        );
      },
    );
  }

  Widget _buildFilters(bool isDarkMode, int transactionCount) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Filter By',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildDropdown(
                      isDarkMode,
                      _selectedFilter,
                      _filters,
                      (value) {
                        setState(() {
                          _selectedFilter = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Date Range',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildDropdown(
                      isDarkMode,
                      _selectedDateRange,
                      _dateRanges,
                      (value) {
                        setState(() {
                          _selectedDateRange = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              'Showing $transactionCount transactions',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdown(
    bool isDarkMode,
    String value,
    List<String> items,
    void Function(String?) onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
        ),
      ),
      child: DropdownButton<String>(
        value: value,
        isExpanded: true,
        underline: const SizedBox(),
        dropdownColor: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
        style: GoogleFonts.poppins(
          fontSize: 14,
          color: isDarkMode ? Colors.white : Colors.black,
        ),
        icon: Icon(
          Icons.arrow_drop_down,
          color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
        ),
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(item),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: Color(0xFFFFCC00),
          ),
          const SizedBox(height: 24),
          Text(
            'Loading Transactions...',
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(bool isDarkMode, String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: isDarkMode ? Colors.red[400] : Colors.red[600],
          ),
          const SizedBox(height: 24),
          Text(
            'Error Loading Transactions',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            errorMessage,
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[500] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () {
              final walletProvider = Provider.of<WalletProvider>(context, listen: false);
              walletProvider.fetchWalletTransactions(page: 1, limit: 50);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFFCC00),
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Retry',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: isDarkMode ? Colors.grey[700] : Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No transactions found',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try changing your filters',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(bool isDarkMode, List<TransactionModel> transactions) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: transactions.length,
      separatorBuilder: (context, index) => Divider(
        color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
        height: 1,
      ),
      itemBuilder: (context, index) {
        final transaction = transactions[index];
        return _buildTransactionItem(transaction, isDarkMode);
      },
    );
  }

  Widget _buildTransactionItem(TransactionModel transaction, bool isDarkMode) {
    return InkWell(
      onTap: () => _showTransactionDetails(transaction, isDarkMode),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: transaction.isDebit
                    ? Colors.red.withOpacity(0.1)
                    : Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                transaction.iconData,
                color: transaction.isDebit ? Colors.red : Colors.green,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.title,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? Colors.grey[800]
                              : Colors.grey[200],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          transaction.category,
                          style: GoogleFonts.poppins(
                            fontSize: 10,
                            color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${transaction.date} · ${transaction.time}',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Text(
              transaction.amount,
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: transaction.isDebit
                    ? Colors.red
                    : Colors.green,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showTransactionDetails(TransactionModel transaction, bool isDarkMode) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 5,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey[700] : Colors.grey[300],
                borderRadius: BorderRadius.circular(5),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'Transaction Details',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: transaction.isDebit
                          ? Colors.red.withOpacity(0.1)
                          : Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      transaction.iconData,
                      color: transaction.isDebit ? Colors.red : Colors.green,
                      size: 48,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    transaction.amount,
                    style: GoogleFonts.poppins(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: transaction.isDebit ? Colors.red : Colors.green,
                    ),
                  ),
                  Text(
                    transaction.title,
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(transaction.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      transaction.status,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: _getStatusColor(transaction.status),
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),
                  _buildDetailRow('Transaction ID', transaction.id, isDarkMode),
                  _buildDetailRow('Date', transaction.date, isDarkMode),
                  _buildDetailRow('Time', transaction.time, isDarkMode),
                  _buildDetailRow('Category', transaction.category, isDarkMode),
                  _buildDetailRow('Payment Method', 
                    transaction.isDebit ? 'Wallet Balance' : 'UPI / Card / Bank', 
                    isDarkMode),
                  const SizedBox(height: 32),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.help_outline,
                          color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                          size: 20,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            'Need help with this transaction? Contact our support team.',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFFCC00),
                      foregroundColor: Colors.black,
                      minimumSize: const Size(double.infinity, 50),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Receipt downloaded successfully'),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                      Navigator.pop(context);
                    },
                    child: Text(
                      'Download Receipt',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
            ),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}

class TransactionModel {
  final String id;
  final String title;
  final String amount;
  final String date;
  final String time;
  final bool isDebit;
  final String category;
  final IconData iconData;
  final String status;

  TransactionModel({
    required this.id,
    required this.title,
    required this.amount,
    required this.date,
    required this.time,
    required this.isDebit,
    required this.category,
    required this.iconData,
    required this.status,
  });
}
