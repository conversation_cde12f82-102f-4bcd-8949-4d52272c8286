import { Request, Response, NextFunction, RequestHandler } from 'express';
import { v4 as uuidv4 } from 'uuid';
import tournamentService, { TournamentService } from '../services/tournamentService';
import rewardsWalletService, { RewardTransactionType } from '../../rewards/services/rewardsWalletService';
import { logger } from '../../../utils/logger';
import { successResponse, errorResponse } from '../../../utils/responseFormatter';

// Enhanced error handling utilities
interface TournamentError {
  code: string;
  message: string;
  statusCode: number;
  details?: any;
}

const parseServiceError = (error: Error): TournamentError => {
  const message = error.message;

  // Parse specific error codes from service layer
  if (message.includes('TOURNAMENT_NOT_FOUND')) {
    return {
      code: 'TOURNAMENT_NOT_FOUND',
      message: 'The specified tournament could not be found',
      statusCode: 404
    };
  }

  if (message.includes('NOT_REGISTERED')) {
    return {
      code: 'NOT_REGISTERED',
      message: 'You are not registered for this tournament',
      statusCode: 403
    };
  }

  if (message.includes('SPECTATOR_RESTRICTION')) {
    return {
      code: 'SPECTATOR_RESTRICTION',
      message: 'Spectators cannot perform this action',
      statusCode: 403
    };
  }

  if (message.includes('already registered')) {
    return {
      code: 'ALREADY_REGISTERED',
      message: 'User is already registered for this tournament',
      statusCode: 409
    };
  }

  if (message.includes('Registration deadline has passed')) {
    return {
      code: 'REGISTRATION_DEADLINE_PASSED',
      message: 'Registration deadline has passed',
      statusCode: 400
    };
  }

  if (message.includes('maximum participants')) {
    return {
      code: 'TOURNAMENT_FULL',
      message: 'Tournament has reached maximum participants',
      statusCode: 400
    };
  }

  if (message.includes('Insufficient wallet balance')) {
    return {
      code: 'INSUFFICIENT_BALANCE',
      message: 'Insufficient wallet balance for tournament entry fee',
      statusCode: 400
    };
  }

  if (message.includes('DEADLINE_PASSED')) {
    return {
      code: 'DEADLINE_PASSED',
      message: 'The deadline for this action has passed',
      statusCode: 410
    };
  }

  if (message.includes('INSUFFICIENT_FUNDS')) {
    return {
      code: 'INSUFFICIENT_FUNDS',
      message: 'Insufficient funds to complete this action',
      statusCode: 402
    };
  }

  if (message.includes('ALREADY_SUBMITTED')) {
    return {
      code: 'ALREADY_SUBMITTED',
      message: 'You have already submitted a result for this tournament',
      statusCode: 409
    };
  }

  if (message.includes('INVALID_POSITION')) {
    return {
      code: 'INVALID_POSITION',
      message: 'Please provide a valid final position',
      statusCode: 400
    };
  }

  if (message.includes('MISSING_SCREENSHOT')) {
    return {
      code: 'MISSING_SCREENSHOT',
      message: 'At least one screenshot is required for result verification',
      statusCode: 400
    };
  }

  if (message.includes('TOURNAMENT_STATUS')) {
    return {
      code: 'INVALID_TOURNAMENT_STATUS',
      message: 'This action is not allowed for the current tournament status',
      statusCode: 409
    };
  }

  // Default error handling
  return {
    code: 'INTERNAL_ERROR',
    message: 'An internal error occurred',
    statusCode: 500,
    details: message
  };
};

// Import the service result interfaces to ensure proper typing
interface DiamondResult {
  success: boolean;
  diamonds: number;
  new_balance: number;
  message?: string;
  error?: string;
  transaction_id?: string;
}

interface PointsResult {
  success: boolean;
  points: number;
  message?: string;
  error?: string;
}

// Use the project's standard Request type with user property
interface AuthenticatedRequest extends Request {
  user: {
    userId: string;
    role: string;
    isActive: boolean;
    isVerified: boolean;
  };
}

// Type for request handlers that require authentication
type AuthenticatedRequestHandler = (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void | Response>;

/**
 * Get all tournaments with filtering options
 */
export const getTournaments: RequestHandler = async (req, res, next) => {
  try {
    const { status, game_id, limit, offset } = req.query;

    // Validate query parameters
    const parsedLimit = limit ? parseInt(limit as string) : undefined;
    const parsedOffset = offset ? parseInt(offset as string) : undefined;
    const parsedGameId = game_id ? parseInt(game_id as string) : undefined;

    if (limit && (isNaN(parsedLimit!) || parsedLimit! < 1 || parsedLimit! > 100)) {
      return res.status(400).json(errorResponse('Invalid limit parameter. Must be between 1 and 100.', 'INVALID_PARAMETER'));
    }

    if (offset && (isNaN(parsedOffset!) || parsedOffset! < 0)) {
      return res.status(400).json(errorResponse('Invalid offset parameter. Must be 0 or greater.', 'INVALID_PARAMETER'));
    }

    if (game_id && (isNaN(parsedGameId!) || parsedGameId! < 1)) {
      return res.status(400).json(errorResponse('Invalid game_id parameter. Must be a positive integer.', 'INVALID_PARAMETER'));
    }

    const tournaments = await tournamentService.getTournaments({
      status: status as 'upcoming' | 'active' | 'completed' | 'cancelled' | undefined,
      game_id: parsedGameId,
      limit: parsedLimit,
      offset: parsedOffset
    });

    return res.status(200).json(successResponse(tournaments, 'Tournaments fetched successfully'));
  } catch (error) {
    logger.error(`Error in getTournaments controller: ${error instanceof Error ? error.message : String(error)}`);

    if (error instanceof Error) {
      const parsedError = parseServiceError(error);
      return res.status(parsedError.statusCode).json(errorResponse(parsedError.message, parsedError.code, parsedError.details));
    }

    return res.status(500).json(errorResponse('Failed to fetch tournaments', 'INTERNAL_ERROR'));
  }
};

/**
 * Get tournament by ID
 */
export const getTournamentById: RequestHandler = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Validate tournament ID format
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      return res.status(400).json(errorResponse('Invalid tournament ID format', 'INVALID_PARAMETER'));
    }

    const tournament = await tournamentService.getTournamentById(id);

    if (!tournament) {
      return res.status(404).json(errorResponse('Tournament not found', 'TOURNAMENT_NOT_FOUND'));
    }

    return res.status(200).json(successResponse(tournament, 'Tournament fetched successfully'));
  } catch (error) {
    logger.error(`Error in getTournamentById controller for ID ${req.params.id}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to fetch tournament', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Get matches by tournament ID
 */
export const getMatchesByTournamentId: RequestHandler = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { limit, offset } = req.query;

    // First check if the tournament exists to provide a clear 404 if it doesn't
    const tournament = await tournamentService.getTournamentById(id);
    if (!tournament) {
      return res.status(404).json(errorResponse('Tournament not found'));
    }

    // Fetch matches for the tournament using the new service method
    const matches = await tournamentService.getMatchesByTournamentId(id, {
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined,
    });
    
    return res.status(200).json(successResponse(matches, 'Tournament matches fetched successfully'));
  } catch (error) {
    logger.error(`Error in getMatchesByTournamentId controller for tournament ID ${req.params.id}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to fetch tournament matches', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Get all matches with filtering options
 */
export const getAllMatches: RequestHandler = async (req, res, next) => {
  try {
    const { status, limit, offset } = req.query;
    
    const matches = await tournamentService.getAllMatches({
      status: status as 'scheduled' | 'live' | 'completed' | 'cancelled' | undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined
    });
    
    return res.status(200).json(successResponse(matches, 'Matches fetched successfully'));
  } catch (error) {
    logger.error(`Error in getAllMatches controller: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to fetch matches', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Create a new tournament
 */
export const createTournament: RequestHandler = async (req, res, next) => {
  try {
    const tournamentData = req.body;
    const { userId } = (req as AuthenticatedRequest).user;

    const tournament = await tournamentService.createTournament(tournamentData, userId);

    return res.status(201).json(successResponse(tournament, 'Tournament created successfully'));
  } catch (error) {
    logger.error(`Error in createTournament controller: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to create tournament', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Register for a tournament
 */
export const registerForTournament: RequestHandler = async (req, res, next) => {
  // === COMPREHENSIVE AUTHENTICATION DEBUG LOGGING ===
  console.log('=== TOURNAMENT REGISTRATION AUTH DEBUG ===');
  console.log('Request Headers:', {
    authorization: req.headers.authorization?.substring(0, 30) + '...',
    'content-type': req.headers['content-type'],
    'user-agent': req.headers['user-agent']?.substring(0, 50) + '...'
  });
  console.log('Request Body:', req.body);
  
  // Ensure user is properly authenticated
  if (!(req as AuthenticatedRequest).user) {
    console.log('REGISTRATION FAILED: No user object in request');
    return res.status(401).json(errorResponse('Authentication required'));
  }

  const user = (req as AuthenticatedRequest).user;
  const { userId } = user;
  
  console.log('User Authentication Details:', {
    userId: userId,
    role: user.role,
    isActive: user.isActive,
    isVerified: user.isVerified
  });

  // Check user status
  if (!user.isActive) {
    console.log(`REGISTRATION FAILED: User ${userId} is not active`);
    return res.status(403).json(errorResponse('User account is not active'));
  }

  if (!user.isVerified) {
    console.log(`REGISTRATION FAILED: User ${userId} is not verified`);
    return res.status(403).json(errorResponse('User account is not verified'));
  }

  console.log(`User ${userId} attempting to register for tournament`);

  try {
    const { tournamentId } = req.body;
    console.log(`Tournament ID: ${tournamentId}`);
    console.log('Authentication checks passed, proceeding to tournament service...');

    const registration = await tournamentService.registerForTournament(tournamentId, userId);
    console.log(`Registration successful:`, registration);

    return res.status(201).json(successResponse(registration, 'Successfully registered for tournament'));
  } catch (error) {
    console.log(`Registration error:`, error);
    console.log(`Error type: ${error instanceof Error ? error.constructor.name : typeof error}`);
    console.log(`Error message: ${error instanceof Error ? error.message : String(error)}`);
    logger.error(`Error in registerForTournament controller for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);

    // Parse the error to return appropriate status code
    if (error instanceof Error) {
      const parsedError = parseServiceError(error);
      return res.status(parsedError.statusCode).json(errorResponse(parsedError.message, error.message));
    }

    return res.status(500).json(errorResponse('Failed to register for tournament', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Get tournament leaderboard
 */
export const getTournamentLeaderboard = async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
  try {
    const { id } = req.params;
    const leaderboard = await tournamentService.getTournamentLeaderboard(id);
    
    return res.status(200).json(successResponse(leaderboard, 'Tournament leaderboard fetched successfully'));
  } catch (error) {
    logger.error(`Error in getTournamentLeaderboard controller for tournament ${req.params.id}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to fetch tournament leaderboard', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Update participant score
 */
export const updateParticipantScore: RequestHandler = async (req, res, next) => {
  // Ensure user is properly authenticated
  if (!(req as AuthenticatedRequest).user) {
    return res.status(401).json(errorResponse('Authentication required'));
  }
  
  const { userId } = (req as AuthenticatedRequest).user;
  try {
    const { tournamentId, score } = req.body;
    // userId already extracted above
    
    const participant = await tournamentService.updateParticipantScore(tournamentId, userId, score);
    
    return res.status(200).json(successResponse(participant, 'Score updated successfully'));
  } catch (error) {
    logger.error(`Error in updateParticipantScore controller for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to update score', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Finalize tournament (admin only)
 */
export const finalizeTournament: RequestHandler = async (req, res, next) => {
  try {
    const { id } = req.params;
    await tournamentService.finalizeTournament(id);
    
    return res.status(200).json(successResponse(null, 'Tournament finalized successfully'));
  } catch (error) {
    logger.error(`Error in finalizeTournament controller for tournament ${req.params.id}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to finalize tournament', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Claim tournament reward
 */
export const claimTournamentReward: RequestHandler = async (req, res, next) => {
  // Ensure user is properly authenticated
  if (!(req as AuthenticatedRequest).user) {
    return res.status(401).json(errorResponse('Authentication required'));
  }
  
  const { userId } = (req as AuthenticatedRequest).user;
  try {
    const { tournamentId, idempotencyKey = uuidv4() } = req.body;
    // userId already extracted above
    
    // Claim the tournament reward
    const claimResult = await tournamentService.claimTournamentReward(
      tournamentId,
      userId,
      idempotencyKey
    );
    
    // Ensure reward and claim are properly typed with default values to avoid undefined access
    const reward = claimResult?.reward || {
      id: '',
      diamond_reward: 0,
      points_reward: 0
    };
    const claim = claimResult?.claim || {
      id: ''
    };
    
    // Get participant information to access rank
    let participantRank = 0;
    try {
      const { data: participant } = await tournamentService.getParticipant(tournamentId, userId);
      participantRank = participant?.final_rank || 0;
    } catch (participantError) {
      logger.error(`Error getting participant info: ${participantError instanceof Error ? participantError.message : String(participantError)}`);
      // Continue with default rank value
    }
    
    // Process diamonds reward
    let diamondResult: DiamondResult = { 
      success: true, 
      diamonds: 0, 
      new_balance: 0,
      message: undefined,
      error: undefined,
      transaction_id: undefined
    };
    if (reward.diamond_reward > 0) {
      diamondResult = await rewardsWalletService.processDiamondReward(
        userId,
        null, // Not using the rewards table directly
        reward.diamond_reward,
        RewardTransactionType.TOURNAMENT_PRIZE, // Using the correct enum
        `Tournament Reward: Rank ${participantRank} in Tournament #${tournamentId}`,

        `${idempotencyKey}-diamonds`
      );
      
      if (!diamondResult.success) {
        throw new Error(`Failed to process diamond reward: ${diamondResult.message || 'Unknown error'}`);
      }
    }
    
    // Process points reward if applicable
    let pointsResult: PointsResult = { 
      success: true, 
      points: 0,
      message: undefined,
      error: undefined
    };
    if (reward.points_reward > 0) {
      pointsResult = await rewardsWalletService.processLoyaltyPoints(
        userId,
        reward.points_reward,
        RewardTransactionType.TOURNAMENT_PRIZE,
        `Tournament Points: Rank ${participantRank} in Tournament #${tournamentId}`,

        `${idempotencyKey}-points`
      );
      
      if (!pointsResult.success) {
        throw new Error(`Failed to process points reward: ${pointsResult.message || 'Unknown error'}`);
      }
    }
    
    // Update the claim with transaction ID if available
    if (diamondResult.transaction_id && claim && claim.id) {
      try {
        // Use the service method instead of direct supabase access
        await tournamentService.updateClaimTransaction(claim.id, diamondResult.transaction_id);
      } catch (updateError) {
        logger.error(`Error updating claim transaction: ${updateError instanceof Error ? updateError.message : String(updateError)}`);
        // Continue execution even if this update fails
      }
    }
    
    return res.status(200).json({
      success: true,
      data: {
        reward: {
          id: reward.id,
          diamond_reward: reward.diamond_reward || 0,
          points_reward: reward.points_reward || 0
        },
        diamonds: {
          amount: reward.diamond_reward || 0,
          new_balance: diamondResult.new_balance || 0
        },
        points: reward.points_reward || 0
      }
    });
  } catch (error) {
    logger.error(`Error in claimTournamentReward controller for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('An error occurred', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Get user's joined tournaments
 */
export const getUserJoinedTournaments: AuthenticatedRequestHandler = async (req, res, next) => {
  try {
    const userId = req.user.userId;
    
    const filters = {
      status: req.query.status as string,
      user_tournament_status: req.query.user_tournament_status as string,
      limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
      offset: req.query.offset ? parseInt(req.query.offset as string) : 0
    };

    const joinedTournaments = await tournamentService.getUserJoinedTournaments(userId, filters);

    return res.status(200).json(successResponse(joinedTournaments, 'User joined tournaments fetched successfully'));
  } catch (error) {
    logger.error(`Error in getUserJoinedTournaments controller for user ${req.user?.userId}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to fetch user joined tournaments', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Get user's active tournaments (registered, active)
 */
export const getUserActiveTournaments: AuthenticatedRequestHandler = async (req, res, next) => {
  try {
    const userId = req.user.userId;

    const activeTournaments = await tournamentService.getUserActiveTournaments(userId);

    return res.status(200).json(successResponse(activeTournaments, 'User active tournaments fetched successfully'));
  } catch (error) {
    logger.error(`Error in getUserActiveTournaments controller for user ${req.user?.userId}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to fetch user active tournaments', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Submit tournament result
 */
export const submitTournamentResult: AuthenticatedRequestHandler = async (req, res, next) => {
  const { userId } = (req as AuthenticatedRequest).user;
  try {
    const { tournamentId } = req.params;
    const { final_position, final_score, kills_count, screenshot_url, screenshot_urls } = req.body;

    // Enhanced input validation
    if (!tournamentId || typeof tournamentId !== 'string' || tournamentId.trim().length === 0) {
      return res.status(400).json(errorResponse('Invalid tournament ID format', 'INVALID_PARAMETER'));
    }

    if (!final_position || typeof final_position !== 'number' || final_position < 1) {
      return res.status(400).json(errorResponse('Final position is required and must be a positive number', 'INVALID_POSITION'));
    }

    if (final_score !== undefined && final_score !== null && (typeof final_score !== 'number' || final_score < 0)) {
      return res.status(400).json(errorResponse('Final score must be a non-negative number', 'INVALID_SCORE'));
    }

    if (kills_count !== undefined && kills_count !== null && (typeof kills_count !== 'number' || kills_count < 0)) {
      return res.status(400).json(errorResponse('Kills count must be a non-negative number', 'INVALID_KILLS'));
    }

    // Validate screenshot requirements
    const hasScreenshots = (screenshot_urls && Array.isArray(screenshot_urls) && screenshot_urls.length > 0) ||
                          (screenshot_url && typeof screenshot_url === 'string' && screenshot_url.trim().length > 0);

    if (!hasScreenshots) {
      return res.status(400).json(errorResponse('At least one screenshot is required for result verification', 'MISSING_SCREENSHOT'));
    }

    const result = await tournamentService.submitTournamentResult(tournamentId, userId, {
      final_position,
      final_score,
      kills_count,
      screenshot_url,
      screenshot_urls
    });

    return res.status(201).json(successResponse(result, 'Tournament result submitted successfully'));
  } catch (error) {
    logger.error(`Error in submitTournamentResult controller for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);

    if (error instanceof Error) {
      const parsedError = parseServiceError(error);
      return res.status(parsedError.statusCode).json(errorResponse(parsedError.message, parsedError.code, parsedError.details));
    }

    return res.status(500).json(errorResponse('Failed to submit tournament result', 'INTERNAL_ERROR'));
  }
};

/**
 * Get tournament verification status for real-time updates
 */
export const getTournamentVerificationStatus: AuthenticatedRequestHandler = async (req, res, next) => {
  const { userId } = (req as AuthenticatedRequest).user;
  try {
    const { tournamentId, targetUserId } = req.params;

    // Validate parameters
    if (!tournamentId || typeof tournamentId !== 'string' || tournamentId.trim().length === 0) {
      return res.status(400).json(errorResponse('Invalid tournament ID format', 'INVALID_PARAMETER'));
    }

    // Use targetUserId if provided (for admin access), otherwise use authenticated user's ID
    const userIdToCheck = targetUserId || userId;

    const status = await tournamentService.getTournamentVerificationStatus(tournamentId, userIdToCheck);

    if (!status) {
      return res.status(404).json(errorResponse('No verification status found for this tournament', 'STATUS_NOT_FOUND'));
    }

    return res.status(200).json(successResponse(status, 'Tournament verification status retrieved successfully'));
  } catch (error) {
    logger.error(`Error in getTournamentVerificationStatus controller for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);

    if (error instanceof Error) {
      const parsedError = parseServiceError(error);
      return res.status(parsedError.statusCode).json(errorResponse(parsedError.message, parsedError.code, parsedError.details));
    }

    return res.status(500).json(errorResponse('Failed to get tournament verification status', 'INTERNAL_ERROR'));
  }
};



/**
 * Get pending tournament results for admin verification
 */
export const getPendingTournamentResults: AuthenticatedRequestHandler = async (req, res, next) => {
  try {
    const { limit, offset } = req.query;

    const pendingResults = await tournamentService.getPendingTournamentResults({
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined
    });

    return res.status(200).json(successResponse(pendingResults, 'Pending tournament results retrieved successfully'));
  } catch (error) {
    logger.error(`Error in getPendingTournamentResults controller: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to retrieve pending tournament results', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Verify tournament result (admin only)
 */
export const verifyTournamentResult: AuthenticatedRequestHandler = async (req, res, next) => {
  const { userId } = (req as AuthenticatedRequest).user;
  try {
    const { resultId } = req.params;
    const { action, admin_notes } = req.body;

    const result = await tournamentService.verifyTournamentResult(resultId, action, userId, admin_notes);

    return res.status(200).json(successResponse(result, `Tournament result ${action}d successfully`));
  } catch (error) {
    logger.error(`Error in verifyTournamentResult controller for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to verify tournament result', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Assign tournament winner(s)
 */
export const assignTournamentWinner: AuthenticatedRequestHandler = async (req, res, next) => {
  const { userId } = (req as AuthenticatedRequest).user;
  try {
    const { tournamentId } = req.params;
    const { winners } = req.body; // Array of winner objects with user_id and position

    const result = await tournamentService.assignTournamentWinners(tournamentId, winners, userId);

    return res.status(200).json(successResponse(result, 'Tournament winners assigned successfully'));
  } catch (error) {
    logger.error(`Error in assignTournamentWinner controller for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to assign tournament winners', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Start tournament match (spectator/admin action)
 */
export const startTournamentMatch: AuthenticatedRequestHandler = async (req, res, next) => {
  const { userId } = (req as AuthenticatedRequest).user;
  try {
    const { tournamentId } = req.params;
    const { room_id, room_password } = req.body;

    console.log(`Controller received - tournamentId: ${tournamentId}, userId: ${userId}, room_id: ${room_id}, room_password: ${room_password}`);

    const result = await tournamentService.startTournamentMatch(tournamentId, userId, {
      room_id,
      room_password
    });

    return res.status(200).json(successResponse(result, 'Tournament match started successfully'));
  } catch (error) {
    logger.error(`Error in startTournamentMatch controller for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to start tournament match', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * End tournament match (spectator/admin action)
 */
export const endTournamentMatch: AuthenticatedRequestHandler = async (req, res, next) => {
  const { userId } = (req as AuthenticatedRequest).user;
  try {
    const { tournamentId } = req.params;

    const result = await tournamentService.endTournamentMatch(tournamentId, userId);

    return res.status(200).json(successResponse(result, 'Tournament match ended successfully'));
  } catch (error) {
    logger.error(`Error in endTournamentMatch controller for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to end tournament match', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Check if user has submitted results for a tournament
 */
export const checkSubmissionStatus: AuthenticatedRequestHandler = async (req, res, next) => {
  const { userId } = (req as AuthenticatedRequest).user;
  try {
    const { tournamentId } = req.params;

    if (!tournamentId || typeof tournamentId !== 'string' || tournamentId.trim().length === 0) {
      return res.status(400).json(errorResponse('Invalid tournament ID format', 'INVALID_PARAMETER'));
    }

    const hasSubmitted = await tournamentService.hasUserSubmittedResults(tournamentId, userId);

    return res.status(200).json(successResponse({
      hasSubmitted,
      tournamentId,
      userId
    }, 'Submission status retrieved successfully'));
  } catch (error) {
    logger.error(`Error in checkSubmissionStatus controller for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
    return res.status(500).json(errorResponse('Failed to check submission status', error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Upload screenshot for tournament results
 */
export const uploadScreenshot: AuthenticatedRequestHandler = async (req, res, next) => {
  const { userId } = (req as AuthenticatedRequest).user;
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const { id } = req.params;
    const file = req.file;

    if (!file) {
      return res.status(400).json({
        success: false,
        error: 'Screenshot file is required'
      });
    }

    // Debug: Log file details
    console.log('TournamentController: Received file details from multer:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      fieldname: file.fieldname,
      path: file.path, // Log path if available (for disk storage)
      bufferExists: !!file.buffer // Log if buffer exists (for memory storage)
    });

    // Validate tournament exists and user is registered
    // Use the service method to check tournament and participant status
    try {
      const tournament = await tournamentService.getTournamentById(id);
      if (!tournament) {
        return res.status(404).json({
          success: false,
          error: 'Tournament not found'
        });
      }
    } catch (error) {
      return res.status(404).json({
        success: false,
        error: 'Tournament not found'
      });
    }

    // Check if user is registered for this tournament
    try {
      const isRegistered = await tournamentService.isUserRegistered(id, userId);
      if (!isRegistered) {
        return res.status(403).json({
          success: false,
          error: 'You are not registered for this tournament'
        });
      }
    } catch (error) {
      return res.status(403).json({
        success: false,
        error: 'You are not registered for this tournament'
      });
    }

    console.log(`TournamentController: Passing file to tournamentService.uploadScreenshot. Originalname: ${file.originalname}, Mimetype: ${file.mimetype}`);
    const screenshotUrl = await tournamentService.uploadScreenshot(file as any, id, req.user.userId);

    res.json({
      success: true,
      data: { screenshot_url: screenshotUrl },
      message: 'Screenshot uploaded successfully'
    });
  } catch (error) {
    console.error('Error uploading tournament screenshot:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to upload screenshot',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
