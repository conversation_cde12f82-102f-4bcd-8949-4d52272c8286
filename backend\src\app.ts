import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import { initRedis, isRedisConnectionAvailable, isUsingFallbackStorage } from './config/redis';
import { logger } from './utils/logger';
import { enforceHttps, secureHeadersMiddleware } from './middleware/enforceHttps';
import authRoutes from './features/auth/routes';
import profileRoutes from './features/profile/routes';
import walletRoutes from './features/wallet/routes';
// import shopRoutes from './features/shop/routes'; // Temporarily disabled
import rewardsRoutes from './features/rewards/routes';
import tournamentRoutes from './features/tournaments/routes';
import matchRoutes from './features/matches/routes'; // Import match routes
import adminUsersRoutes from './features/admin-users/routes';
import adminWithdrawalRoutes from './features/wallet/routes/adminWithdrawalRoutes'; // Import admin withdrawal routes
import adminNotificationRoutes from './features/wallet/routes/adminNotificationRoutes'; // Import admin notification routes
import gameRoutes from './routes/games'; // Import game routes
import supportRoutes from './features/support/routes'; // Import support routes
import { matchDeadlineService } from './features/matches/services/matchDeadlineService';

const app = express();

// Initialize Redis for distributed caching and rate limiting
initRedis().then(() => {
  if (isRedisConnectionAvailable()) {
    logger.info('Redis initialized successfully for distributed caching and rate limiting');
  } else {
    logger.warn('Redis connection failed, using memory fallback for caching and rate limiting');
  }
}).catch(err => {
  logger.error(`Failed to initialize Redis: ${err instanceof Error ? err.message : String(err)}`);
});

// Middleware

// Apply HTTPS enforcement early in the middleware chain
app.use(enforceHttps);

// Apply secure headers middleware
app.use(secureHeadersMiddleware);

// Body parsing middleware with explicit charset
app.use(express.json({
  limit: '10mb',
  type: 'application/json'
}));

// Set default charset for all responses
app.use((req, res, next) => {
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  next();
});

// Add middleware to log all OPTIONS requests for debugging
app.use((req, res, next) => {
  if (req.method === 'OPTIONS') {
    logger.debug(`🔍 [CORS] OPTIONS request to ${req.originalUrl}`);
    logger.debug(`🔍 [CORS] Origin: ${req.headers.origin}`);
    logger.debug(`🔍 [CORS] Access-Control-Request-Method: ${req.headers['access-control-request-method']}`);
    logger.debug(`🔍 [CORS] Access-Control-Request-Headers: ${req.headers['access-control-request-headers']}`);
  }
  next();
});

// Configure CORS with specific options
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://app.wiggyz.com', 'https://admin.wiggyz.com'] // Restrict origins in production
    : '*', // Allow all origins in development
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Idempotency-Key'],
  credentials: true, // Support credentials
  maxAge: 0 // Disable preflight caching for debugging
}));

// Log all requests
app.use(morgan('dev'));

// Add route debugging for tournaments
app.use('/api/v1/tournaments', (req, res, next) => {
  console.log(`Tournament route accessed: ${req.method} ${req.path}`);
  next();
});

// Routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/profile', profileRoutes);
app.use('/api/v1/wallet', walletRoutes);
// app.use('/api/v1/shop', shopRoutes); // Temporarily disabled
app.use('/api/v1/rewards', rewardsRoutes);
app.use('/api/v1/tournaments', tournamentRoutes); // Mount tournament routes at the correct path
app.use('/api/v1/matches', matchRoutes); // Mount match routes
app.use('/api/v1/admin/users', adminUsersRoutes);
app.use('/api/v1/admin/withdrawals', adminWithdrawalRoutes); // Mount admin withdrawal routes
app.use('/api/v1/admin/notifications', adminNotificationRoutes); // Mount admin notification routes
app.use('/api/v1/games', gameRoutes); // Mount game routes
app.use('/api/v1/support', supportRoutes); // Mount support routes

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Initialize background services
if (process.env.NODE_ENV !== 'test') {
  // Start match deadline monitoring service
  matchDeadlineService.startDeadlineMonitoring();
  logger.info('Match deadline monitoring service started');

  // Graceful shutdown handling
  process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    matchDeadlineService.stopDeadlineMonitoring();
    process.exit(0);
  });

  process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down gracefully');
    matchDeadlineService.stopDeadlineMonitoring();
    process.exit(0);
  });
}

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Internal Server Error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

export default app;
