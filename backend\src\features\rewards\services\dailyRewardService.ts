import { createClient } from '@supabase/supabase-js';
import { config } from '../../../config';
import { logger } from '../../../utils/logger';
import rewardsService from './rewardsService';
import { walletService } from '../../wallet/services/walletService';
import { loyaltyService, LoyaltyAction } from './loyaltyService';
import { supabaseHelpers } from '../../../utils/supabaseHelpers';

const supabase = createClient(config.supabase.url, config.supabase.key);

class DailyRewardService {
  async getDailyRewardStatus(userId: string) {
    try {
      // Get the user's login streak
      const { data: streakData, error: streakError } = await supabase
        .from('login_streaks')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (streakError && streakError.code !== 'PGRST116') {
        throw streakError;
      }

      // Get daily rewards
      const { data: rewardsData, error: rewardsError } = await supabase
        .from('rewards')
        .select('*')
        .eq('type_id', '3a5f9e7d-cb09-4cae-8f3b-a7f43e2c8bb6') // daily_login type
        .order('requirements->day', { ascending: true });

      if (rewardsError) throw rewardsError;

      // Check if already claimed today
      const today = new Date().toISOString().split('T')[0];
      const { data: claimedData, error: claimedError } = await supabase
        .from('user_rewards')
        .select('*')
        .eq('user_id', userId)
        .gte('claimed_at', `${today}T00:00:00Z`)
        .lt('claimed_at', `${today}T23:59:59Z`);

      if (claimedError) throw claimedError;

      const hasClaimedToday = claimedData && claimedData.length > 0;

      logger.info(`Daily reward status check for user ${userId}: hasClaimedToday=${hasClaimedToday}, today=${today}, claimedCount=${claimedData?.length || 0}`);

      // Ensure streak data has all required fields
      const streak = streakData || {
        id: `default-streak-${userId}`,
        user_id: userId,
        current_streak: 0,
        longest_streak: 0,
        last_login_date: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      return {
        streak,
        rewards: rewardsData || [],
        hasClaimedToday
      };
    } catch (error) {
      logger.error(`Error in getDailyRewardStatus: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  async claimDailyReward(userId: string, idempotencyKey: string) {
    // Use UTC for consistent timezone handling
    const now = new Date();
    const today = now.toISOString().split('T')[0];

    try {
      // Start a transaction
      const { data: streakData, error: streakError } = await supabase
        .from('login_streaks')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (streakError && streakError.code !== 'PGRST116') {
        throw streakError;
      }

      // Check if already claimed today using UTC timezone
      const { data: claimedData, error: claimedError } = await supabase
        .from('user_rewards')
        .select('*')
        .eq('user_id', userId)
        .gte('claimed_at', `${today}T00:00:00Z`)
        .lt('claimed_at', `${today}T23:59:59Z`);

      if (claimedError) throw claimedError;

      if (claimedData && claimedData.length > 0) {
        throw new Error('Daily reward already claimed today');
      }

      // Calculate new streak with improved logic
      let currentStreak = 1;
      let longestStreak = 1;

      if (streakData) {
        const lastLoginDate = streakData.last_login_date;
        const yesterday = new Date(now);
        yesterday.setUTCDate(yesterday.getUTCDate() - 1);
        const yesterdayStr = yesterday.toISOString().split('T')[0];

        // Fixed streak calculation logic
        if (lastLoginDate === yesterdayStr) {
          // User logged in yesterday, continue streak
          currentStreak = streakData.current_streak + 1;
        } else if (lastLoginDate === today) {
          // User already logged in today, maintain current streak
          currentStreak = streakData.current_streak;
        } else {
          // User missed days, reset streak
          currentStreak = 1;
        }

        longestStreak = Math.max(currentStreak, streakData.longest_streak);
      }

      // Use transaction helper for consistency
      const result = await supabaseHelpers.transaction(async () => {
        // Update or insert streak
        if (streakData) {
          const { error: updateStreakError } = await supabase
            .from('login_streaks')
            .update({
              current_streak: currentStreak,
              longest_streak: longestStreak,
              last_login_date: today,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', userId);

          if (updateStreakError) throw updateStreakError;
        } else {
          const { error: insertStreakError } = await supabase
            .from('login_streaks')
            .insert({
              user_id: userId,
              current_streak: currentStreak,
              longest_streak: longestStreak,
              last_login_date: today
            });

          if (insertStreakError) throw insertStreakError;
        }

        // Get the appropriate reward based on streak
        let rewardData: any;
        const { data: initialRewardData, error: rewardError } = await supabase
          .from('rewards')
          .select('*')
          .eq('type_id', '3a5f9e7d-cb09-4cae-8f3b-a7f43e2c8bb6') // daily_login type
          .lte('requirements->day', currentStreak)
          .order('requirements->day', { ascending: false })
          .limit(1)
          .single();

        if (rewardError) {
          // If no reward found for current streak, try to get the highest available reward
          const { data: fallbackReward, error: fallbackError } = await supabase
            .from('rewards')
            .select('*')
            .eq('type_id', '3a5f9e7d-cb09-4cae-8f3b-a7f43e2c8bb6')
            .order('requirements->day', { ascending: false })
            .limit(1)
            .single();

          if (fallbackError) {
            throw new Error(`No daily rewards configured: ${fallbackError.message}`);
          }

          rewardData = fallbackReward;
        } else {
          rewardData = initialRewardData;
        }

        // Record the claimed reward
        const { data: userReward, error: userRewardError } = await supabase
          .from('user_rewards')
          .insert({
            user_id: userId,
            reward_id: rewardData.id,
            claimed_at: new Date().toISOString()
          })
          .select()
          .single();

        if (userRewardError) throw userRewardError;

        // Record transaction
        await rewardsService.createRewardTransaction(
          userId,
          rewardData.id,
          rewardData.points,
          'daily_reward',
          `Day ${currentStreak} login reward`
        );

        // Update loyalty points using the proper loyalty service
        await loyaltyService.awardLoyaltyPoints(
          userId,
          rewardData.points,
          LoyaltyAction.DAILY_LOGIN,
          `Day ${currentStreak} login reward`,
          `${idempotencyKey}_loyalty`
        );

        // Update wallet with diamonds
        await walletService.addDiamonds(
          userId,
          rewardData.diamond_value,
          'daily_reward',
          `Day ${currentStreak} login reward`,
          idempotencyKey
        );

        return {
          reward: rewardData,
          streak: {
            current_streak: currentStreak,
            longest_streak: longestStreak
          }
        };
      });

      return result;
    } catch (error) {
      logger.error(`Error in claimDailyReward: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
}

export default new DailyRewardService();
