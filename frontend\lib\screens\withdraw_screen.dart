import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:wiggyz_app/features/wallet/providers/wallet_provider.dart';

class WithdrawScreen extends StatefulWidget {
  const WithdrawScreen({super.key});

  @override
  State<WithdrawScreen> createState() => _WithdrawScreenState();
}

class _WithdrawScreenState extends State<WithdrawScreen> {
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _accountNumberController = TextEditingController();
  final TextEditingController _ifscCodeController = TextEditingController();
  final TextEditingController _accountHolderNameController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  
  final List<String> _quickAmounts = ['₹500', '₹1000', '₹1500', '₹2000'];
  String _selectedBank = 'HDFC Bank';
  final List<String> _banks = [
    'HDFC Bank', 
    'ICICI Bank', 
    'SBI', 
    'Axis Bank', 
    'Kotak Mahindra',
    'Other'
  ];
  
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();

    // Add listener for real-time withdrawal summary updates
    _amountController.addListener(_onAmountChanged);

    // Fetch wallet details when the screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final walletProvider = Provider.of<WalletProvider>(
        context,
        listen: false,
      );
      walletProvider.fetchWalletDetails();
    });
  }

  void _onAmountChanged() {
    // Trigger UI rebuild when amount changes
    setState(() {
      // The withdrawal summary will automatically recalculate in _buildWithdrawalSummary
    });
  }

  String _formatCurrency(double amount) {
    if (amount == 0.0) return '₹0';
    return '₹${amount.toStringAsFixed(2)}';
  }

  String _getTransactionFeeLabel(double withdrawalAmount, double transactionFee) {
    if (withdrawalAmount == 0.0) return 'Transaction Fee';

    final calculatedFee = withdrawalAmount * 0.02;
    if (transactionFee <= calculatedFee + 0.01) { // Small tolerance for floating point comparison
      return 'Transaction Fee (2%)';
    } else {
      return 'Transaction Fee (Min ₹5)';
    }
  }

  @override
  void dispose() {
    _amountController.removeListener(_onAmountChanged);
    _amountController.dispose();
    _accountNumberController.dispose();
    _ifscCodeController.dispose();
    _accountHolderNameController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final walletProvider = Provider.of<WalletProvider>(context);
    
    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.grey[100],
      appBar: AppBar(
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFFFFCC00),
                Color(0xFFFF9500),
              ],
            ),
          ),
        ),
        backgroundColor: Colors.transparent,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Colors.black,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Withdraw Funds',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Current Balance Card
                  _buildBalanceCard(isDarkMode, walletProvider),
                  
                  const SizedBox(height: 24),
                  
                  // Amount Input Section
                  _buildSectionTitle('Withdrawal Amount', isDarkMode),
                  const SizedBox(height: 12),
                  
                  // Amount Text Field
                  _buildAmountTextField(isDarkMode, walletProvider),
                  
                  const SizedBox(height: 16),
                  
                  // Quick Amount Selection
                  _buildQuickAmountSelector(isDarkMode),
                  
                  const SizedBox(height: 24),
                  
                  // Bank Account Section
                  _buildSectionTitle('Bank Account Details', isDarkMode),
                  const SizedBox(height: 12),
                  
                  // Bank Selection
                  _buildBankSelector(isDarkMode),
                  
                  const SizedBox(height: 16),
                  
                  // Account Number
                  _buildTextField(
                    controller: _accountNumberController,
                    label: 'Account Number',
                    hint: 'Enter your account number',
                    keyboardType: TextInputType.number,
                    icon: Icons.account_balance,
                    isDarkMode: isDarkMode,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter account number';
                      }
                      if (value.length < 9 || value.length > 18) {
                        return 'Invalid account number';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // IFSC Code
                  _buildTextField(
                    controller: _ifscCodeController,
                    label: 'IFSC Code',
                    hint: 'Enter IFSC code',
                    keyboardType: TextInputType.text,
                    icon: Icons.confirmation_number,
                    isDarkMode: isDarkMode,
                    textCapitalization: TextCapitalization.characters,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]')),
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter IFSC code';
                      }
                      if (!RegExp(r'^[A-Z]{4}0[A-Z0-9]{6}$').hasMatch(value.toUpperCase())) {
                        return 'Invalid IFSC code';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Account Holder Name
                  _buildTextField(
                    controller: _accountHolderNameController,
                    label: 'Account Holder Name',
                    hint: 'Enter account holder name',
                    keyboardType: TextInputType.name,
                    icon: Icons.person,
                    isDarkMode: isDarkMode,
                    textCapitalization: TextCapitalization.words,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter account holder name';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Withdrawal Summary
                  _buildWithdrawalSummary(isDarkMode),
                  
                  const SizedBox(height: 24),
                  
                  // Security Note
                  _buildSecurityNote(isDarkMode),
                  
                  const SizedBox(height: 24),
                  
                  // Withdraw Button
                  _buildWithdrawButton(isDarkMode),
                  
                  const SizedBox(height: 16),
                  
                  // Terms and Conditions
                  _buildTermsText(isDarkMode),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildSectionTitle(String title, bool isDarkMode) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: isDarkMode ? Colors.white : Colors.black,
      ),
    );
  }
  
  Widget _buildBalanceCard(bool isDarkMode, WalletProvider walletProvider) {
    // Get real balance from wallet provider
    double availableBalance = 0.0;
    if (walletProvider.walletData != null && walletProvider.walletData!['balance'] != null) {
      availableBalance = double.tryParse(walletProvider.walletData!['balance'].toString()) ?? 0.0;
    }
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFFCC00),
            Color(0xFFFF9500),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFFCC00).withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Available Balance',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.account_balance_wallet,
                      size: 16,
                      color: Colors.black87,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Withdrawable',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          walletProvider.isLoading
              ? const SizedBox(
                  height: 32,
                  child: Center(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.black87),
                      ),
                    ),
                  ),
                )
              : Text(
                  '₹${availableBalance.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Minimum withdrawal: ₹100',
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildAmountTextField(bool isDarkMode, WalletProvider walletProvider) {
    // Get real available balance
    double availableBalance = 0.0;
    if (walletProvider.walletData != null && walletProvider.walletData!['balance'] != null) {
      availableBalance = double.tryParse(walletProvider.walletData!['balance'].toString()) ?? 0.0;
    }
    return TextFormField(
      controller: _amountController,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')), // Allow decimals with up to 2 decimal places
      ],
      style: GoogleFonts.poppins(
        fontSize: 16,
        color: isDarkMode ? Colors.grey[100] : Colors.black,
        fontWeight: FontWeight.w600,
      ),
      decoration: InputDecoration(
        filled: true,
        fillColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        prefixIcon: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Text(
            '₹',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: const Color(0xFFFFCC00),
            ),
          ),
        ),
        prefixIconConstraints: const BoxConstraints(
          minWidth: 0,
          minHeight: 0,
        ),
        hintText: '0',
        hintStyle: GoogleFonts.poppins(
          color: isDarkMode ? Colors.grey[400] : Colors.grey[400],
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Color(0xFFFFCC00),
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.red,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.red,
            width: 2,
          ),
        ),
        errorStyle: GoogleFonts.poppins(
          fontSize: 12,
          color: Colors.red,
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter amount';
        }
        final amount = int.tryParse(value);
        if (amount == null) {
          return 'Invalid amount';
        }
        if (amount < 100) {
          return 'Minimum withdrawal amount is ₹100';
        }
        if (amount > availableBalance) {
          return 'Amount exceeds available balance of ₹${availableBalance.toStringAsFixed(2)}';
        }
        return null;
      },
    );
  }
  
  Widget _buildQuickAmountSelector(bool isDarkMode) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _quickAmounts.map((amount) {
        return InkWell(
          onTap: () {
            _amountController.text = amount.substring(1); // Remove ₹ symbol
            // The listener will automatically trigger setState
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
              ),
            ),
            child: Text(
              amount,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
  
  Widget _buildBankSelector(bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedBank,
          isExpanded: true,
          dropdownColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
          icon: Icon(
            Icons.keyboard_arrow_down_rounded,
            color: isDarkMode ? Colors.grey[200] : Colors.grey[700],
          ),
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: isDarkMode ? Colors.grey[100] : Colors.black,
            fontWeight: FontWeight.w500,
          ),
          items: _banks.map<DropdownMenuItem<String>>((String bank) {
            return DropdownMenuItem<String>(
              value: bank,
              child: Row(
                children: [
                  Icon(
                    Icons.account_balance,
                    size: 18,
                    color: const Color(0xFFFFCC00),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    bank,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: isDarkMode ? Colors.grey[100] : Colors.black,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _selectedBank = newValue;
              });
            }
          },
        ),
      ),
    );
  }
  
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required TextInputType keyboardType,
    required IconData icon,
    required bool isDarkMode,
    TextCapitalization textCapitalization = TextCapitalization.none,
    List<TextInputFormatter>? inputFormatters,
    required String? Function(String?) validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      textCapitalization: textCapitalization,
      inputFormatters: inputFormatters,
      style: GoogleFonts.poppins(
        fontSize: 14,
        color: isDarkMode ? Colors.grey[100] : Colors.black,
        fontWeight: FontWeight.w500,
      ),
      decoration: InputDecoration(
        filled: true,
        fillColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        labelText: label,
        labelStyle: GoogleFonts.poppins(
          color: isDarkMode ? Colors.grey[200] : Colors.grey[700],
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        hintText: hint,
        hintStyle: GoogleFonts.poppins(
          color: isDarkMode ? Colors.grey[400] : Colors.grey[400],
          fontSize: 14,
        ),
        prefixIcon: Icon(
          icon,
          color: const Color(0xFFFFCC00),
          size: 20,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Color(0xFFFFCC00),
            width: 1.5,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.red,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.red,
            width: 1.5,
          ),
        ),
        errorStyle: GoogleFonts.poppins(
          fontSize: 12,
          color: Colors.red,
        ),
      ),
      validator: validator,
    );
  }
  
  Widget _buildWithdrawalSummary(bool isDarkMode) {
    // Calculate fees and final amount with real-time updates
    double withdrawalAmount = 0.0;
    double transactionFee = 0.0;
    double gstOnFee = 0.0;
    double totalDeductions = 0.0;
    double finalAmount = 0.0;
    bool isValidAmount = false;
    String? validationMessage;

    if (_amountController.text.isNotEmpty) {
      withdrawalAmount = double.tryParse(_amountController.text) ?? 0.0;

      if (withdrawalAmount > 0) {
        isValidAmount = true;
        // Minimum ₹5 transaction fee OR 2% of withdrawal amount (whichever is higher)
        final calculatedFee = withdrawalAmount * 0.02; // 2% of withdrawal amount
        transactionFee = calculatedFee > 5.0 ? calculatedFee : 5.0; // Minimum ₹5
        gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
        totalDeductions = transactionFee + gstOnFee;
        finalAmount = withdrawalAmount - totalDeductions; // Amount user receives after fees

        // Validation checks
        if (withdrawalAmount < 100) {
          validationMessage = 'Minimum withdrawal amount is ₹100';
        } else if (finalAmount < 80) {
          validationMessage = 'Net amount after fees should be at least ₹80';
        }

        // Check wallet balance
        final walletProvider = Provider.of<WalletProvider>(context, listen: false);
        if (walletProvider.walletData != null && walletProvider.walletData!['balance'] != null) {
          double availableBalance = double.tryParse(walletProvider.walletData!['balance'].toString()) ?? 0.0;
          if (withdrawalAmount > availableBalance) {
            validationMessage = 'Insufficient balance. Available: ₹${availableBalance.toStringAsFixed(2)}';
          }
        }
      }
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Withdrawal Summary',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Estimated fees (final amounts calculated by system)',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: isDarkMode ? Colors.grey[200] : Colors.grey[600],
              fontStyle: FontStyle.italic,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (validationMessage != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.orange.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    size: 16,
                    color: Colors.orange,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      validationMessage,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: 12),
          _buildSummaryRow(
            'Withdrawal Amount',
            _formatCurrency(withdrawalAmount),
            isDarkMode,
          ),
          _buildSummaryRow(
            _getTransactionFeeLabel(withdrawalAmount, transactionFee),
            _formatCurrency(transactionFee),
            isDarkMode,
            valueColor: Colors.orange,
          ),
          _buildSummaryRow(
            'GST on Fee (18%)',
            _formatCurrency(gstOnFee),
            isDarkMode,
            valueColor: Colors.orange,
          ),
          _buildSummaryRow(
            'Total Deductions',
            _formatCurrency(totalDeductions),
            isDarkMode,
            valueColor: Colors.red,
            isBold: true,
          ),
          _buildSummaryRow(
            'Processing Time',
            'Instant to 2 Hours',
            isDarkMode,
          ),
          const Divider(height: 24),
          _buildSummaryRow(
            'Amount Transferred to Bank',
            _formatCurrency(finalAmount),
            isDarkMode,
            isBold: true,
            valueColor: const Color(0xFFFFCC00),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSummaryRow(
    String label,
    String value,
    bool isDarkMode, {
    bool isBold = false,
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[200] : Colors.grey[700],
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: isBold ? FontWeight.bold : FontWeight.w600,
              color: valueColor ?? (isDarkMode ? Colors.grey[100] : Colors.black),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSecurityNote(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.blue.withOpacity(0.1) : Colors.blue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.blue.withOpacity(0.3),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.security,
            color: Colors.blue,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Security Note',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'We prioritize your security. Please ensure that the bank account details provided belong to you. Withdrawals to third-party accounts are not allowed.',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: isDarkMode ? Colors.blue[100] : Colors.blue[800],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildWithdrawButton(bool isDarkMode) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isProcessing
            ? null
            : () {
                if (_formKey.currentState!.validate()) {
                  _processWithdrawal();
                }
              },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFFCC00),
          foregroundColor: Colors.black,
          disabledBackgroundColor: isDarkMode ? Colors.grey[800] : Colors.grey[300],
          disabledForegroundColor: isDarkMode ? Colors.grey[600] : Colors.grey[500],
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: _isProcessing
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.black,
                ),
              )
            : Text(
                'Withdraw Funds',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }
  
  Widget _buildTermsText(bool isDarkMode) {
    return Center(
      child: Text(
        'By continuing, you agree to our Terms & Conditions and Withdrawal Policy',
        style: GoogleFonts.poppins(
          fontSize: 12,
          color: isDarkMode ? Colors.grey[500] : Colors.grey[600],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
  
  void _processWithdrawal() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final withdrawalAmount = double.parse(_amountController.text);
      final transactionFee = withdrawalAmount * 0.02; // 2% transaction fee
      final gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
      final totalDeductions = transactionFee + gstOnFee;
      final finalAmount = withdrawalAmount - totalDeductions; // Amount user receives after fees
      final walletProvider = Provider.of<WalletProvider>(context, listen: false);

      // Prepare withdrawal data (backend will calculate fees)
      final withdrawalData = {
        'amount': withdrawalAmount, // Full amount to be deducted from wallet
        'currency': 'INR',
        'withdrawal_method': 'bank_transfer',
        'account_details': {
          'account_number': _accountNumberController.text,
          'ifsc_code': _ifscCodeController.text,
          'account_holder_name': _accountHolderNameController.text,
          'bank_name': _selectedBank,
        }
      };

      // Process withdrawal through WalletProvider
      final success = await walletProvider.withdraw(withdrawalData);

      setState(() {
        _isProcessing = false;
      });

      if (success) {
        // Show success dialog
        _showSuccessDialog();
      } else {
        // Show error dialog
        _showErrorDialog(walletProvider.errorMessage ?? 'Withdrawal failed. Please try again.');
      }
    } catch (e) {
      setState(() {
        _isProcessing = false;
      });

      // Show error dialog
      _showErrorDialog('Invalid withdrawal details. Please check and try again.');
    }
  }
  
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;
        
        return Dialog(
          backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 48,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Withdrawal Request Submitted!',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Your withdrawal request of ₹${_amountController.text} has been submitted and is pending admin approval. The amount has been deducted from your wallet.',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.grey[100]!,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      _buildDialogDetailRow(
                        'Reference ID',
                        'WD${DateTime.now().millisecondsSinceEpoch.toString().substring(5, 13)}',
                        isDarkMode,
                      ),
                      _buildDialogDetailRow(
                        'Account',
                        'XXXX${_accountNumberController.text.substring(_accountNumberController.text.length > 4 ? _accountNumberController.text.length - 4 : 0)}',
                        isDarkMode,
                      ),
                      _buildDialogDetailRow(
                        'Bank',
                        _selectedBank,
                        isDarkMode,
                      ),
                      _buildDialogDetailRow(
                        'Status',
                        'Pending Approval',
                        isDarkMode,
                        valueColor: Colors.orange,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isDarkMode ? Colors.blue.withOpacity(0.1) : Colors.blue.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.blue,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Your request is being reviewed by our team. You will be notified once approved and funds are transferred.',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: () async {
                      Navigator.pop(context); // Close dialog

                      // Refresh wallet data and transactions
                      final walletProvider = Provider.of<WalletProvider>(context, listen: false);
                      await walletProvider.fetchWalletDetails();
                      await walletProvider.fetchWalletTransactions(page: 1, limit: 50);

                      Navigator.pop(context); // Go back to wallet screen
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFFCC00),
                      foregroundColor: Colors.black,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Back to Wallet',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildDialogDetailRow(
    String label,
    String value,
    bool isDarkMode, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
            ),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: valueColor ?? (isDarkMode ? Colors.white : Colors.black),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String errorMessage) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;

        return Dialog(
          backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 30,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Withdrawal Failed',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  errorMessage,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Try Again',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
