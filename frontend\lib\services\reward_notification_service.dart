/// Reward notification service for managing reward-related notifications
/// Handles daily reward reminders, achievement unlocks, loyalty upgrades, and expiration warnings
library;

import '../models/notification_model.dart';
import '../models/reward_models.dart';
import '../models/achievement_models.dart';
import '../models/loyalty_models.dart';
import '../models/daily_reward_models.dart';

class RewardNotificationService {
  static final RewardNotificationService _instance = RewardNotificationService._internal();
  factory RewardNotificationService() => _instance;
  RewardNotificationService._internal();

  // Callback for adding notifications to the notification provider
  Function(NotificationModel)? _addNotificationCallback;

  /// Set the callback for adding notifications
  void setAddNotificationCallback(Function(NotificationModel) callback) {
    _addNotificationCallback = callback;
  }

  /// Add a notification to the system
  void _addNotification(NotificationModel notification) {
    _addNotificationCallback?.call(notification);
  }

  /// Generate unique notification ID
  String _generateNotificationId() {
    return 'reward_${DateTime.now().millisecondsSinceEpoch}';
  }

  // ==================== DAILY REWARD NOTIFICATIONS ====================

  /// Notify about available daily reward
  void notifyDailyRewardAvailable(DailyRewardDay day) {
    if (!day.canClaim || day.reward == null) return;

    final notification = NotificationModel(
      id: _generateNotificationId(),
      title: 'Daily Reward Available! 🎁',
      message: 'Claim your day ${day.day} reward: ${day.reward!.diamondValue} diamonds',
      type: NotificationType.rewardsDistributed,
      timestamp: DateTime.now(),
      payload: {
        'type': 'daily_reward',
        'day': day.day,
        'reward_id': day.reward!.id,
        'diamonds': day.reward!.diamondValue,
      },
    );

    _addNotification(notification);
  }

  /// Notify about daily reward claimed successfully
  void notifyDailyRewardClaimed(DailyRewardClaimResult result) {
    if (!result.success || result.claimedReward == null) return;

    final notification = NotificationModel(
      id: _generateNotificationId(),
      title: 'Daily Reward Claimed! ✨',
      message: 'You earned ${result.diamondsEarned ?? 0} diamonds and ${result.pointsEarned ?? 0} points',
      type: NotificationType.rewardsDistributed,
      timestamp: DateTime.now(),
      payload: {
        'type': 'daily_reward_claimed',
        'reward_id': result.claimedReward!.id,
        'diamonds': result.diamondsEarned,
        'points': result.pointsEarned,
      },
    );

    _addNotification(notification);
  }

  /// Notify about streak milestone
  void notifyStreakMilestone(int streakDays) {
    if (streakDays < 7) return; // Only notify for weekly milestones

    String title;
    String message;
    
    if (streakDays == 7) {
      title = 'Week Streak! 🔥';
      message = 'Amazing! You\'ve logged in for 7 days straight!';
    } else if (streakDays == 30) {
      title = 'Month Streak! 🏆';
      message = 'Incredible! 30 days of daily logins!';
    } else if (streakDays % 7 == 0) {
      title = '${streakDays ~/ 7} Week Streak! 🔥';
      message = 'You\'re on fire! $streakDays days of daily logins!';
    } else {
      return; // Don't notify for non-milestone days
    }

    final notification = NotificationModel(
      id: _generateNotificationId(),
      title: title,
      message: message,
      type: NotificationType.achievementUnlocked,
      timestamp: DateTime.now(),
      payload: {
        'type': 'streak_milestone',
        'streak_days': streakDays,
      },
    );

    _addNotification(notification);
  }

  /// Notify about streak broken
  void notifyStreakBroken(int previousStreak) {
    if (previousStreak < 3) return; // Only notify for meaningful streaks

    final notification = NotificationModel(
      id: _generateNotificationId(),
      title: 'Streak Broken 💔',
      message: 'Your $previousStreak-day streak ended. Start a new one today!',
      type: NotificationType.general,
      timestamp: DateTime.now(),
      payload: {
        'type': 'streak_broken',
        'previous_streak': previousStreak,
      },
    );

    _addNotification(notification);
  }

  // ==================== ACHIEVEMENT NOTIFICATIONS ====================

  /// Notify about achievement unlocked
  void notifyAchievementUnlocked(AchievementModel achievement) {
    final notification = NotificationModel(
      id: _generateNotificationId(),
      title: 'Achievement Unlocked! 🏆',
      message: '${achievement.title} - ${achievement.description}',
      type: NotificationType.achievementUnlocked,
      timestamp: DateTime.now(),
      payload: {
        'type': 'achievement_unlocked',
        'achievement_id': achievement.id,
        'achievement_title': achievement.title,
        'reward_points': achievement.rewardPoints,
        'reward_diamonds': achievement.rewardDiamonds,
      },
    );

    _addNotification(notification);
  }

  /// Notify about achievement progress milestone
  void notifyAchievementProgress(AchievementModel achievement, int previousProgress) {
    final progressPercentage = achievement.progressPercentage;
    final previousPercentage = previousProgress / achievement.target;
    
    // Notify at 25%, 50%, 75% milestones
    final milestones = [0.25, 0.5, 0.75];
    
    for (final milestone in milestones) {
      if (previousPercentage < milestone && progressPercentage >= milestone) {
        final percentage = (milestone * 100).toInt();
        
        final notification = NotificationModel(
          id: _generateNotificationId(),
          title: 'Achievement Progress! 📈',
          message: '${achievement.title} is $percentage% complete',
          type: NotificationType.general,
          timestamp: DateTime.now(),
          payload: {
            'type': 'achievement_progress',
            'achievement_id': achievement.id,
            'achievement_title': achievement.title,
            'progress_percentage': percentage,
          },
        );

        _addNotification(notification);
        break; // Only send one notification per update
      }
    }
  }

  /// Notify about achievement ready to claim
  void notifyAchievementReadyToClaim(AchievementModel achievement) {
    final notification = NotificationModel(
      id: _generateNotificationId(),
      title: 'Achievement Ready! 🎁',
      message: 'Claim your reward for ${achievement.title}',
      type: NotificationType.rewardsDistributed,
      timestamp: DateTime.now(),
      payload: {
        'type': 'achievement_ready',
        'achievement_id': achievement.id,
        'achievement_title': achievement.title,
        'reward_points': achievement.rewardPoints,
        'reward_diamonds': achievement.rewardDiamonds,
      },
    );

    _addNotification(notification);
  }

  // ==================== LOYALTY NOTIFICATIONS ====================

  /// Notify about loyalty tier upgrade
  void notifyLoyaltyTierUpgrade(LoyaltyTierModel newTier, LoyaltyTierModel? previousTier) {
    final notification = NotificationModel(
      id: _generateNotificationId(),
      title: 'Tier Upgrade! 🌟',
      message: previousTier != null 
          ? 'Congratulations! You\'ve been upgraded from ${previousTier.name} to ${newTier.name}'
          : 'Welcome to ${newTier.name} tier!',
      type: NotificationType.achievementUnlocked,
      timestamp: DateTime.now(),
      payload: {
        'type': 'loyalty_tier_upgrade',
        'new_tier_id': newTier.id,
        'new_tier_name': newTier.name,
        'previous_tier_id': previousTier?.id,
        'previous_tier_name': previousTier?.name,
      },
    );

    _addNotification(notification);
  }

  /// Notify about loyalty points earned
  void notifyLoyaltyPointsEarned(int points, LoyaltyAction action, String? description) {
    if (points <= 0) return;

    final notification = NotificationModel(
      id: _generateNotificationId(),
      title: 'Loyalty Points Earned! ⭐',
      message: description ?? 'You earned $points loyalty points from ${action.displayName}',
      type: NotificationType.rewardsDistributed,
      timestamp: DateTime.now(),
      payload: {
        'type': 'loyalty_points_earned',
        'points': points,
        'action': action.value,
        'description': description,
      },
    );

    _addNotification(notification);
  }

  /// Notify about approaching tier upgrade
  void notifyApproachingTierUpgrade(LoyaltyTierModel nextTier, int pointsNeeded) {
    if (pointsNeeded > 100) return; // Only notify when close

    final notification = NotificationModel(
      id: _generateNotificationId(),
      title: 'Almost There! 🚀',
      message: 'Only $pointsNeeded more points to reach ${nextTier.name} tier!',
      type: NotificationType.general,
      timestamp: DateTime.now(),
      payload: {
        'type': 'approaching_tier_upgrade',
        'next_tier_id': nextTier.id,
        'next_tier_name': nextTier.name,
        'points_needed': pointsNeeded,
      },
    );

    _addNotification(notification);
  }

  // ==================== GENERAL REWARD NOTIFICATIONS ====================

  /// Notify about reward expiration warning
  void notifyRewardExpiring(UserRewardModel reward, int daysUntilExpiry) {
    if (daysUntilExpiry > 7 || daysUntilExpiry < 1) return; // Only warn 1-7 days before

    final notification = NotificationModel(
      id: _generateNotificationId(),
      title: 'Reward Expiring Soon! ⏰',
      message: '${reward.reward?.title ?? 'Your reward'} expires in $daysUntilExpiry day${daysUntilExpiry == 1 ? '' : 's'}',
      type: NotificationType.general,
      timestamp: DateTime.now(),
      payload: {
        'type': 'reward_expiring',
        'reward_id': reward.id,
        'reward_title': reward.reward?.title,
        'days_until_expiry': daysUntilExpiry,
      },
    );

    _addNotification(notification);
  }

  /// Notify about special reward available
  void notifySpecialRewardAvailable(RewardModel reward) {
    final notification = NotificationModel(
      id: _generateNotificationId(),
      title: 'Special Reward Available! 🎉',
      message: '${reward.title} - Limited time offer!',
      type: NotificationType.rewardsDistributed,
      timestamp: DateTime.now(),
      payload: {
        'type': 'special_reward_available',
        'reward_id': reward.id,
        'reward_title': reward.title,
        'diamond_value': reward.diamondValue,
        'points': reward.points,
      },
    );

    _addNotification(notification);
  }

  /// Notify about reward claimed successfully
  void notifyRewardClaimed(RewardModel reward) {
    final notification = NotificationModel(
      id: _generateNotificationId(),
      title: 'Reward Claimed! ✅',
      message: 'You received ${reward.title}',
      type: NotificationType.rewardsDistributed,
      timestamp: DateTime.now(),
      payload: {
        'type': 'reward_claimed',
        'reward_id': reward.id,
        'reward_title': reward.title,
        'diamond_value': reward.diamondValue,
        'points': reward.points,
      },
    );

    _addNotification(notification);
  }

  // ==================== UTILITY METHODS ====================

  /// Check and send daily reward reminder (call this on app startup)
  void checkDailyRewardReminder(DailyRewardStatusModel? status) {
    if (status == null || status.hasClaimedToday) return;

    final currentDay = status.currentDayReward;
    if (currentDay != null) {
      // Schedule notification for later if not already claimed
      Future.delayed(const Duration(hours: 1), () {
        if (!status.hasClaimedToday) {
          final day = DailyRewardDay.fromData(
            day: status.streak.currentStreak,
            streak: status.streak,
            rewards: status.rewards,
            hasClaimedToday: status.hasClaimedToday,
          );
          notifyDailyRewardAvailable(day);
        }
      });
    }
  }

  /// Check for expiring rewards
  void checkExpiringRewards(List<UserRewardModel> rewards) {
    final now = DateTime.now();
    
    for (final reward in rewards) {
      if (reward.expiryDate != null) {
        final daysUntilExpiry = reward.expiryDate!.difference(now).inDays;
        notifyRewardExpiring(reward, daysUntilExpiry);
      }
    }
  }
}
