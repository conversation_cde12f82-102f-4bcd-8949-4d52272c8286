import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:wiggyz_app/app_themes.dart';
import 'package:wiggyz_app/providers/user_provider.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';
import 'package:wiggyz_app/theme_provider.dart';
import 'package:wiggyz_app/core/navigation/app_router.dart';
import 'package:wiggyz_app/core/services/supabase_service.dart';
import 'package:wiggyz_app/features/notifications/providers/notification_provider.dart';
import 'package:wiggyz_app/features/wallet/providers/wallet_provider.dart'; // Import WalletProvider
import 'package:wiggyz_app/services/cart_service.dart'; // Import CartService

import 'package:wiggyz_app/features/tournament_provider.dart'; // Import TournamentProvider
import 'package:wiggyz_app/features/winners_provider.dart'; // Import WinnersProvider
import 'package:wiggyz_app/features/streamers_provider.dart'; // Import StreamersProvider
import 'package:wiggyz_app/providers/user_joined_provider.dart'; // Import UserJoinedProvider
import 'package:wiggyz_app/providers/match_state_provider.dart'; // Import MatchStateProvider
import 'package:wiggyz_app/providers/tournament_match_state_provider.dart'; // Import TournamentMatchStateProvider
import 'package:wiggyz_app/providers/payment_provider.dart'; // Import PaymentProvider
import 'package:wiggyz_app/providers/reward_provider.dart'; // Import RewardProvider
import 'package:wiggyz_app/providers/match_history_provider.dart'; // Import MatchHistoryProvider

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system overlay style for status bar
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
    ),
  );

  // Initialize Supabase for fallback authentication
  await SupabaseService.initialize();

  // Create user provider instance
  final userProvider = UserProvider();
  // Initialize user data and await its completion
  await userProvider.init();

  // Create auth provider instance
  final authProvider = AuthProvider();

  // Create notification provider instance
  final notificationProvider = NotificationProvider();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider<ThemeProvider>(create: (_) => ThemeProvider()),
        ChangeNotifierProvider<UserProvider>(create: (_) => userProvider),
        ChangeNotifierProvider<AuthProvider>(create: (_) => authProvider),
        ChangeNotifierProvider<RewardProvider>(
          create: (_) => RewardProvider(authProvider.authService),
        ), // Add RewardProvider with AuthService dependency
        ChangeNotifierProvider<NotificationProvider>(
          create: (_) => notificationProvider..initialize(),
          lazy: false, // Initialize immediately
        ),
        ChangeNotifierProvider<WalletProvider>(
          create: (_) => WalletProvider(),
        ), // Add WalletProvider
        ChangeNotifierProvider<CartService>(
          create: (_) => CartService(),
        ), // Add CartService
        ChangeNotifierProvider<TournamentProvider>(
          create: (_) => TournamentProvider(),
        ), // Add TournamentProvider
        ChangeNotifierProvider<WinnersProvider>(
          create: (_) => WinnersProvider(),
        ), // Add WinnersProvider
        ChangeNotifierProvider<StreamersProvider>(
          create: (_) => StreamersProvider(),
        ), // Add StreamersProvider
        ChangeNotifierProvider<UserJoinedProvider>(
          create: (_) => UserJoinedProvider(),
        ), // Add UserJoinedProvider
        ChangeNotifierProvider<MatchStateProvider>(
          create: (_) => MatchStateProvider(),
        ), // Add MatchStateProvider
        ChangeNotifierProvider<TournamentMatchStateProvider>(
          create: (_) => TournamentMatchStateProvider(),
        ), // Add TournamentMatchStateProvider
        ChangeNotifierProvider<PaymentProvider>(
          create: (_) => PaymentProvider(),
        ), // Add PaymentProvider
        ChangeNotifierProvider<MatchHistoryProvider>(
          create: (_) => MatchHistoryProvider(authProvider),
        ), // Add MatchHistoryProvider
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    // Create the router instance
    final router = AppRouter.createRouter(context);

    return MaterialApp.router(
      title: 'Wiggyz Gaming',
      debugShowCheckedModeBanner: false,
      theme: AppThemes.lightTheme,
      darkTheme: AppThemes.darkTheme,
      themeMode: themeProvider.themeMode,
      routerConfig: router,
      // Router config replaces the need for home parameter
    );
  }
}
