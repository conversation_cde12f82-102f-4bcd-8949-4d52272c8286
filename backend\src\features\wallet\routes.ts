/**
 * Wallet routes for handling wallet-related API endpoints
 */
import express from 'express';
import {
  getWalletDetails,
  getTransactionHistory,
  initiateTopUp,
  processPaymentWebhook,
  initiateWithdrawal,
  addBalance,
  verifyPayment
} from './controllers/walletController';
import { processRazorpayWebhook } from './controllers/webhookController';
import { authenticate } from '../../middleware/auth';
import {
  paymentInitiationRateLimit,
  paymentVerificationRateLimit,
  webhookRateLimit,
  validatePaymentAmount,
  validateWithdrawalAmount,
  auditPaymentOperation,
  detectSuspiciousPaymentActivity,
  checkUserBlocked
} from '../../middleware/paymentSecurity';

const router = express.Router();

// Protected routes requiring authentication
router.get('/', authenticate, getWalletDetails);
router.get('/transactions', authenticate, getTransactionHistory);

// Payment routes with enhanced security
router.post('/topup',
  authenticate,
  checkUserBlocked,
  paymentInitiationRateLimit,
  validatePaymentAmount,
  auditPaymentOperation('payment_initiation'),
  initiateTopUp
);

router.post('/verify-payment',
  authenticate,
  checkUserBlocked,
  paymentVerificationRateLimit,
  auditPaymentOperation('payment_verification'),
  detectSuspiciousPaymentActivity,
  verifyPayment
);

router.post('/withdraw',
  authenticate,
  checkUserBlocked,
  paymentInitiationRateLimit,
  validateWithdrawalAmount,
  auditPaymentOperation('withdrawal_initiation'),
  initiateWithdrawal
);

router.post('/add-balance',
  authenticate,
  validatePaymentAmount,
  auditPaymentOperation('manual_balance_addition'),
  addBalance
); // For testing/admin purposes

// Webhook routes (secured by signature verification)
router.post('/webhook/razorpay',
  webhookRateLimit,
  auditPaymentOperation('razorpay_webhook'),
  processRazorpayWebhook
);

// Generic webhook route for backward compatibility
router.post('/webhook/:gateway',
  webhookRateLimit,
  auditPaymentOperation('webhook_processing'),
  processPaymentWebhook
);

export default router;
