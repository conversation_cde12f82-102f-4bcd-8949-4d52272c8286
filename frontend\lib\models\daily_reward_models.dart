/// Daily reward system models for the WiggyZ app
/// These models handle daily login rewards, streaks, and reward claiming
library;

import 'package:flutter/foundation.dart';
import 'reward_models.dart';

/// Model for daily reward status response from API
class DailyRewardStatusModel {
  final LoginStreakModel streak;
  final List<RewardModel> rewards;
  final bool hasClaimedToday;

  const DailyRewardStatusModel({
    required this.streak,
    required this.rewards,
    required this.hasClaimedToday,
  });

  factory DailyRewardStatusModel.fromJson(Map<String, dynamic> json) {
    try {
      return DailyRewardStatusModel(
        streak: LoginStreakModel.fromJson(json['streak'] as Map<String, dynamic>),
        rewards: (json['rewards'] as List)
            .map((reward) => RewardModel.fromJson(reward as Map<String, dynamic>))
            .toList(),
        hasClaimedToday: json['hasClaimedToday'] as bool? ?? false,
      );
    } catch (e) {
      debugPrint('Error parsing DailyRewardStatusModel: $e');
      debugPrint('JSON data: $json');
      // Return a default model to prevent crashes
      return DailyRewardStatusModel(
        streak: LoginStreakModel(
          id: 'default-streak',
          userId: 'current-user',
          currentStreak: 0,
          longestStreak: 0,
          lastLoginDate: null,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        rewards: [],
        hasClaimedToday: false,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'streak': streak.toJson(),
      'rewards': rewards.map((reward) => reward.toJson()).toList(),
      'hasClaimedToday': hasClaimedToday,
    };
  }

  /// Get the reward for a specific day
  RewardModel? getRewardForDay(int day) {
    return rewards.where((reward) => reward.dayRequirement == day).firstOrNull;
  }

  /// Get the current day's reward
  RewardModel? get currentDayReward {
    return getRewardForDay(streak.currentStreak);
  }

  /// Get all rewards sorted by day
  List<RewardModel> get sortedRewards {
    final sorted = List<RewardModel>.from(rewards);
    sorted.sort((a, b) {
      final dayA = a.dayRequirement ?? 0;
      final dayB = b.dayRequirement ?? 0;
      return dayA.compareTo(dayB);
    });
    return sorted;
  }

  /// Check if user can claim today's reward
  bool get canClaimToday {
    return !hasClaimedToday && currentDayReward != null;
  }

  /// Get the maximum day available in rewards
  int get maxRewardDay {
    return rewards
        .map((reward) => reward.dayRequirement ?? 0)
        .fold(0, (max, day) => day > max ? day : max);
  }
}

/// Model for daily reward claim response
class DailyRewardClaimResult {
  final bool success;
  final String? error;
  final RewardModel? claimedReward;
  final LoginStreakModel? updatedStreak;
  final int? diamondsEarned;
  final int? pointsEarned;

  const DailyRewardClaimResult({
    required this.success,
    this.error,
    this.claimedReward,
    this.updatedStreak,
    this.diamondsEarned,
    this.pointsEarned,
  });

  factory DailyRewardClaimResult.fromJson(Map<String, dynamic> json) {
    return DailyRewardClaimResult(
      success: json['success'] as bool? ?? false,
      error: json['error'] as String?,
      claimedReward: json['claimedReward'] != null
          ? RewardModel.fromJson(json['claimedReward'] as Map<String, dynamic>)
          : null,
      updatedStreak: json['updatedStreak'] != null
          ? LoginStreakModel.fromJson(json['updatedStreak'] as Map<String, dynamic>)
          : null,
      diamondsEarned: json['diamondsEarned'] as int?,
      pointsEarned: json['pointsEarned'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'error': error,
      'claimedReward': claimedReward?.toJson(),
      'updatedStreak': updatedStreak?.toJson(),
      'diamondsEarned': diamondsEarned,
      'pointsEarned': pointsEarned,
    };
  }
}

/// Model for a single day in the daily rewards calendar
class DailyRewardDay {
  final int day;
  final RewardModel? reward;
  final bool isClaimed;
  final bool isCurrentDay;
  final bool isAvailable;
  final bool isFutureDay;

  const DailyRewardDay({
    required this.day,
    this.reward,
    required this.isClaimed,
    required this.isCurrentDay,
    required this.isAvailable,
    required this.isFutureDay,
  });

  /// Create a daily reward day from streak and rewards data
  factory DailyRewardDay.fromData({
    required int day,
    required LoginStreakModel streak,
    required List<RewardModel> rewards,
    required bool hasClaimedToday,
  }) {
    try {
      final reward = rewards.where((r) => r.dayRequirement == day).firstOrNull;
      final currentStreak = streak.currentStreak;

      final isCurrentDay = day == currentStreak;
      final isAvailable = day <= currentStreak;
      final isFutureDay = day > currentStreak;
      final isClaimed = isAvailable && (day < currentStreak || (day == currentStreak && hasClaimedToday));

      debugPrint('📅 Day $day: reward=${reward != null}, currentStreak=$currentStreak, isCurrentDay=$isCurrentDay, isClaimed=$isClaimed');

      return DailyRewardDay(
        day: day,
        reward: reward,
        isClaimed: isClaimed,
        isCurrentDay: isCurrentDay,
        isAvailable: isAvailable,
        isFutureDay: isFutureDay,
      );
    } catch (e) {
      debugPrint('❌ Error creating DailyRewardDay for day $day: $e');
      // Return a safe default
      return DailyRewardDay(
        day: day,
        reward: null,
        isClaimed: false,
        isCurrentDay: false,
        isAvailable: false,
        isFutureDay: true,
      );
    }
  }

  /// Get the display status for this day
  DailyRewardDayStatus get status {
    if (isClaimed) return DailyRewardDayStatus.claimed;
    if (isCurrentDay) return DailyRewardDayStatus.available;
    if (isAvailable) return DailyRewardDayStatus.missed;
    return DailyRewardDayStatus.locked;
  }

  /// Check if this day can be claimed
  bool get canClaim {
    return isCurrentDay && !isClaimed && reward != null;
  }
}

/// Enum for daily reward day status
enum DailyRewardDayStatus {
  claimed,    // Already claimed
  available,  // Can be claimed today
  missed,     // Was available but not claimed
  locked,     // Not yet available
}

/// Extension for daily reward day status
extension DailyRewardDayStatusExtension on DailyRewardDayStatus {
  String get displayName {
    switch (this) {
      case DailyRewardDayStatus.claimed:
        return 'Claimed';
      case DailyRewardDayStatus.available:
        return 'Available';
      case DailyRewardDayStatus.missed:
        return 'Missed';
      case DailyRewardDayStatus.locked:
        return 'Locked';
    }
  }

  bool get isInteractive {
    return this == DailyRewardDayStatus.available;
  }
}

/// Model for daily rewards calendar
class DailyRewardsCalendar {
  final List<DailyRewardDay> days;
  final LoginStreakModel streak;
  final bool hasClaimedToday;

  const DailyRewardsCalendar({
    required this.days,
    required this.streak,
    required this.hasClaimedToday,
  });

  /// Create calendar from daily reward status
  factory DailyRewardsCalendar.fromStatus(DailyRewardStatusModel status) {
    try {
      debugPrint('📅 Creating calendar from status...');
      debugPrint('📊 Streak: ${status.streak.currentStreak}, Rewards: ${status.rewards.length}');

      final maxDay = status.maxRewardDay;
      debugPrint('📊 Max reward day: $maxDay');

      if (maxDay <= 0) {
        debugPrint('⚠️ No rewards configured (maxDay: $maxDay)');
        // Return empty calendar if no rewards are configured
        return DailyRewardsCalendar(
          days: [],
          streak: status.streak,
          hasClaimedToday: status.hasClaimedToday,
        );
      }

      final days = <DailyRewardDay>[];

      for (int day = 1; day <= maxDay; day++) {
        try {
          final dayData = DailyRewardDay.fromData(
            day: day,
            streak: status.streak,
            rewards: status.rewards,
            hasClaimedToday: status.hasClaimedToday,
          );
          days.add(dayData);
          debugPrint('📅 Created day $day: canClaim=${dayData.canClaim}, isClaimed=${dayData.isClaimed}');
        } catch (dayError) {
          debugPrint('❌ Error creating day $day: $dayError');
          // Continue with other days even if one fails
        }
      }

      debugPrint('✅ Calendar created with ${days.length} days');
      return DailyRewardsCalendar(
        days: days,
        streak: status.streak,
        hasClaimedToday: status.hasClaimedToday,
      );
    } catch (e) {
      debugPrint('💥 Error creating calendar: $e');
      debugPrint('📊 Status data: streak=${status.streak.currentStreak}, rewards=${status.rewards.length}');
      rethrow;
    }
  }

  /// Get the current day
  DailyRewardDay? get currentDay {
    return days.where((day) => day.isCurrentDay).firstOrNull;
  }

  /// Get claimed days count
  int get claimedDaysCount {
    return days.where((day) => day.isClaimed).length;
  }

  /// Get available days count
  int get availableDaysCount {
    return days.where((day) => day.isAvailable).length;
  }

  /// Get total days count
  int get totalDaysCount => days.length;

  /// Get progress percentage
  double get progressPercentage {
    if (totalDaysCount == 0) return 0.0;
    return claimedDaysCount / totalDaysCount;
  }

  /// Check if user can claim any reward today
  bool get canClaimToday {
    return currentDay?.canClaim ?? false;
  }

  /// Get next available reward
  DailyRewardDay? get nextAvailableReward {
    return days.where((day) => day.status == DailyRewardDayStatus.available).firstOrNull;
  }
}
