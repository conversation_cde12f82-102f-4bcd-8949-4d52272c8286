import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'package:wiggyz_app/providers/user_provider.dart';
import 'package:wiggyz_app/providers/reward_provider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:wiggyz_app/models/game_model.dart';
import 'package:wiggyz_app/features/tournament_models.dart' as api_models;
import 'package:wiggyz_app/features/tournament_provider.dart';
import 'package:wiggyz_app/features/winners_provider.dart';
import 'package:wiggyz_app/features/winners_service.dart';
import 'package:wiggyz_app/features/streamers_provider.dart';
import 'package:wiggyz_app/screens/notification_screen.dart';
import 'package:wiggyz_app/screens/streamer_details_screen.dart';
import 'package:wiggyz_app/screens/tournament_details_screen_new.dart';
import 'package:wiggyz_app/screens/popular_game_details_screen.dart';
import 'package:wiggyz_app/screens/all_popular_games_screen.dart';
import 'package:wiggyz_app/screens/recent_winners_screen.dart';
import 'package:wiggyz_app/screens/games_screen_new_fixed.dart';
import 'package:wiggyz_app/screens/main_navigation.dart';
import 'package:wiggyz_app/services/games_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  int _featuredCarouselIndex = 0;
  late TabController _tabController;
  final GamesService _gamesService = GamesService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Fetch real tournament and matches data on initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final tournamentProvider = Provider.of<TournamentProvider>(
          context,
          listen: false,
        );
        tournamentProvider.fetchTournaments();
        tournamentProvider
            .fetchAllMatches(); // Fetch all matches for upcoming matches section

        // Fetch recent winners data
        final winnersProvider = Provider.of<WinnersProvider>(
          context,
          listen: false,
        );
        winnersProvider.fetchRecentWinners(limit: 6);

        // Fetch live streamers data
        final streamersProvider = Provider.of<StreamersProvider>(
          context,
          listen: false,
        );
        streamersProvider.fetchLiveStreamers(limit: 3);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: RefreshIndicator(
        onRefresh: () async {
          // Refresh tournament and matches data from API
          final tournamentProvider = Provider.of<TournamentProvider>(
            context,
            listen: false,
          );
          final winnersProvider = Provider.of<WinnersProvider>(
            context,
            listen: false,
          );
          final streamersProvider = Provider.of<StreamersProvider>(
            context,
            listen: false,
          );
          await Future.wait([
            tournamentProvider.fetchTournaments(),
            tournamentProvider.fetchAllMatches(),
            winnersProvider.fetchRecentWinners(limit: 6),
            streamersProvider.fetchLiveStreamers(limit: 3),
          ]);
        },
        color: Theme.of(context).primaryColor,
        backgroundColor: Theme.of(context).colorScheme.secondary,
        child: CustomScrollView(
          slivers: [
            _buildSliverAppBar(context),
            SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 10),
                  _buildFeaturedTournamentsCarousel(),
                  const SizedBox(height: 20),
                  _buildPopularGames(context),
                  const SizedBox(height: 20),
                ],
              ),
            ),
            _buildTournamentsSliverSection(context),
            SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 5),
                  _buildUpcomingMatchesSection(),
                  const SizedBox(height: 20),
                  _buildLiveStreamersSection(),
                  const SizedBox(height: 20),
                  _buildRecentWinnersSection(),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSliverAppBar(BuildContext context) {
    return SliverAppBar(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      elevation: 0,
      floating: true,
      automaticallyImplyLeading: false,
      expandedHeight: 60,
      flexibleSpace: FlexibleSpaceBar(
        title: SafeArea(
          child: Container(
            constraints: const BoxConstraints(maxHeight: 56), // Standard app bar height
            alignment: Alignment.centerLeft,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
            Consumer<UserProvider>(
              builder: (context, userProvider, _) {
                return Container(
                  width: 32,
                  height: 32,
                  constraints: const BoxConstraints(
                    maxWidth: 32,
                    maxHeight: 32,
                  ),
                  child: Stack(
                    clipBehavior: Clip.hardEdge,
                    children: [
                      // Base circle with user initials as fallback
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Consumer<UserProvider>(
                            builder: (context, userProvider, _) {
                              // Generate initials from username
                              String initials = 'WZ';

                              if (userProvider.profile?.username != null &&
                                  userProvider.profile!.username!.isNotEmpty) {
                                final nameParts = userProvider
                                    .profile!
                                    .username!
                                    .split(' ');
                                initials = nameParts
                                    .map(
                                      (part) =>
                                          part.isNotEmpty
                                              ? part[0].toUpperCase()
                                              : '',
                                    )
                                    .take(2)
                                    .join('');
                              } else if (userProvider.username.isNotEmpty) {
                                final nameParts = userProvider.username.split(
                                  ' ',
                                );
                                initials = nameParts
                                    .map(
                                      (part) =>
                                          part.isNotEmpty
                                              ? part[0].toUpperCase()
                                              : '',
                                    )
                                    .take(2)
                                    .join('');
                              }

                              if (initials.isEmpty) initials = 'WZ';

                              return Text(
                                initials,
                                style: GoogleFonts.poppins(
                                  color: Colors.black,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              );
                            },
                          ),
                        ),
                      ), // Profile image overlay
                      if (userProvider.profileImageUrl.isNotEmpty)
                        ClipOval(
                          child: CachedNetworkImage(
                            imageUrl: userProvider.profileImageUrl,
                            width: 32,
                            height: 32,
                            fit: BoxFit.cover,
                            placeholder:
                                (context, url) => Container(
                                  width: 32,
                                  height: 32,
                                  color: Colors.grey[300],
                                  child: const Center(
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  ),
                                ),
                            errorWidget:
                                (context, url, error) => Container(
                                  width: 32,
                                  height: 32,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).primaryColor,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Consumer<UserProvider>(
                                      builder: (context, userProvider, _) {
                                        // Generate initials from username
                                        String initials = 'WZ';

                                        if (userProvider.profile?.username !=
                                                null &&
                                            userProvider
                                                .profile!
                                                .username!
                                                .isNotEmpty) {
                                          final nameParts = userProvider
                                              .profile!
                                              .username!
                                              .split(' ');
                                          initials = nameParts
                                              .map(
                                                (part) =>
                                                    part.isNotEmpty
                                                        ? part[0].toUpperCase()
                                                        : '',
                                              )
                                              .take(2)
                                              .join('');
                                        } else if (userProvider
                                            .username
                                            .isNotEmpty) {
                                          final nameParts = userProvider
                                              .username
                                              .split(' ');
                                          initials = nameParts
                                              .map(
                                                (part) =>
                                                    part.isNotEmpty
                                                        ? part[0].toUpperCase()
                                                        : '',
                                              )
                                              .take(2)
                                              .join('');
                                        }

                                        if (initials.isEmpty) initials = 'WZ';

                                        return Text(
                                          initials,
                                          style: GoogleFonts.poppins(
                                            color: Colors.black,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ),
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Container(
                constraints: const BoxConstraints(maxHeight: 48), // Ensure text stays within bounds
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                  Text(
                    'Welcome back,',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  Consumer<UserProvider>(
                    builder: (context, userProvider, _) {
                      // Get first name from profile or username
                      String displayName = 'User';

                      if (userProvider.profile?.username != null &&
                          userProvider.profile!.username!.isNotEmpty) {
                        // Extract first name from username/name
                        final nameParts = userProvider.profile!.username!.split(
                          ' ',
                        );
                        displayName = nameParts.first;
                      } else if (userProvider.username.isNotEmpty) {
                        // Fallback to username and extract first part
                        final nameParts = userProvider.username.split(' ');
                        displayName = nameParts.first;
                      }

                      return Text(
                        displayName,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      );
                    },
                  ),
                ],
                ),
              ),
            ),
          ],
            ),
          ),
        ),
        titlePadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      ),
      actions: [
        Consumer<RewardProvider>(
          builder: (context, rewardProvider, _) {
            final canClaim = rewardProvider.canClaimDailyReward ||
                rewardProvider.readyToClaimAchievements.isNotEmpty;

            return IconButton(
              icon: Stack(
                clipBehavior: Clip.none,
                children: [
                  Icon(
                    Icons.card_giftcard_outlined,
                    color: Theme.of(context).colorScheme.onSurface,
                    size: 24,
                  ),
                  if (canClaim)
                    Positioned(
                      top: -2,
                      right: -2,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFCC00),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                ],
              ),
              onPressed: () {
                // Navigate to rewards tab
                MainNavigation.navigateToTab(context, 2);
              },
            );
          },
        ),
        IconButton(
          icon: Icon(
            Icons.notifications_outlined,
            color: Theme.of(context).colorScheme.onSurface,
            size: 24,
          ),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const NotificationScreen(),
              ),
            );
          },
        ),
        const SizedBox(width: 10),
      ],
    );
  }

  Widget _buildFeaturedTournamentsCarousel() {
    return Consumer<TournamentProvider>(
      builder: (context, tournamentProvider, child) {
        if (tournamentProvider.isLoadingTournaments) {
          return SizedBox(
            height: 200,
            child: Center(
              child: CircularProgressIndicator(
                color: Theme.of(context).primaryColor,
              ),
            ),
          );
        }

        if (tournamentProvider.tournamentsError != null) {
          return SizedBox(
            height: 200,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Error loading tournaments',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      color: Colors.grey[400],
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () => tournamentProvider.fetchTournaments(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        // Filter for featured tournaments (you can adjust this logic based on your API)
        final featuredTournaments =
            tournamentProvider.tournaments
                .where((t) => t.status == 'active' || t.status == 'upcoming')
                .take(5)
                .toList();

        if (featuredTournaments.isEmpty) {
          return SizedBox(
            height: 200,
            child: Center(
              child: Text(
                'No featured tournaments available.',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          );
        }

        return Column(
          children: [
            CarouselSlider.builder(
              itemCount: featuredTournaments.length,
              itemBuilder: (context, index, realIndex) {
                final tournament = featuredTournaments[index];
                return _buildFeaturedApiTournamentCard(tournament);
              },
              options: CarouselOptions(
                height: 200,
                autoPlay: true,
                enlargeCenterPage: true,
                aspectRatio: 16 / 9,
                viewportFraction: 0.85,
                onPageChanged: (index, reason) {
                  setState(() {
                    _featuredCarouselIndex = index;
                  });
                },
              ),
            ),
            const SizedBox(height: 10),
            AnimatedSmoothIndicator(
              activeIndex: _featuredCarouselIndex,
              count: featuredTournaments.length,
              effect: ExpandingDotsEffect(
                dotHeight: 8,
                dotWidth: 8,
                activeDotColor: Theme.of(context).primaryColor,
                dotColor: Colors.grey[700]!,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFeaturedApiTournamentCard(api_models.Tournament tournament) {
    return GestureDetector(
      onTap: () {
        // Navigate to new tournament details screen that uses API data
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) =>
                    TournamentDetailsScreenNew(tournamentId: tournament.id),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 5),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: const LinearGradient(
            colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment:
                MainAxisAlignment.spaceBetween, // Added to distribute space
            children: [
              Expanded(
                // Wrap the Row with Expanded
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment:
                      CrossAxisAlignment.start, // Align items to the start
                  children: [
                    Expanded(
                      child: Text(
                        tournament.name,
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        maxLines: 2, // Allow up to 2 lines
                        overflow:
                            TextOverflow
                                .ellipsis, // Show ellipsis if text overflows
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        tournament.game?.name ?? 'N/A',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8), // Add some space
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Prize: ${tournament.prizePool ?? 'N/A'}',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.black.withOpacity(0.8),
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Entry: ${tournament.entryFee ?? 'Free'}',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.black.withOpacity(0.8),
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPopularGames(BuildContext context) {
    return FutureBuilder<List<Game>>(
      future: _gamesService.getPopularGames(limit: 6),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildPopularGamesLoading();
        }

        if (snapshot.hasError) {
          debugPrint('Error loading popular games: ${snapshot.error}');
          // Fallback to hardcoded games on error
          return _buildPopularGamesContent(_getFallbackGames());
        }

        final popularGames = snapshot.data ?? _getFallbackGames();
        return _buildPopularGamesContent(popularGames);
      },
    );
  }

  List<Game> _getFallbackGames() {
    return [
      Game(
        id: '1',
        name: 'Fortnite',
        icon: FontAwesomeIcons.gamepad,
        imageUrl:
            'https://cdn2.unrealengine.com/fortnite-chapter-4-season-4-key-art-1920x1080-d35912cc5ebb.jpg',
        description: 'Battle Royale',
        playersOnline: 400000000,
        totalDownloads: '400M+',
      ),
      Game(
        id: '2',
        name: 'Valorant',
        icon: FontAwesomeIcons.crosshairs,
        imageUrl:
            'https://cmsassets.rgpub.io/sanity/images/dsfx7636/news/95d6db3095e11be3964a0d7ad4ff5ce5a8978d2e-1920x1080.jpg',
        description: 'Tactical Shooter',
        playersOnline: 15000000,
        totalDownloads: '15M+',
      ),
      Game(
        id: '3',
        name: 'CS:GO',
        icon: FontAwesomeIcons.bullseye,
        imageUrl:
            'https://cdn.cloudflare.steamstatic.com/steam/apps/730/capsule_616x353.jpg',
        description: 'FPS',
        playersOnline: 26000000,
        totalDownloads: '26M+',
      ),
      Game(
        id: '4',
        name: 'League of Legends',
        icon: FontAwesomeIcons.trophy,
        imageUrl:
            'https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_0.jpg',
        description: 'MOBA',
        playersOnline: 150000000,
        totalDownloads: '150M+',
      ),
    ];
  }

  Widget _buildPopularGamesLoading() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Popular Games',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(width: 60), // Placeholder for "See All" button
            ],
          ),
          const SizedBox(height: 15),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: 4,
              itemBuilder: (context, index) {
                return Container(
                  width: 100,
                  margin: const EdgeInsets.only(right: 15),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Center(child: CircularProgressIndicator()),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPopularGamesContent(List<Game> popularGames) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Popular Games',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (context) =>
                              AllPopularGamesScreen(popularGames: popularGames),
                    ),
                  );
                },
                child: Text(
                  'View All',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              itemCount: popularGames.length,
              itemBuilder: (context, index) {
                final game = popularGames[index];
                return _buildGameCard(game);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameCard(Game game) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PopularGameDetailsScreen(game: game),
          ),
        );
      },
      child: Container(
        width: 100,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          image:
              game.imageUrl != null
                  ? DecorationImage(
                    image: NetworkImage(game.imageUrl!),
                    fit: BoxFit.cover,
                  )
                  : null,
          color:
              game.imageUrl == null
                  ? Theme.of(context).primaryColor.withOpacity(0.3)
                  : null,
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.transparent, Colors.black.withOpacity(0.7)],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  game.name,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  game.totalDownloads ?? 'N/A',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTournamentsSliverSection(BuildContext context) {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tournaments',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 15),
            Container(
              height: 45,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(25),
              ),
              child: TabBar(
                controller: _tabController,
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  color: Theme.of(context).primaryColor,
                ),
                labelColor: Colors.black,
                unselectedLabelColor: Theme.of(
                  context,
                ).colorScheme.onSurface.withOpacity(0.6),
                labelStyle: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.normal,
                ),
                tabs: const [
                  Tab(text: 'Upcoming'),
                  Tab(text: 'Live'),
                  Tab(text: 'Completed'),
                ],
              ),
            ),
            const SizedBox(height: 15),
            // Replace TabBarView with AnimatedBuilder to avoid nested scrolling
            SizedBox(
              height: 250,
              child: AnimatedBuilder(
                animation: _tabController,
                builder: (context, child) {
                  int tabIndex = _tabController.index;
                  String status =
                      tabIndex == 0
                          ? 'upcoming'
                          : tabIndex == 1
                          ? 'active'
                          : 'completed';
                  return _buildTournamentList(status);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTournamentList(String status) {
    return Consumer<TournamentProvider>(
      builder: (context, tournamentProvider, child) {
        if (tournamentProvider.isLoadingTournaments) {
          return const Center(child: CircularProgressIndicator());
        }

        final tournaments =
            tournamentProvider.tournaments
                .where((t) => t.status == status)
                .take(5)
                .toList();

        if (tournaments.isEmpty) {
          return Center(
            child: Text(
              'No $status tournaments',
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[400]),
            ),
          );
        }

        return ListView.builder(
          physics: const BouncingScrollPhysics(),
          itemCount: tournaments.length,
          itemBuilder: (context, index) {
            final tournament = tournaments[index];
            return _buildTournamentCard(tournament);
          },
        );
      },
    );
  }

  Widget _buildTournamentCard(api_models.Tournament tournament) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) =>
                    TournamentDetailsScreenNew(tournamentId: tournament.id),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                gradient: const LinearGradient(
                  colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
                ),
              ),
              child: const Icon(
                FontAwesomeIcons.trophy,
                color: Colors.black,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    tournament.name,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Prize: Rs. ${tournament.prizePool ?? '0'}',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Players: ${tournament.currentParticipants}/${tournament.maxParticipants}',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getStatusColor(tournament.status),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                tournament.status.toUpperCase(),
                style: GoogleFonts.poppins(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return Colors.green;
      case 'upcoming':
        return Colors.orange;
      case 'completed':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  Widget _buildUpcomingMatchesSection() {
    return Consumer<TournamentProvider>(
      builder: (context, tournamentProvider, child) {
        if (tournamentProvider.isLoadingAllMatches) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Section Header with enhanced design
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: const LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFFFFCC00).withOpacity(0.3),
                        offset: const Offset(0, 2),
                        blurRadius: 6,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Row(
                    children: [
                      const Icon(
                        FontAwesomeIcons.calendar,
                        color: Colors.black87,
                        size: 18,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'Upcoming Matches',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 15),
                const Center(child: CircularProgressIndicator()),
              ],
            ),
          );
        }

        if (tournamentProvider.allMatchesError != null) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Section Header with enhanced design
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: const LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFFFFCC00).withOpacity(0.3),
                        offset: const Offset(0, 2),
                        blurRadius: 6,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Row(
                    children: [
                      const Icon(
                        FontAwesomeIcons.calendar,
                        color: Colors.black87,
                        size: 18,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'Upcoming Matches',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 15),
                Center(
                  child: Text(
                    'Error loading matches: ${tournamentProvider.allMatchesError}',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[400],
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        // Filter for upcoming matches, take max 5
        final upcomingMatches =
            tournamentProvider.allMatches
                .where(
                  (match) =>
                      match.status.toLowerCase() == 'scheduled' ||
                      match.status.toLowerCase() == 'upcoming',
                )
                .take(5)
                .toList();

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enhanced section header with View All button
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: const LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFFFFCC00).withOpacity(0.3),
                      offset: const Offset(0, 2),
                      blurRadius: 6,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 10,
                ),
                margin: const EdgeInsets.only(bottom: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          FontAwesomeIcons.calendar,
                          color: Colors.black87,
                          size: 18,
                        ),
                        const SizedBox(width: 10),
                        Text(
                          'Upcoming Matches',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                    if (upcomingMatches.isNotEmpty)
                      Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            // Navigate to GamesScreen with Matches tab (index 1) selected
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) =>
                                        const GamesScreen(initialTabIndex: 1),
                              ),
                            );
                          },
                          borderRadius: BorderRadius.circular(20),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'View All',
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    color: Colors.black87,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                const Icon(
                                  Icons.arrow_forward_ios,
                                  size: 12,
                                  color: Colors.black87,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),

              if (upcomingMatches.isEmpty)
                Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20.0),
                    child: Text(
                      'No upcoming matches.',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[400],
                      ),
                    ),
                  ),
                )
              else
                ...upcomingMatches.map((match) => _buildMatchCard(match)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMatchCard(api_models.Match match) {
    final gameName = match.game?.name ?? 'N/A';
    final participant1Name = match.participant1?.username ?? 'TBD';
    final participant2Name = match.participant2?.username ?? 'TBD';
    final matchTimeFormatted =
        match.matchTime != null
            ? _formatDateTime(match.matchTime!)
            : 'Time TBD';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF1F1F1F)
                : Colors.white,
            Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF2A2A2A)
                : const Color(0xFFF5F5F5),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? Colors.black.withOpacity(0.2)
                    : Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? Colors.amber.withOpacity(0.3)
                  : const Color(0xFFFFCC00).withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              // Navigate to match details when clicked
              // You can implement this in the future
            },
            splashColor: Theme.of(context).primaryColor.withOpacity(0.1),
            highlightColor: Theme.of(context).primaryColor.withOpacity(0.05),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Game name and status row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Icon(
                            FontAwesomeIcons.gamepad,
                            size: 16,
                            color: Color(0xFFFFCC00),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            gameName,
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ],
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 5,
                        ),
                        decoration: BoxDecoration(
                          color: _getMatchStatusColor(match.status),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: _getMatchStatusColor(
                                match.status,
                              ).withOpacity(0.3),
                              spreadRadius: 1,
                              blurRadius: 4,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Text(
                          match.status.toUpperCase() ?? 'UNKNOWN',
                          style: GoogleFonts.poppins(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Players/teams container
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.black12
                              : Colors.white.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.grey.withOpacity(0.2)
                                : Colors.grey.withOpacity(0.1),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Text(
                            participant1Name,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 5,
                          ),
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFCC00),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'VS',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            participant2Name,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Tournament and date/time
                  Row(
                    children: [
                      const Icon(
                        FontAwesomeIcons.trophy,
                        size: 14,
                        color: Color(0xFFFFCC00),
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          'Tournament: ${match.tournamentId}',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withOpacity(0.7),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        size: 14,
                        color: Color(0xFFFFCC00),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        matchTimeFormatted,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),

                  // Entry Fee and Prize Pool Section
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.black12
                              : const Color(0xFFF8F9FA),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.grey.withOpacity(0.2)
                                : Colors.grey.withOpacity(0.1),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Entry Fee
                        Row(
                          children: [
                            const Icon(
                              Icons.attach_money,
                              size: 14,
                              color: Color(0xFFFFCC00),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Entry: ${match.entryFee != null ? '₹${match.entryFee!.toStringAsFixed(0)}' : 'Free'}',
                              style: GoogleFonts.poppins(
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurface.withOpacity(0.8),
                              ),
                            ),
                          ],
                        ),
                        // Prize Pool
                        Row(
                          children: [
                            const Icon(
                              Icons.emoji_events,
                              size: 14,
                              color: Color(0xFFFFCC00),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Prize: ${match.prizePool != null ? '₹${match.prizePool!.toStringAsFixed(0)}' : 'TBD'}',
                              style: GoogleFonts.poppins(
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurface.withOpacity(0.8),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Color _getMatchStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'scheduled':
      case 'upcoming':
        return Colors.blueAccent;
      case 'live':
        return Colors.redAccent;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.grey;
      default:
        return Colors.orange;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    // Format the date in a more readable way
    final now = DateTime.now();
    final diff = dateTime.difference(now);

    // Format hour and minute with leading zeros
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final time = '$hour:$minute';

    if (diff.inDays == 0) {
      // Today
      return 'Today at $time';
    } else if (diff.inDays == 1) {
      // Tomorrow
      return 'Tomorrow at $time';
    } else if (diff.inDays < 7) {
      // Within a week
      final List<String> weekdays = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday',
      ];
      final weekday = weekdays[dateTime.weekday - 1]; // weekday is 1-7
      return '$weekday at $time';
    } else {
      // More than a week
      final day = dateTime.day.toString().padLeft(2, '0');
      final month = dateTime.month.toString().padLeft(2, '0');
      return '$day/$month/${dateTime.year} at $time';
    }
  }

  Widget _buildLiveStreamersSection() {
    return Consumer<StreamersProvider>(
      builder: (context, streamersProvider, child) {
        if (streamersProvider.isLoadingLive) {
          return _buildLoadingStreamersSection();
        }

        if (streamersProvider.hasError) {
          return _buildErrorStreamersSection(streamersProvider.error!);
        }

        final streamers = streamersProvider.liveStreamers;

        if (streamers.isEmpty) {
          return _buildEmptyStreamersSection();
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Live Streamers',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      // Navigate to view all live streamers
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'View all streamers feature coming soon!',
                          ),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    },
                    child: Text(
                      'View All',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 15),
              SizedBox(
                height: 180,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  physics: const BouncingScrollPhysics(),
                  clipBehavior: Clip.none,
                  itemCount: streamers.length,
                  itemBuilder: (context, index) {
                    final streamer = streamers[index];
                    return _buildStreamerCard(streamer);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingStreamersSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Live Streamers',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 15),
          SizedBox(
            height: 180,
            child: Center(
              child: CircularProgressIndicator(
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorStreamersSection(String error) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Live Streamers',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 15),
          SizedBox(
            height: 180,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    FontAwesomeIcons.exclamationTriangle,
                    color: Colors.red,
                    size: 24,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Unable to load streamers',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[400],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Please try again later',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyStreamersSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Live Streamers',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 15),
          SizedBox(
            height: 180,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    FontAwesomeIcons.twitch,
                    color: Colors.grey[400],
                    size: 32,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'No live streamers',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[400],
                    ),
                  ),
                  Text(
                    'Check back later for live streams',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStreamerCard(dynamic streamer) {
    final String name = streamer.name ?? 'Unknown Streamer';
    final String viewers = streamer.viewers ?? '0';
    final String game = streamer.game ?? 'Unknown Game';
    final String streamTitle = streamer.streamTitle ?? 'Live Stream';
    final String avatarUrl = streamer.avatarUrl ?? '';

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => StreamerDetailsScreen(
                  name: streamer.name,
                  viewers: streamer.viewers,
                  game: streamer.game,
                  streamTitle: streamer.streamTitle,
                  avatarUrl: streamer.avatarUrl,
                  startedTime: streamer.startedTime,
                  isFollowing: streamer.isFollowing,
                ),
          ),
        );
      },
      child: Container(
        width: 140,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 80,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                gradient: const LinearGradient(
                  colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
                ),
              ),
              child: Stack(
                children: [
                  if (avatarUrl.isNotEmpty)
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                      child: CachedNetworkImage(
                        imageUrl: avatarUrl,
                        height: 80,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder:
                            (context, url) => Container(
                              color: Colors.grey[300],
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            ),
                        errorWidget:
                            (context, url, error) => Container(
                              color: Colors.grey[300],
                              child: const Icon(Icons.person, size: 32),
                            ),
                      ),
                    )
                  else
                    const Center(
                      child: Icon(
                        Icons.person,
                        size: 32,
                        color: Colors.black54,
                      ),
                    ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'LIVE',
                        style: GoogleFonts.poppins(
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    game,
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      color: Theme.of(context).primaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    streamTitle,
                    style: GoogleFonts.poppins(
                      fontSize: 9,
                      color: Colors.grey[500],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '$viewers viewers',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      color: Colors.grey[400],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentWinnersSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Winners',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const RecentWinnersScreen(),
                    ),
                  );
                },
                child: Text(
                  'View All',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Consumer<WinnersProvider>(
            builder: (context, winnersProvider, child) {
              if (winnersProvider.isLoadingRecentWinners) {
                return _buildLoadingWinners();
              }

              if (winnersProvider.recentWinnersError != null) {
                return _buildWinnersError(winnersProvider.recentWinnersError!);
              }

              if (winnersProvider.recentWinners.isEmpty) {
                return _buildNoWinners();
              }

              return Column(
                children:
                    winnersProvider.recentWinners
                        .take(4) // Show only first 4 winners on home screen
                        .map(
                          (winner) => Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: _buildWinnerCardFromData(winner),
                          ),
                        )
                        .toList(),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWinners() {
    return SizedBox(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Theme.of(context).primaryColor),
            const SizedBox(height: 12),
            Text(
              'Loading recent winners...',
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[400]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWinnersError(String error) {
    return SizedBox(
      height: 120,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.exclamationTriangle,
              color: Colors.red,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              'Unable to load winners',
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[400]),
            ),
            const SizedBox(height: 4),
            Text(
              'Please try again later',
              style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoWinners() {
    return SizedBox(
      height: 120,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(FontAwesomeIcons.trophy, color: Colors.grey[400], size: 32),
            const SizedBox(height: 12),
            Text(
              'No winners yet',
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[400]),
            ),
            Text(
              'Complete tournaments to see winners',
              style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWinnerCardFromData(TournamentWinner winner) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          // Rank Badge
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getRankColor(winner.rank),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '#${winner.rank}',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // Winner Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  winner.username,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  winner.tournamentName,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  _formatDate(winner.completionDate),
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    color: Colors.grey[400],
                  ),
                ),
              ],
            ),
          ),
          // Prize
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'Rs. ${winner.prizeAmount ?? '0'}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              Text(
                'Prize',
                style: GoogleFonts.poppins(
                  fontSize: 10,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return Colors.amber; // Gold
      case 2:
        return Colors.grey[400]!; // Silver
      case 3:
        return Colors.brown; // Bronze
      default:
        return Colors.blue;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
