// Enhanced Match Join Screen with Smart Auto-Fill and Security Features
// Implements intelligent user data management, payment integration, and fraud prevention

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:wiggyz_app/features/tournament_provider.dart';
import 'package:wiggyz_app/features/tournament_models.dart';
import 'package:wiggyz_app/services/enhanced_security_service.dart';
import 'package:wiggyz_app/services/smart_wallet_manager.dart';

class EnhancedMatchJoinScreen extends StatefulWidget {
  final String matchId;

  const EnhancedMatchJoinScreen({super.key, required this.matchId});

  @override
  State<EnhancedMatchJoinScreen> createState() =>
      _EnhancedMatchJoinScreenState();
}

class _EnhancedMatchJoinScreenState extends State<EnhancedMatchJoinScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _gameIdController = TextEditingController();
  final _emailController = TextEditingController();

  bool _isLoading = false;
  bool _dataAutoFilled = false;
  bool _agreedToTerms = false;

  MatchEntryValidation? _entryValidation;
  PaymentMethod _selectedPaymentMethod = PaymentMethod.upi; // Default to UPI
  TopUpSuggestion? _topUpSuggestion;

  // Security and verification
  bool _gameIdVerified = false;
  FraudRiskScore? _fraudRisk;

  @override
  void initState() {
    super.initState();
    _initializeMatchJoin();
  }

  Future<void> _initializeMatchJoin() async {
    setState(() => _isLoading = true);

    try {
      // Load match details
      final provider = Provider.of<TournamentProvider>(context, listen: false);
      await provider.fetchStandaloneMatchById(widget.matchId);

      // Auto-fill user data
      await _autoFillUserData();

      // Validate entry eligibility
      await _validateMatchEntry();

      // Security check
      await _performSecurityCheck();
    } catch (e) {
      debugPrint('Error initializing match join: $e');
      _showErrorSnackBar('Failed to load match information');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _autoFillUserData() async {
    try {
      // Get user ID from auth context (implementation would get from auth service)
      const userId = 'current_user_id'; // Replace with actual user ID

      // Fetch auto-fill data
      final autoFillData = await SmartProfileManager.getAutoFillData(userId);

      setState(() {
        _usernameController.text = autoFillData.username ?? '';
        _emailController.text = autoFillData.email ?? '';
        _selectedPaymentMethod =
            autoFillData.preferredPaymentMethod ?? PaymentMethod.upi; // Default to UPI

        // Auto-fill game ID if available for this game type
        final match =
            Provider.of<TournamentProvider>(
              context,
              listen: false,
            ).selectedMatch;
        if (match?.game?.name != null) {
          final gameId =
              autoFillData.gameIds
                  ?.where((gid) => gid.gameType == match!.game!.name)
                  .firstOrNull
                  ?.gameId;

          if (gameId != null) {
            _gameIdController.text = gameId;
            _gameIdVerified =
                autoFillData.gameIds!
                    .firstWhere((gid) => gid.gameId == gameId)
                    .verified;
          }
        }

        _dataAutoFilled = true;
      });
    } catch (e) {
      debugPrint('Error auto-filling user data: $e');
    }
  }

  Future<void> _validateMatchEntry() async {
    try {
      const userId = 'current_user_id'; // Replace with actual user ID
      final match =
          Provider.of<TournamentProvider>(context, listen: false).selectedMatch;

      if (match?.entryFee != null) {
        final validation = await SmartWalletManager.validateMatchEntry(
          userId,
          widget.matchId,
          match!.entryFee!,
        );

        setState(() {
          _entryValidation = validation;
          if (!validation.canJoin && validation.topUpSuggestion != null) {
            _topUpSuggestion = validation.topUpSuggestion;
          }
        });
      }
    } catch (e) {
      debugPrint('Error validating match entry: $e');
    }
  }

  Future<void> _performSecurityCheck() async {
    try {
      const userId = 'current_user_id'; // Replace with actual user ID
      final match =
          Provider.of<TournamentProvider>(context, listen: false).selectedMatch;

      if (match?.entryFee != null) {
        final fraudRisk = await EnhancedSecurityService.calculateFraudRisk(
          userId,
          widget.matchId,
          match!.entryFee!,
        );

        setState(() => _fraudRisk = fraudRisk);

        if (fraudRisk.requiresReview) {
          _showSecurityWarning(fraudRisk);
        }
      }
    } catch (e) {
      debugPrint('Error performing security check: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _buildBody(),
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'Join Match',
        style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
      ),
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      elevation: 0,
      leading: IconButton(
        onPressed: () async {
          try {
            // First, try using the standard Navigator pop
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
              return;
            }

            // If Navigator can't pop, try GoRouter
            final router = GoRouter.of(context);
            if (router.canPop()) {
              router.pop();
              return;
            }

            // Final fallback - navigate to a safe route
            if (mounted) {
              await Future.delayed(const Duration(milliseconds: 100));
              if (mounted) {
                context.go('/matches/${widget.matchId}');
              }
            }
          } catch (e) {
            // Ultimate fallback - go home
            debugPrint('Navigation error: $e');
            if (mounted) {
              context.go('/');
            }
          }
        },
        icon: const Icon(Icons.arrow_back),
      ),
      actions: [
        if (_dataAutoFilled)
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.auto_awesome, size: 16, color: Colors.green),
                const SizedBox(width: 4),
                Text(
                  'Auto-filled',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.green,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildBody() {
    return Consumer<TournamentProvider>(
      builder: (context, provider, _) {
        final match = provider.selectedMatch;
        if (match == null) {
          return const Center(child: Text('Match not found'));
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildMatchSummaryCard(match),
                const SizedBox(height: 20),

                _buildSecurityStatusCard(),
                const SizedBox(height: 20),

                _buildUserInfoSection(),
                const SizedBox(height: 20),

                _buildGameIdSection(match),
                const SizedBox(height: 20),

                _buildPaymentSection(match),
                const SizedBox(height: 20),

                _buildTermsAndConditions(),
                const SizedBox(height: 100), // Space for bottom action bar
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMatchSummaryCard(Match match) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.sports_esports, color: Colors.white, size: 24),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  match.game?.name ?? 'Game Match',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          Row(
            children: [
              _buildMatchInfoChip(
                'Entry Fee',
                match.entryFee != null
                    ? '\$${match.entryFee!.toStringAsFixed(2)}'
                    : 'Free',
                Icons.attach_money,
              ),
              const SizedBox(width: 12),
              _buildMatchInfoChip(
                'Players',
                '${match.participants.length}/${match.maxParticipants ?? '∞'}',
                Icons.people,
              ),
            ],
          ),

          if (match.matchTime != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.schedule, color: Colors.white70, size: 16),
                const SizedBox(width: 4),
                Text(
                  'Starts: ${_formatMatchTime(match.matchTime!)}',
                  style: GoogleFonts.poppins(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMatchInfoChip(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white, size: 14),
          const SizedBox(width: 4),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.poppins(color: Colors.white70, fontSize: 10),
              ),
              Text(
                value,
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityStatusCard() {
    if (_fraudRisk == null) return const SizedBox.shrink();

    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (_fraudRisk!.riskLevel) {
      case RiskLevel.minimal:
      case RiskLevel.low:
        statusColor = Colors.green;
        statusIcon = Icons.security;
        statusText = 'Secure Transaction';
        break;
      case RiskLevel.medium:
        statusColor = Colors.orange;
        statusIcon = Icons.warning;
        statusText = 'Additional Verification Required';
        break;
      case RiskLevel.high:
      case RiskLevel.critical:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = 'High Risk - Manual Review Required';
        break;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  statusText,
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    color: statusColor,
                  ),
                ),
                if (_fraudRisk!.riskFactors.isNotEmpty)
                  Text(
                    'Security Score: ${100 - _fraudRisk!.score}/100',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: statusColor.withOpacity(0.8),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfoSection() {
    return _buildSectionCard(
      title: 'Player Information',
      child: Column(
        children: [
          TextFormField(
            controller: _usernameController,
            decoration: InputDecoration(
              labelText: 'Username',
              prefixIcon: const Icon(Icons.person),
              suffixIcon:
                  _dataAutoFilled
                      ? const Icon(Icons.auto_awesome, color: Colors.green)
                      : null,
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Username is required';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _emailController,
            decoration: InputDecoration(
              labelText: 'Email',
              prefixIcon: const Icon(Icons.email),
              suffixIcon:
                  _dataAutoFilled
                      ? const Icon(Icons.auto_awesome, color: Colors.green)
                      : null,
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Email is required';
              }
              if (!RegExp(
                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
              ).hasMatch(value)) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildGameIdSection(Match match) {
    return _buildSectionCard(
      title: 'Game Information',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFormField(
            controller: _gameIdController,
            decoration: InputDecoration(
              labelText: '${match.game?.name ?? 'Game'} ID',
              prefixIcon: const Icon(Icons.games),
              suffixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_gameIdVerified)
                    const Icon(Icons.verified, color: Colors.green)
                  else if (_gameIdController.text.isNotEmpty)
                    IconButton(
                      onPressed: _verifyGameId,
                      icon: const Icon(Icons.check_circle_outline),
                    ),
                  if (_dataAutoFilled)
                    const Icon(Icons.auto_awesome, color: Colors.green),
                ],
              ),
              helperText:
                  _gameIdVerified
                      ? 'Game ID verified ✓'
                      : 'Enter your game ID for verification',
              helperStyle: TextStyle(
                color: _gameIdVerified ? Colors.green : null,
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Game ID is required';
              }
              return null;
            },
            onChanged: (value) {
              setState(() => _gameIdVerified = false);
            },
          ),
          const SizedBox(height: 8),
          Text(
            'Your game ID will be saved for future matches',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Theme.of(context).colorScheme.secondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSection(Match match) {
    if (match.entryFee == null || match.entryFee == 0) {
      return _buildSectionCard(
        title: 'Entry Fee',
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              const Icon(Icons.celebration, color: Colors.green),
              const SizedBox(width: 12),
              Text(
                'Free Entry!',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return _buildSectionCard(
      title: 'Payment Method',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Entry fee display
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.attach_money, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Entry Fee: \$${match.entryFee!.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Payment validation status
          if (_entryValidation != null) ...[
            _buildPaymentValidationStatus(),
            const SizedBox(height: 16),
          ],

          // Payment method selection
          if (_entryValidation?.canJoin == true)
            _buildPaymentMethodSelector()
          else if (_topUpSuggestion != null)
            _buildTopUpSection(),
        ],
      ),
    );
  }

  Widget _buildPaymentValidationStatus() {
    if (_entryValidation == null) return const SizedBox.shrink();

    final canJoin = _entryValidation!.canJoin;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            canJoin
                ? Colors.green.withOpacity(0.1)
                : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              canJoin
                  ? Colors.green.withOpacity(0.3)
                  : Colors.orange.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            canJoin ? Icons.check_circle : Icons.warning,
            color: canJoin ? Colors.green : Colors.orange,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _entryValidation!.message,
              style: GoogleFonts.poppins(
                color: canJoin ? Colors.green : Colors.orange,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Payment Method',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600, fontSize: 14),
        ),
        const SizedBox(height: 8),

        RadioListTile<PaymentMethod>(
          title: const Text('Wallet Balance'),
          subtitle: const Text('Instant payment'),
          value: PaymentMethod.wallet,
          groupValue: _selectedPaymentMethod,
          onChanged: (value) {
            setState(() => _selectedPaymentMethod = value!);
          },
        ),

        RadioListTile<PaymentMethod>(
          title: const Text('Credit/Debit Card'),
          subtitle: const Text('Secure payment gateway'),
          value: PaymentMethod.card,
          groupValue: _selectedPaymentMethod,
          onChanged: (value) {
            setState(() => _selectedPaymentMethod = value!);
          },
        ),

        RadioListTile<PaymentMethod>(
          title: const Text('UPI'),
          subtitle: const Text('Quick UPI payment'),
          value: PaymentMethod.upi,
          groupValue: _selectedPaymentMethod,
          onChanged: (value) {
            setState(() => _selectedPaymentMethod = value!);
          },
        ),
      ],
    );
  }

  Widget _buildTopUpSection() {
    if (_topUpSuggestion == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              const Icon(Icons.account_balance_wallet, color: Colors.red),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Insufficient Balance: \$${_topUpSuggestion!.shortfall.toStringAsFixed(2)} needed',
                  style: GoogleFonts.poppins(
                    color: Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        Text(
          'Top-up Suggestions',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600, fontSize: 14),
        ),

        const SizedBox(height: 8),

        ...(_topUpSuggestion!.suggestions
            .take(3)
            .map((suggestion) => _buildTopUpSuggestionTile(suggestion))),

        const SizedBox(height: 16),

        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _handleTopUp,
            icon: const Icon(Icons.add),
            label: const Text('TOP UP WALLET'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTopUpSuggestionTile(TopUpAmount suggestion) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '\$${suggestion.amount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                Text(
                  suggestion.reason,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
              ],
            ),
          ),
          if (suggestion.discount > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${(suggestion.discount * 100).toInt()}% OFF',
                style: GoogleFonts.poppins(
                  fontSize: 10,
                  color: Colors.green,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTermsAndConditions() {
    return _buildSectionCard(
      title: 'Terms & Conditions',
      child: CheckboxListTile(
        value: _agreedToTerms,
        onChanged: (value) {
          setState(() => _agreedToTerms = value ?? false);
        },
        title: RichText(
          text: TextSpan(
            style: GoogleFonts.poppins(
              color: Theme.of(context).colorScheme.onSurface,
              fontSize: 14,
            ),
            children: [
              const TextSpan(text: 'I agree to the '),
              TextSpan(
                text: 'Terms of Service',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
              const TextSpan(text: ' and '),
              TextSpan(
                text: 'Privacy Policy',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
            ],
          ),
        ),
        controlAffinity: ListTileControlAffinity.leading,
        contentPadding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildSectionCard({required String title, required Widget child}) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildBottomActionBar() {
    final needsTopUp = _topUpSuggestion != null;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _canProceed() ? _handleJoinMatch : null,
            icon: Icon(needsTopUp ? Icons.add : Icons.sports_esports),
            label: Text(
              needsTopUp ? 'TOP UP & JOIN' : 'JOIN MATCH',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor:
                  needsTopUp ? Colors.orange : Theme.of(context).primaryColor,
            ),
          ),
        ),
      ),
    );
  }

  bool _canProceed() {
    return _formKey.currentState?.validate() == true &&
        _agreedToTerms &&
        _gameIdController.text.isNotEmpty &&
        (_fraudRisk?.riskLevel != RiskLevel.critical);
  }

  // Event handlers
  Future<void> _verifyGameId() async {
    if (_gameIdController.text.isEmpty) return;

    setState(() => _isLoading = true);

    try {
      // Implementation would verify game ID
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      setState(() => _gameIdVerified = true); // Save game ID for future use
      const userId = 'current_user_id'; // Replace with actual user ID
      final match =
          Provider.of<TournamentProvider>(context, listen: false).selectedMatch;
      if (match?.game?.name != null) {
        await SmartProfileManager.saveGameId(
          userId,
          match!.game!.name,
          _gameIdController.text,
        );
      }

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Game ID verified successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      debugPrint('Error verifying game ID: $e');
      _showErrorSnackBar('Failed to verify game ID');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handleTopUp() async {
    // Navigate to top-up screen or show top-up dialog
    // Implementation would handle wallet top-up flow
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Top-up feature coming soon!')),
    );
  }

  Future<void> _handleJoinMatch() async {
    if (!_formKey.currentState!.validate() || !_agreedToTerms) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      const userId = 'current_user_id'; // Replace with actual user ID
      final match =
          Provider.of<TournamentProvider>(context, listen: false).selectedMatch;

      if (match?.entryFee != null && match!.entryFee! > 0) {
        // Process payment
        final paymentRequest = PaymentRequest(
          amount: match.entryFee!,
          method: _selectedPaymentMethod,
        );

        final paymentResult = await SmartWalletManager.processMatchEntry(
          userId,
          widget.matchId,
          paymentRequest,
        );

        if (!paymentResult.success) {
          _showErrorSnackBar(paymentResult.error ?? 'Payment failed');
          return;
        }
      } // Join match
      final provider = Provider.of<TournamentProvider>(context, listen: false);
      await provider.joinMatch(widget.matchId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Successfully joined the match!'),
            backgroundColor: Colors.green,
          ),
        );

        context.go('/matches/${widget.matchId}');
      }
    } catch (e) {
      debugPrint('Error joining match: $e');
      _showErrorSnackBar('Failed to join match');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showSecurityWarning(FraudRiskScore fraudRisk) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Security Notice'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your transaction requires additional verification due to:',
                ),
                const SizedBox(height: 8),
                ...fraudRisk.riskFactors.map(
                  (factor) => Padding(
                    padding: const EdgeInsets.only(left: 8, bottom: 4),
                    child: Text('• $factor'),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Understood'),
              ),
            ],
          ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  String _formatMatchTime(DateTime matchTime) {
    final now = DateTime.now();
    final difference = matchTime.difference(now);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ${difference.inHours % 24}h';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ${difference.inMinutes % 60}m';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'Starting soon';
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _gameIdController.dispose();
    _emailController.dispose();
    super.dispose();
  }
}

// Supporting classes for smart profile management
class SmartProfileManager {
  static Future<MatchJoinData> getAutoFillData(String userId) async {
    // Implementation would fetch user profile and game data
    return MatchJoinData(
      username: 'player123',
      email: '<EMAIL>',
      preferredPaymentMethod: PaymentMethod.wallet,
      gameIds: [
        GameIdData(
          gameType: 'PUBG Mobile',
          gameId: '123456789',
          lastUsed: DateTime.now().subtract(const Duration(days: 1)),
          verified: true,
        ),
      ],
    );
  }

  static Future<void> saveGameId(
    String userId,
    String gameType,
    String gameId,
  ) async {
    // Implementation would save game ID to user profile
    debugPrint('Saving game ID: $gameId for $gameType');
  }
}

class MatchJoinData {
  final String? username;
  final String? email;
  final PaymentMethod? preferredPaymentMethod;
  final List<GameIdData>? gameIds;

  MatchJoinData({
    this.username,
    this.email,
    this.preferredPaymentMethod,
    this.gameIds,
  });
}

class GameIdData {
  final String gameType;
  final String gameId;
  final DateTime lastUsed;
  final bool verified;

  GameIdData({
    required this.gameType,
    required this.gameId,
    required this.lastUsed,
    required this.verified,
  });
}
