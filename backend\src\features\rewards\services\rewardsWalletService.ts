/**
 * Service for integrating between rewards and wallet systems
 * Handles all diamond transactions for rewards
 */
import { v4 as uuidv4 } from 'uuid';
import { supabase } from '../../../config/supabase';
import { walletService } from '../../wallet/services/walletService';
import { logger } from '../../../utils/logger';

export enum RewardTransactionType {
  DAILY_REWARD = 'daily_reward',
  ACHIEVEMENT = 'achievement',
  LOYALTY_BONUS = 'loyalty_bonus',
  REFERRAL = 'referral',
  TOURNAMENT_PRIZE = 'tournament_prize',
  SPECIAL_OFFER = 'special_offer',
  MANUAL_ADJUSTMENT = 'manual_adjustment'
}

/**
 * Type definition for diamond result
 */
interface DiamondResult {
  success: boolean;
  diamonds: number;
  new_balance: number;
  message?: string;
  error?: string;
  transaction_id?: string;
}

/**
 * Type definition for points result
 */
interface PointsResult {
  success: boolean;
  points: number;
  message?: string;
  error?: string;
}

/**
 * Type definition for claim result
 */
interface ClaimResult {
  success: boolean;
  message?: string;
  error?: string;
  reward?: {
    id: string;
    title: string;
    diamond_value: number;
    points: number;
    type: string;
  };
  diamonds?: number;
  points?: number;
  new_balance?: number;
}

export const rewardsWalletService = {
  /**
   * Process a diamond reward for a user
   * @param userId - User ID
   * @param rewardId - Reward ID
   * @param amount - Diamond amount (positive for credit, negative for debit)
   * @param transactionType - Type of reward transaction
   * @param description - Description of the reward
   * @param idempotencyKey - Optional idempotency key to prevent duplicate transactions
   * @returns Transaction result
   */
  /**
   * Process a diamond reward for a user
   * @param userId - User ID
   * @param rewardId - Reward ID
   * @param amount - Diamond amount (positive for credit, negative for debit)
   * @param transactionType - Type of reward transaction
   * @param description - Description of the reward
   * @param idempotencyKey - Optional idempotency key to prevent duplicate transactions
   * @returns Transaction result with standardized fields
   */
  async processDiamondReward(
    userId: string,
    rewardId: string | null,
    amount: number,
    transactionType: RewardTransactionType,
    description: string,
    idempotencyKey?: string
  ): Promise<DiamondResult> {
    try {
      // Generate idempotency key if not provided
      const finalIdempotencyKey = idempotencyKey || `reward-${uuidv4()}`;
      
      // Check if reward is already claimed (if rewardId is provided)
      if (rewardId) {
        const { data: existingClaim, error: claimError } = await supabase
          .from('user_rewards')
          .select('id')
          .eq('user_id', userId)
          .eq('reward_id', rewardId)
          .maybeSingle();
        
        if (claimError) {
          throw new Error(`Error checking claim status: ${claimError.message}`);
        }
        
        if (existingClaim) {
          // Reward already claimed
          return {
            success: false,
            message: 'Reward already claimed',
            error: 'ALREADY_CLAIMED',
            diamonds: 0,
            new_balance: 0
          };
        }
      }
      
      // Record the reward claim if rewardId is provided
      if (rewardId) {
        const { error: claimError } = await supabase
          .from('user_rewards')
          .insert({
            user_id: userId,
            reward_id: rewardId,
            claimed_at: new Date().toISOString()
          });
        
        if (claimError) {
          throw new Error(`Error recording claim: ${claimError.message}`);
        }
      }
      
      // Process the diamond transaction
      const transactionResult = await walletService.addDiamonds(
        userId,
        amount,
        transactionType,
        description,
        finalIdempotencyKey
      );
      
      // If the transaction was idempotent (already processed), handle gracefully
      if (transactionResult.idempotent) {
        // Get current wallet balance
        const { data: wallet, error: walletError } = await supabase
          .from('wallets')
          .select('balance')
          .eq('user_id', userId)
          .single();
          
        if (walletError) {
          logger.error(`Error getting wallet balance: ${walletError.message}`);
          // Continue with a default case
          return {
            success: true,
            diamonds: amount,
            new_balance: 0,  // We don't know the balance but the operation was successful
            message: 'Transaction was already processed',
            transaction_id: 'idempotent'
          };
        }
        
        return {
          success: true,
          diamonds: amount,
          new_balance: wallet.balance || 0,
          message: 'Transaction was already processed',
          transaction_id: 'idempotent'
        };
      }
      
      // Record a reward transaction
      const { error: txError } = await supabase
        .from('reward_transactions')
        .insert({
          user_id: userId,
          reward_id: rewardId,
          points: 0, // Points are handled separately from diamonds
          action: transactionType,
          description,
          metadata: {
            diamond_amount: amount,
            idempotency_key: finalIdempotencyKey
          },
          created_at: new Date().toISOString()
        });
      
      if (txError) {
        logger.error(`Error recording reward transaction: ${txError.message}`);
        // Continue execution as the wallet transaction was already processed
      }
      
      return {
        success: true,
        diamonds: amount,
        transaction_id: transactionResult.transactionId,
        new_balance: (() => {
          const balance = transactionResult.balance;
          if (typeof balance === 'object' && balance && 'balance' in balance) {
            return (balance as any).balance;
          }
          return typeof balance === 'number' ? balance : 0;
        })()
      };
    } catch (error) {
      logger.error(`Error processing diamond reward: ${error instanceof Error ? error.message : String(error)}`);
      
      // Return a standardized error response rather than throwing
      return {
        success: false,
        diamonds: 0,
        new_balance: 0,
        message: error instanceof Error ? error.message : 'Unknown error processing diamonds',
        error: 'PROCESSING_ERROR'
      };
    }
  },

  /**
   * Process loyalty points for a user
   * @param userId - User ID
   * @param points - Number of points to award
   * @param transactionType - Type of reward transaction
   * @param description - Description of the points award
   * @param idempotencyKey - Optional idempotency key
   * @returns Points processing result
   */
  async processLoyaltyPoints(
    userId: string,
    points: number,
    transactionType: RewardTransactionType,
    description: string,
    idempotencyKey?: string
  ): Promise<PointsResult> {
    try {
      // Generate idempotency key if not provided
      const finalIdempotencyKey = idempotencyKey || `points-${uuidv4()}`;
      
      // Check for existing transaction with this idempotency key
      if (finalIdempotencyKey) {
        const { data: existingTx, error: txError } = await supabase
          .from('reward_transactions')
          .select('id')
          .eq('metadata->idempotency_key', finalIdempotencyKey)
          .maybeSingle();
        
        if (!txError && existingTx) {
          return { 
            success: true, 
            points: 0,
            message: 'Points already processed' 
          };
        }
      }
      
      // Record the points transaction
      const { error: txError } = await supabase
        .from('reward_transactions')
        .insert({
          user_id: userId,
          reward_id: null,
          points: points,
          action: transactionType,
          description,
          metadata: {
            idempotency_key: finalIdempotencyKey
          },
          created_at: new Date().toISOString()
        });
      
      if (txError) {
        logger.error(`Error recording points transaction: ${txError.message}`);
        return {
          success: false,
          points: 0,
          message: `Failed to record points transaction: ${txError.message}`,
          error: 'TRANSACTION_ERROR'
        };
      }
      
      // Update user's loyalty points in their profile
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({ 
          loyalty_points: supabase.rpc('increment', { amount: points }),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);
      
      if (updateError) {
        logger.error(`Error updating loyalty points: ${updateError.message}`);
        return {
          success: false,
          points: 0,
          message: `Failed to update loyalty points: ${updateError.message}`,
          error: 'UPDATE_ERROR'
        };
      }
      
      return {
        success: true,
        points: points
      };
    } catch (error) {
      logger.error(`Error processing loyalty points: ${error instanceof Error ? error.message : String(error)}`);
      
      return {
        success: false,
        points: 0,
        message: error instanceof Error ? error.message : 'Unknown error processing points',
        error: 'PROCESSING_ERROR'
      };
    }
  },

  /**
   * Process claiming a reward
   * @param userId - User ID
   * @param rewardId - Reward ID to claim
   * @param idempotencyKey - Optional idempotency key
   * @returns Claim result
   */
  async claimReward(
    userId: string,
    rewardId: string,
    idempotencyKey?: string
  ): Promise<ClaimResult> {
    try {
      // Generate a transaction-specific idempotency key if not provided
      const finalIdempotencyKey = idempotencyKey || `reward-claim-${userId}-${rewardId}-${Date.now()}`;
      
      // Check for existing claims with this idempotency key to ensure true idempotency
      if (finalIdempotencyKey) {
        const { data: existingClaim, error: idempotencyError } = await supabase
          .from('reward_transactions')
          .select('id')
          .eq('metadata->idempotency_key', finalIdempotencyKey)
          .maybeSingle();
          
        if (idempotencyError) {
          logger.warn(`Error checking idempotency: ${idempotencyError.message}`);
          // Continue execution as this is a non-critical check
        } else if (existingClaim) {
          // Already processed this exact claim
          return {
            success: false,
            message: 'Reward already claimed with this request',
            error: 'DUPLICATE_REQUEST'
          };
        }
      }
      
      // Get reward details
      const { data: reward, error: rewardError } = await supabase
        .from('rewards')
        .select(`
          id,
          title,
          description,
          diamond_value,
          points,
          limit_per_user,
          reward_types:type_id (
            name,
            code
          )
        `)
        .eq('id', rewardId)
        .single();
      
      if (rewardError) {
        throw new Error(`Error fetching reward: ${rewardError.message}`);
      }
      
      if (!reward) {
        return {
          success: false,
          message: 'Reward not found',
          error: 'NOT_FOUND'
        };
      }
      
      // Check if reward is active
      const { data: activeCheck, error: activeError } = await supabase
        .from('rewards')
        .select('is_active, start_date, end_date')
        .eq('id', rewardId)
        .single();
      
      if (activeError) {
        throw new Error(`Error checking reward status: ${activeError.message}`);
      }
      
      const now = new Date();
      const isActive = activeCheck.is_active &&
        (!activeCheck.start_date || new Date(activeCheck.start_date) <= now) &&
        (!activeCheck.end_date || new Date(activeCheck.end_date) >= now);
      
      if (!isActive) {
        return {
          success: false,
          message: 'Reward is not active or has expired',
          error: 'INACTIVE_REWARD'
        };
      }
      
      // Check user limits if applicable
      if (reward.limit_per_user) {
        const { count, error: limitError } = await supabase
          .from('user_rewards')
          .select('id', { count: 'exact' })
          .eq('user_id', userId)
          .eq('reward_id', rewardId);
          
        if (limitError) {
          logger.error(`Error checking user limits: ${limitError.message}`);
          // Continue as this is a non-critical check
        } else if (count && count >= reward.limit_per_user) {
          return {
            success: false,
            message: `You have reached the limit of ${reward.limit_per_user} claims for this reward`,
            error: 'LIMIT_REACHED'
          };
        }
      }
      
      // Determine the transaction type based on reward type
      let transactionType = RewardTransactionType.SPECIAL_OFFER;
      if (reward.reward_types && reward.reward_types.code) {
        switch (reward.reward_types.code) {
          case 'daily':
            transactionType = RewardTransactionType.DAILY_REWARD;
            break;
          case 'achievement':
            transactionType = RewardTransactionType.ACHIEVEMENT;
            break;
          case 'loyalty':
            transactionType = RewardTransactionType.LOYALTY_BONUS;
            break;
          case 'referral':
            transactionType = RewardTransactionType.REFERRAL;
            break;
          case 'tournament':
            transactionType = RewardTransactionType.TOURNAMENT_PRIZE;
            break;
          // Default already set to SPECIAL_OFFER
        }
      }
      
      // Start a database transaction to ensure consistency
      const { error: txError } = await supabase.rpc('begin_transaction');
      if (txError) {
        logger.error(`Error starting transaction: ${txError.message}`);
        // Fallback to proceeding without transaction if not supported
      }
      
      try {
        // Process the diamond reward if there's a diamond value
        let diamondResult: {
          success: boolean;
          diamonds: number;
          new_balance: number;
          message?: string;
          error?: string;
        } = {
          success: true,
          diamonds: 0,
          new_balance: 0
        };
        
        if (reward.diamond_value && reward.diamond_value > 0) {
          diamondResult = await this.processDiamondReward(
            userId,
            rewardId,
            reward.diamond_value,
            transactionType,
            `Claimed: ${reward.title}`,
            finalIdempotencyKey
          );
          
          if (!diamondResult.success) {
            // Rollback transaction if diamond processing failed
            await supabase.rpc('rollback_transaction');
            return {
              success: false,
              message: diamondResult.message || 'Failed to process diamonds',
              error: diamondResult.error || 'DIAMOND_ERROR',
              reward: {
                id: reward.id,
                title: reward.title,
                diamond_value: reward.diamond_value || 0,
                points: reward.points || 0,
                type: reward.reward_types?.name || 'Unknown'
              },
              diamonds: 0,
              points: 0,
              new_balance: 0
            };
          }
        }
        
        // Process points reward if applicable
        let pointsResult: PointsResult | null = null;
        if (reward.points && reward.points > 0) {
          // Record points in reward_transactions table
          const { error: pointsError } = await supabase
            .from('reward_transactions')
            .insert({
              user_id: userId,
              reward_id: rewardId,
              points: reward.points,
              action: transactionType,
              description: `Points for: ${reward.title}`,
              metadata: {
                idempotency_key: finalIdempotencyKey,
                diamond_amount: reward.diamond_value || 0
              },
              created_at: new Date().toISOString()
            });
            
          if (pointsError) {
            logger.error(`Error recording points transaction: ${pointsError.message}`);
            await supabase.rpc('rollback_transaction');
            return {
              success: false,
              message: 'Failed to process points reward',
              error: 'POINTS_ERROR',
              diamonds: 0,
              points: 0,
              new_balance: 0
            };
          }
          
          // Update user's loyalty points in their profile
          const { error: updatePointsError } = await supabase
            .from('user_profiles')
            .update({ 
              loyalty_points: supabase.rpc('increment', { amount: reward.points }),
              updated_at: new Date().toISOString()
            })
            .eq('user_id', userId);
            
          if (updatePointsError) {
            logger.error(`Error updating loyalty points: ${updatePointsError.message}`);
            // Continue as this is not critical for the reward claim
          }
          
          pointsResult = {
            success: true,
            points: reward.points
          };
        }
        
        // Commit the transaction
        await supabase.rpc('commit_transaction');
        
        return {
          success: true,
          reward: {
            id: reward.id,
            title: reward.title,
            diamond_value: reward.diamond_value || 0,
            points: reward.points || 0,
            type: reward.reward_types?.name || 'Unknown'
          },
          diamonds: diamondResult.diamonds,
          points: pointsResult?.points || 0,
          new_balance: diamondResult.new_balance
        };
      } catch (transactionError) {
        // Rollback transaction on error
        await supabase.rpc('rollback_transaction');
        throw transactionError;
      }
    } catch (error) {
      logger.error(`Error claiming reward: ${error instanceof Error ? error.message : String(error)}`);
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
        error: 'CLAIM_ERROR',
        diamonds: 0,
        points: 0,
        new_balance: 0
      };
    }
  }
};

export default rewardsWalletService;
